name: Setup Test Environment
description: Sets up Node.js, Yarn, SSH keys, and configures test environment

inputs:
  backend_environment:
    description: 'Backend environment (dev/dynamic)'
    required: false
    default: 'dev'
  dynamic_backend_url:
    description: 'Dynamic backend URL'
    required: false
    default: ''
  qase_api_token:
    description: 'QASE API token'
    required: true
  qase_project_code:
    description: 'QASE project code'
    required: true
  qase_run_id:
    description: 'QASE run ID'
    required: true
  qase_mode:
    description: 'QASE mode'
    required: false
    default: 'testops'
  qase_run_complete:
    description: 'Whether to complete QASE run after tests (true/false)'
    required: false
    default: 'true'
  runner_type:
    description: 'Runner type (farm/standalone)'
    required: false
    default: 'farm'
  mistral_api_key:
    description: 'Mistral API key'
    required: true
  openai_api_key:
    description: 'OpenAI API key'
    required: true
  frontegg_dev_client_id:
    description: 'Frontegg dev client ID'
    required: true
  frontegg_dev_api_key:
    description: 'Frontegg dev API key'
    required: true
  sa_email:
    description: 'Service account email'
    required: true
  sa_password:
    description: 'Service account password'
    required: true
  syngrisi_api_key:
    description: 'Syngrisi API key'
    required: true
  gh_runner_ssh_private_key:
    description: 'GitHub runner SSH private key'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Debug inputs
      shell: bash
      run: |
        echo "=== Debug Inputs ==="
        echo "backend_environment: ${{ inputs.backend_environment }}"
        echo "dynamic_backend_url: ${{ inputs.dynamic_backend_url }}"
        echo "qase_api_token: [MASKED]"
        echo "qase_project_code: ${{ inputs.qase_project_code }}"
        echo "qase_run_id: ${{ inputs.qase_run_id }}"
        echo "qase_mode: ${{ inputs.qase_mode }}"
        echo "runner_type: ${{ inputs.runner_type }}"
        echo "mistral_api_key: [MASKED]"
        echo "openai_api_key: [MASKED]"
        echo "frontegg_dev_client_id: ${{ inputs.frontegg_dev_client_id }}"
        echo "frontegg_dev_api_key: [MASKED]"
        echo "sa_email: ${{ inputs.sa_email }}"
        echo "sa_password: [MASKED]"
        echo "syngrisi_api_key: [MASKED]"
        echo "gh_runner_ssh_private_key: [MASKED]"
        echo "==================="

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22.4.0'
        cache: 'yarn'

    - name: Install Yarn
      shell: bash
      run: npm install --global yarn

    - name: Configure environment
      shell: bash
      run: |
        echo "MISTRAL_API_KEY=${{ inputs.mistral_api_key }}" > test/taf-ms/.env
        echo "OPENAI_API_KEY=${{ inputs.openai_api_key }}" >> test/taf-ms/.env
        echo "FGAI_TEST_AUTH_STRING=vscode://zencoderai.zencoder/25f43bc3cec0c64e9977d183733a3529?windowId%3D10=&authToken=STUB&refreshToken=STUB" >> test/taf-ms/.env

        # Add QASE configuration for tests
        echo "QASE_API_TOKEN=${{ inputs.qase_api_token }}" >> test/taf-ms/.env
        echo "QASE_PROJECT_CODE=${{ inputs.qase_project_code }}" >> test/taf-ms/.env
        echo "QASE_RUN_ID=${{ inputs.qase_run_id }}" >> test/taf-ms/.env
        echo "QASE_MODE=${{ inputs.qase_mode }}" >> test/taf-ms/.env
        echo "QASE_TESTOPS_RUN_COMPLETE=${{ inputs.qase_run_complete }}" >> test/taf-ms/.env

        echo "TEST_CONF=${{ inputs.backend_environment }}" >> test/taf-ms/.env
        if [[ "${{ inputs.backend_environment }}" == "dynamic" && "${{ inputs.dynamic_backend_url }}" != "" ]]; then
          echo "FGAI_TEST_DYNAMIC_BASE_URL=${{ inputs.dynamic_backend_url }}" >> test/taf-ms/.env
        fi
        echo "LOG_LEVEL=debug" >> test/taf-ms/.env

        # Vendor token
        url="https://api.us.frontegg.com/auth/vendor/"
        headers='-H "accept: application/json" -H "content-type: application/json"'
        body=$(jq -n \
          --arg clientId "${{ inputs.frontegg_dev_client_id }}" \
          --arg secret "${{ inputs.frontegg_dev_api_key }}" \
          '{clientId: $clientId, secret: $secret}')
        response=$(curl -s -X POST "$url" \
          -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          -d "$body")
        vendor_token=$(echo "$response" | jq -r ".token")

        # User token
        reportUrl="https://api.us.frontegg.com/identity/resources/auth/v1/user"
        response=$(curl -s -X POST "$reportUrl" \
          -H "accept: */*" \
          -H "accept-language: en-GB,en-US;q=0.9,en;q=0.8" \
          -H "authorization: Bearer $vendor_token" \
          -H "content-type: application/json" \
          -H "dnt: 1" \
          -H "frontegg-vendor-host: dev.fe.zencoder.ai" \
          -H "origin: http://localhost:3000" \
          -H "priority: u=1, i" \
          -H "referer: http://localhost:3000/" \
          -H "sec-ch-ua: \"Not?A_Brand\";v=\"99\", \"Chromium\";v=\"130\"" \
          -H "sec-ch-ua-mobile: ?0" \
          -H "sec-ch-ua-platform: \"macOS\"" \
          -H "sec-fetch-dest: empty" \
          -H "sec-fetch-mode: cors" \
          -H "sec-fetch-site: cross-site" \
          -H "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36" \
          --data-raw "{\"email\":\"${{ inputs.sa_email }}\",\"password\":\"${{ inputs.sa_password }}\"}")

        user_token=$(echo "$response" | jq -r ".accessToken")
        refresh_token=$(echo "$response" | jq -r ".refreshToken")

        echo "FGAI_TEST_AUTH_STRING_DEV=vscode://zencoderai.zencoder/25f43bc3cec0c64e9977d183733a3529?windowId%3D10%26&authToken=$user_token&refreshToken=$refresh_token" >> test/taf-ms/.env
        echo "SYNGRISI_API_KEY=${{ inputs.syngrisi_api_key }}" >> test/taf-ms/.env

        # Add cache paths for standalone runner
        if [[ "${{ inputs.runner_type }}" == "standalone" ]]; then
          echo "FGAI_SERVICES_PATH=/shared/taf-cache/services" >> test/taf-ms/.env
          echo "FGAI_TEST_BASE_CACHE_PATH=/shared/taf-cache/vscode-cache" >> test/taf-ms/.env
          echo "FGAI_TEST_EXTENSION_DEV_PATH=/shared/taf-cache/vscode-plugin" >> test/taf-ms/.env
          echo "FGAI_TEST_COMMAND_CACHE=/shared/taf-cache/command-cache" >> test/taf-ms/.env
          echo "FGAI_TEST_REPOS_PATH=/shared/taf-cache/repositories" >> test/taf-ms/.env
        fi

        echo "=== Contents of test/taf-ms/.env ==="
        cat test/taf-ms/.env

        echo "=== Token Debug Info ==="
        echo "User token length: ${#user_token}"
        echo "User token ends with: ...${user_token: -10}"
        echo "==================================="

    - name: Copy .env.production to .env
      shell: bash
      run: cp plugins/vscode/.env.production plugins/vscode/.env

    - name: User info
      shell: bash
      run: whoami

    - name: Setup SSH key
      shell: bash
      run: |
        # Create .ssh directory if it doesn't exist
        mkdir -p ~/.ssh

        # Use the same SSH key for both runner types
        echo "${{ inputs.gh_runner_ssh_private_key }}" > ~/.ssh/cicd_id_rsa
        chmod 600 ~/.ssh/cicd_id_rsa

        # Add GitHub's SSH key to known_hosts
        ssh-keyscan github.com >> ~/.ssh/known_hosts

    - name: Set permissions for /tmp/zencoder (standalone only)
      if: inputs.runner_type == 'standalone'
      shell: bash
      run: |
        sudo mkdir -p /tmp/zencoder
        sudo chmod -R 777 /tmp/zencoder

    - name: Install dependencies
      shell: bash
      run: yarn install --immutable

    - name: Clear plugin compile command cache (standalone only)
      if: inputs.runner_type == 'standalone'
      shell: bash
      run: |
        rm -rf /shared/taf-cache/command-cache/*compile*
