name: Run E2E Tests
description: Runs E2E tests with specified test suite and shard configuration

inputs:
  test_suite:
    description: 'Test suite to run'
    required: true
  test_command:
    description: 'Custom test command (overrides test suite selection)'
    required: false
    default: ''
  shard:
    description: 'Shard configuration'
    required: true
  shard_index:
    description: 'Shard index for artifact naming'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Run tests with sharding
      id: run_tests
      shell: bash
      run: |
        cd test/taf-ms

        # Set sharding environment variable
        export SHARD="${{ inputs.shard }}"

        if [[ "${{ inputs.test_command }}" != "" ]]; then
          test_command="${{ inputs.test_command }}"
        else
          case "${{ inputs.test_suite }}" in
            "Regression")
              test_command="yarn run retest"
              ;;
            "Web")
              test_command="yarn test:web"
              ;;
            "Core")
              test_command="yarn test"
              ;;
            "Visual")
              test_command="yarn test:visual"
              ;;
            "Smoke")
              test_command="yarn smoke"
              ;;
            "Blocked Smoke")
              test_command="yarn smoke:blocked"
              ;;
            *)
              test_command="yarn smoke"
              ;;
          esac
        fi

        echo "Running test command with shard ${{ inputs.shard }}: xvfb-run $test_command"
        set +e
        xvfb-run $test_command

        # Store test command exit code
        test_exit_code=$?
        echo "TEST_EXIT_CODE=$test_exit_code" >> $GITHUB_ENV

        # Exit with the original test exit code
        exit $test_exit_code

    - name: Check blob reports files
      shell: bash
      run: |
        echo "Current directory: '$(pwd)'"
        ls -la test/taf-ms/reports/blob-report/

    - name: Upload blob report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: blob-report-${{ inputs.shard_index }}
        path: test/taf-ms/reports/blob-report/blob-report-*.zip
        retention-days: 1
