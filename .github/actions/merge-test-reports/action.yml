name: Merge Test Reports
description: Downloads, merges and uploads test reports, and extracts test analysis

inputs:
  blob_report_path:
    description: "Path to blob reports"
    required: true
  merged_report_path:
    description: "Path to merged reports"
    required: true
  runner_type:
    description: "Runner type (farm/standalone)"
    required: false
    default: "farm"
  workload_identity_provider:
    description: "GCP workload identity provider"
    required: true
  service_account:
    description: "GCP service account"
    required: true
  github_run_number:
    description: "GitHub run number"
    required: true
  gh_runner_ssh_private_key:
    description: "GitHub runner SSH private key"
    required: false

outputs:
  test_analysis:
    description: "Test analysis summary"
    value: ${{ steps.parse-test-results.outputs.analysis }}

runs:
  using: "composite"
  steps:
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: "22.4.0"
        cache: "yarn"

    - name: Install Yarn
      shell: bash
      run: npm install --global yarn

    - name: Install dependencies
      shell: bash
      run: yarn install --immutable

    - name: Create blob reports directory
      shell: bash
      run: |
        echo "Cleaning up blob reports directory..."
        rm -rf ${{ inputs.blob_report_path }}
        echo "Creating blob reports directory"
        mkdir -p ${{ inputs.blob_report_path }}
        ls -la ${{ inputs.blob_report_path }}

    - name: Download all blob reports
      uses: actions/download-artifact@v4
      with:
        path: ${{ inputs.blob_report_path }}
        pattern: blob-report-*
        merge-multiple: true

    - name: Check downloaded artifacts
      shell: bash
      run: |
        echo "Current directory: '$(pwd)'"
        ls -la ${{ inputs.blob_report_path }}

    - name: Create merged report directory
      shell: bash
      run: |
        echo "Cleaning up merged report directory..."
        rm -rf ${{ inputs.merged_report_path }}
        echo "Creating merged report directory"
        mkdir -p ${{ inputs.merged_report_path }}
        ls -la ${{ inputs.merged_report_path }}

    - name: Merge reports
      shell: bash
      run: |
        echo "Merging blob reports from ${{ inputs.blob_report_path }}"
        export NODE_OPTIONS='--max-old-space-size=8192'
        cd test/taf-ms
        npx playwright merge-reports --config merge.config.ts ${{ inputs.blob_report_path }}
        # check merged report
        ls -la ${{ inputs.merged_report_path }}

    - name: Upload merged test report
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-report-merged
        path: ${{ inputs.merged_report_path }}

    - name: Upload report.zip artifact
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: report-zip
        path: ${{ inputs.blob_report_path }}/report.zip
        if-no-files-found: warn

    - name: Authenticate with Google Cloud
      uses: google-github-actions/auth@v2
      with:
        token_format: access_token
        workload_identity_provider: ${{ inputs.workload_identity_provider }}
        service_account: ${{ inputs.service_account }}

    - name: Install gcloud CLI
      shell: bash
      run: |
        echo "Installing gcloud CLI..."
        if ! command -v gcloud &> /dev/null; then
          echo "gcloud not found, installing..."
          curl -sSL https://sdk.cloud.google.com | bash
          exec -l $SHELL
        fi
        gcloud --version

    - name: Upload merged test report to GCS
      if: always()
      shell: bash
      run: |
        echo "Uploading merged test report to GCS"
        echo "Current directory: '$(pwd)'"
        ls -la ${{ inputs.merged_report_path }}
        gsutil -m cp -r ${{ inputs.merged_report_path }}/* gs://forgood-test-reports/${{ inputs.github_run_number }}/

    - name: Extract and parse test analysis
      id: parse-test-results
      if: always()
      shell: bash
      run: |
        echo "Extracting and parsing test results..."
        cd test/taf-ms

        # Find and extract the blob report zip file
        blob_report_zip=$(find ${{ inputs.blob_report_path }} -name "report.zip" | head -1)
        if [ -n "$blob_report_zip" ]; then
          echo "Found blob report: $blob_report_zip"

          # Create extraction directory
          extraction_dir="${{ inputs.blob_report_path }}/extracted"
          mkdir -p "$extraction_dir"

          # Extract the zip file
          unzip -o "$blob_report_zip" -d "$extraction_dir"

          ls -la "$extraction_dir"

          # Look for report.jsonl
          jsonl_file="$extraction_dir/report.jsonl"
          if [ -f "$jsonl_file" ]; then
            echo "Found report.jsonl: $jsonl_file"

            # Parse the JSONL file using our script
            analysis_result=$(npx tsx src/scripts/parse-merged-jsonl-log.ts "$jsonl_file")
            echo "Analysis result: $analysis_result"

            # Extract key metrics for Slack
            total_tests=$(echo "$analysis_result" | jq -r '.totalDistinctTests')
            passed_tests=$(echo "$analysis_result" | jq -r '.passedTestsCount')
            flaky_tests=$(echo "$analysis_result" | jq -r '.flakyTestsCount')
            skipped_tests=$(echo "$analysis_result" | jq -r '.skippedTestsCount')
            failed_tests=$(echo "$analysis_result" | jq -r '.failedTestsCount')
            avg_duration=$(echo "$analysis_result" | jq -r '.averageDuration')
            longest_test_duration=$(echo "$analysis_result" | jq -r '.longestTest.duration // "N/A"')
            longest_test_title=$(echo "$analysis_result" | jq -r '.longestTest.title // "N/A"')
            percentage_passed=$(echo "$analysis_result" | jq -r '.percentageOfPassedTests')

            # Create formatted summary for Slack
            passed_percentage_display="${percentage_passed}%"
            if [ "$(echo "$percentage_passed > 95" | awk '{print ($1 > $3)}')" -eq 1 ]; then
              passed_percentage_display="${percentage_passed}% :parrot_hd:"
            fi
            analysis_summary="📊 *Test Analysis*:\n• Total tests: $total_tests\n• Passed: $passed_tests ($passed_percentage_display)\n• Failed: $failed_tests\n• Skipped: $skipped_tests\n• Flaky: $flaky_tests\n• Avg duration: $avg_duration\n• Longest test: $longest_test_duration\n• Longest test title: $longest_test_title"

            # Set output for use in other jobs
            echo "analysis<<EOF" >> $GITHUB_OUTPUT
            echo "$analysis_summary" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT

            echo "Test analysis completed successfully"
          else
            echo "report.jsonl not found in extracted files"
            echo "analysis=📊 Test analysis not available" >> $GITHUB_OUTPUT
          fi
        else
          echo "No blob report zip file found"
          echo "analysis=📊 Test analysis not available" >> $GITHUB_OUTPUT
        fi

    - name: Prepare report URL
      if: always()
      shell: bash
      run: echo "https://testreports.zencoder.ai/${{ inputs.github_run_number }}/"
