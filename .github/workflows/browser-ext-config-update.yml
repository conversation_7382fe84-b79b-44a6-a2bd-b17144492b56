name: Update Browser Extension Config

on:
  push:
    branches:
      - main
    paths:
      - 'libs/browser-extension/src/config.ts'
  workflow_dispatch:

permissions:
  id-token: write
  contents: read

jobs:
  update-config:
    name: Update Browser Extension Config
    runs-on:
      group: buildfarm
      labels: "buildfarm-large"
    steps:
      - name: Cleanup build folder
        run: |
          rm -rf ./*
          rm -rf ./.??*

      - name: Fetch Sources
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Enable Corepack
        run: corepack enable

      - name: Install Dependencies and Build
        run: |
          yarn install --immutable && yarn browser-ext:package

      - name: GCloud auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: '${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}'
          service_account: '${{ secrets.SERVICE_ACCOUNT }}'

      - name: Upload Config to Google Cloud Storage
        run: |
          gcloud storage cp libs/browser-extension/browser-extension-config.json gs://zencoder-public-dev/browser-extension-config.json
          gcloud storage cp libs/browser-extension/browser-extension-config.json gs://zencoder-public/browser-extension-config.json
