name: VSCode E2E Linux Tests

on:
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Select test suite'
        required: false
        type: choice
        options:
          - Custom
          - Regression
          - Web
          - Core
          - Visual
          - Smoke
          - Blocked Smoke
        default: 'Smoke'
      test_command:
        description: 'Custom test command (overrides test suite selection)'
        required: false
        default: ''
      backend_environment:
        description: 'Select backend environment'
        required: true
        type: choice
        options:
          - dev
          - dynamic
        default: 'dev'
      dynamic_backend_url:
        description: 'Dynamic backend base URL (works only with dynamic backend environment)'
        required: false
        type: string
        default: ''
      runner_type:
        description: 'Select runner type'
        required: false
        type: choice
        options:
          - farm
          - standalone
        default: 'farm'
      total_shard_nodes:
        description: 'Total number of shard nodes for test parallelization (ignored for standalone runner)'
        required: false
        type: number
        default: 2
      qase_project_code:
        description: 'QASE Project Code'
        required: false
        default: 'ZEN'
      qase_run_id:
        description: 'QASE Run ID (for updating existing runs)'
        required: false
        default: ''

  workflow_call:
    inputs:
      test_suite:
        required: true
        type: string
      skip_error:
        required: false
        type: string
        default: 'false'
      slack_channel:
        required: false
        type: string
        default: 'aqa-notify'
      backend_environment:
        required: true
        type: string
        default: 'dev'
      runner_type:
        required: false
        type: string
        default: 'farm'
      total_shard_nodes:
        required: false
        type: number
        default: 2
      qase_project_code:
        description: 'QASE Project Code'
        required: false
        type: string
        default: 'ZEN'
      qase_run_id:
        description: 'QASE Run ID (for updating existing runs)'
        required: false
        type: string
        default: ''

permissions:
  id-token: write
  contents: read

env:
  # QASE Configuration
  QASE_API_TOKEN: ${{ secrets.QASE_TOKEN }}
  QASE_PROJECT_CODE: ${{ inputs.qase_project_code || 'ZEN' }}
  QASE_RUN_NAME: '${{ inputs.test_suite }} Branch: ${{ github.ref_name }} E2E Tests'
  QASE_RUN_ID: ${{ inputs.qase_run_id }}
  QASE_RUN_DESCRIPTION: "Automated VS E2E test run triggered by [@${{ github.actor }}](https://github.com/${{ github.actor }})\nScope: ${{ inputs.test_suite }}\nBranch: [${{ github.ref_name }}](https://github.com/${{ github.repository }}/tree/${{ github.ref_name }})\nReport: [Test Report](https://testreports.zencoder.ai/${{ github.run_number }}/)\nRun Result Details: [GitHub Actions Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
  QASE_MODE: 'testops'
  BLOB_REPORT_PATH: '/tmp/reports/blob-reports'
  MERGED_REPORT_PATH: '/tmp/reports/merged-html'

jobs:
  workflow_info:
    runs-on: ubuntu-latest
    steps:
      - name: Show dispatch inputs
        run: |
          echo "Branch: ${{ github.ref_name }}"
          echo "Inputs: ${{ toJSON(inputs) }}"
  setup:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.generate-matrix.outputs.matrix }}
      qase_run_id: ${{ steps.create-qase-run.outputs.id || inputs.qase_run_id }}
      use_sharding: ${{ steps.check-runner-type.outputs.use_sharding }}
      runner_type: ${{ steps.check-runner-type.outputs.runner_type }}

    steps:
      - name: Check runner type and set sharding
        id: check-runner-type
        run: |
          runner_type="${{ inputs.runner_type || 'farm' }}"
          echo "runner_type=$runner_type" >> $GITHUB_OUTPUT

          if [[ "$runner_type" == "standalone" ]]; then
            echo "use_sharding=false" >> $GITHUB_OUTPUT
            echo "Using standalone runner - sharding disabled"
          else
            echo "use_sharding=true" >> $GITHUB_OUTPUT
            echo "Using farm runners - sharding enabled"
          fi

      - name: Generate sharding matrix
        id: generate-matrix
        run: |
          runner_type="${{ steps.check-runner-type.outputs.runner_type }}"
          if [[ "$runner_type" == "standalone" ]]; then
            # For standalone runner, always use 1/1 shard
            matrix='{"include":[{"shard":"1/1","shard_index":1}]}'
          else
            # For farm runners, use specified shard count
            total_shards=${{ inputs.total_shard_nodes || 1 }}
            matrix="{\"include\":["
            for i in $(seq 1 $total_shards); do
              if [ $i -gt 1 ]; then
                matrix+=","
              fi
              matrix+="{\"shard\":\"$i/$total_shards\",\"shard_index\":$i}"
            done
            matrix+="]}"
          fi
          echo "matrix=$matrix" >> $GITHUB_OUTPUT
          echo "Generated matrix: $matrix"

      - name: Create QASE Test Run
        id: create-qase-run
        if: ${{ env.QASE_RUN_ID == '' }}
        uses: qase-tms/gh-actions/run-create@v1
        with:
          description: ${{ env.QASE_RUN_DESCRIPTION }}
          project: ${{ env.QASE_PROJECT_CODE }}
          title: ${{ env.QASE_RUN_NAME }}
          token: ${{ env.QASE_API_TOKEN }}

  run-tests:
    needs: setup
    uses: ./.github/workflows/vs-e2e-shard-execution.yaml
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.setup.outputs.matrix) }}
    with:
      test_suite: ${{ inputs.test_suite }}
      test_command: ${{ inputs.test_command }}
      backend_environment: ${{ inputs.backend_environment }}
      dynamic_backend_url: ${{ inputs.dynamic_backend_url }}
      runner_type: ${{ inputs.runner_type }}
      shard: ${{ matrix.shard }}
      shard_index: ${{ matrix.shard_index }}
      qase_project_code: ${{ inputs.qase_project_code }}
      qase_run_id: ${{ needs.setup.outputs.qase_run_id }}
    secrets: inherit

  merge-reports-farm:
    needs: [setup, run-tests]
    if: always() && needs.setup.outputs.runner_type != 'standalone'
    runs-on:
      group: buildfarm
      labels: 'buildfarm-aqa'
    outputs:
      test_analysis: ${{ steps.merge-reports.outputs.test_analysis }}

    steps:
      - uses: actions/checkout@v4

      - name: Merge Test Reports
        id: merge-reports
        uses: ./.github/actions/merge-test-reports
        with:
          blob_report_path: ${{ env.BLOB_REPORT_PATH }}
          merged_report_path: ${{ env.MERGED_REPORT_PATH }}
          runner_type: ${{ needs.setup.outputs.runner_type }}
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.SERVICE_ACCOUNT }}
          github_run_number: ${{ github.run_number }}
          gh_runner_ssh_private_key: ${{ secrets.GH_RUNNER_SSH_PRIVATE_KEY }}

  merge-reports-standalone:
    needs: [setup, run-tests]
    if: always() && needs.setup.outputs.runner_type == 'standalone'
    runs-on:
      group: self-hosted-aqa-runners
      labels: 'Linux'
    outputs:
      test_analysis: ${{ steps.merge-reports.outputs.test_analysis }}

    steps:
      - uses: actions/checkout@v4

      - name: Merge Test Reports
        id: merge-reports
        uses: ./.github/actions/merge-test-reports
        with:
          blob_report_path: ${{ env.BLOB_REPORT_PATH }}
          merged_report_path: ${{ env.MERGED_REPORT_PATH }}
          runner_type: ${{ needs.setup.outputs.runner_type }}
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.SERVICE_ACCOUNT }}
          github_run_number: ${{ github.run_number }}
          gh_runner_ssh_private_key: ${{ secrets.GH_RUNNER_SSH_PRIVATE_KEY }}

  finalize:
    needs: [setup, run-tests, merge-reports-farm, merge-reports-standalone]
    if: always()
    runs-on: ubuntu-latest

    steps:
      - name: Complete QASE Test Run
        if: always() && needs.setup.outputs.qase_run_id != '' && inputs.qase_run_id == ''
        id: complete_qase_run
        uses: qase-tms/gh-actions/run-complete@v1
        with:
          project: ${{ env.QASE_PROJECT_CODE }}
          id: ${{ needs.setup.outputs.qase_run_id }}
          token: ${{ env.QASE_API_TOKEN }}

      - name: Check overall test status
        id: check-status
        run: |
          # Check if any test job failed
          if [[ "${{ contains(needs.run-tests.result, 'failure') }}" == "true" ]]; then
            echo "TESTS_FAILED=true" >> $GITHUB_ENV
          else
            echo "TESTS_FAILED=false" >> $GITHUB_ENV
          fi

      - name: Check QASE run status
        if: always() && (inputs.qase_run_id != '' || needs.setup.outputs.qase_run_id != '') && env.TESTS_FAILED == 'true'
        id: check_qase_status
        run: |
          # Get QASE run status
          QASE_API_URL="https://api.qase.io/v1/run/${{ env.QASE_PROJECT_CODE }}/${{ inputs.qase_run_id || needs.setup.outputs.qase_run_id }}"
          echo "Fetching QASE run status from: $QASE_API_URL"

          # Get run details
          run_details=$(curl -s -H "Token: ${{ env.QASE_API_TOKEN }}" "$QASE_API_URL")
          echo "QASE API response: $run_details"

          # Extract status_text field which contains "passed" or "failed"
          qase_status=$(echo "$run_details" | jq -r '.result.status_text')
          echo "QASE run status: $qase_status"

          # Create QASE run URL
          qase_run_url="https://app.qase.io/run/${{ env.QASE_PROJECT_CODE }}/${{ inputs.qase_run_id || needs.setup.outputs.qase_run_id }}"
          echo "QASE run URL: $qase_run_url"
          echo "QASE_RUN_URL=$qase_run_url" >> $GITHUB_ENV

          # Extract failed tests if any
          failed_tests=$(echo "$run_details" | jq -r '.result.stats.failed')
          echo "Failed tests count: $failed_tests"

          # Store result information
          echo "QASE_STATUS=$qase_status" >> $GITHUB_ENV
          echo "QASE_FAILED_TESTS=$failed_tests" >> $GITHUB_ENV

          # Determine job status based on QASE status
          if [[ "$qase_status" == "passed" ]]; then
            echo "QASE status is 'passed'. Job will pass."
            exit 0
          elif [[ "$qase_status" == "failed" ]]; then
            echo "QASE status is 'failed'. Job will fail."
            exit 1
          else
            echo "QASE status is '$qase_status'. Job will show warning."
            exit 1
          fi

      - name: Final job status check
        if: always()
        run: |
          echo "Final job status determination:"
          echo "Test exit code: ${{ env.TEST_EXIT_CODE }}"
          echo "Tests failed flag: ${{ env.TESTS_FAILED }}"
          echo "QASE status: ${{ env.QASE_STATUS }}"
          echo "QASE run URL: ${{ env.QASE_RUN_URL }}"
          echo "QASE failed tests count: ${{ env.QASE_FAILED_TESTS }}"

          if [[ "${{ env.TESTS_FAILED }}" == "true" ]]; then
            # Tests failed
            if [[ "${{ env.QASE_STATUS }}" == "passed" ]]; then
              # But QASE says it passed (flaky tests), so we can pass the job
              echo "Tests failed but QASE status is 'passed'. Job will pass as tests are considered flaky."
              exit 0
            elif [[ "${{ env.QASE_STATUS }}" == "failed" ]]; then
              # QASE also says it failed, so the job should fail
              echo "Tests failed and QASE status is 'failed'. Job will fail."
              exit 1
            elif [[ -z "${{ env.QASE_STATUS }}" ]]; then
              # QASE status not checked or empty, fail based on tests
              echo "Tests failed and no QASE status available. Job will fail."
              exit 1
            else
              # QASE has some other status, default to failure
              echo "Tests failed and QASE status is '${{ env.QASE_STATUS }}'. Job will fail."
              exit 1
            fi
          else
            # Tests passed normally
            echo "Tests passed. Job will pass."
            exit 0
          fi

      - name: Create issue on failure with artifact and report link
        if: failure()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.CICD_TOKEN }}
          script: |
            const { owner, repo } = context.repo;
            const issueTitle = `Test failed: Run #${{ github.run_number }}`;
            const reportUrl = `https://testreports.zencoder.ai/${{ github.run_number }}/`;
            const runDetailsUrl = `https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}`;

            const qaseRunUrl = `${{ env.QASE_RUN_URL }}`;
            const failedTests = `${{ env.QASE_FAILED_TESTS }}`;

            const issueBody = `
            View the test report [here](${reportUrl}).

            View more run details and download artifacts [here](${runDetailsUrl}).

            View QASE run details [here](${qaseRunUrl})${failedTests != '0' ? ` (Failed tests: ${failedTests})` : ''}.

            Please note that the report may take a few minutes to become available.`;

            github.rest.issues.create({
              owner,
              repo,
              title: issueTitle,
              body: issueBody
            });

      - name: Post to the Slack channel
        if: always()
        uses: slackapi/slack-github-action@v2.0.0
        with:
          token: ${{ secrets.AQA_SLACK_BOT_TOKEN }}
          method: chat.postMessage
          payload: |
            {
              "channel": "${{ inputs.slack_channel || 'aqa-notify' }}",
              "text": "GitHub Action tests result: ${{ job.status }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Test Result status*: ${{ env.TESTS_FAILED == 'true' && (env.QASE_STATUS == 'passed' && '✅' || env.QASE_STATUS == 'failed' && '❌' || '⚠️') || job.status == 'success' && '✅' || '❌' }} ${{ env.TESTS_FAILED == 'true' && env.QASE_STATUS || job.status }}\n*Branch*: <https://github.com/${{ github.repository }}/tree/${{ github.ref_name }}|${{ github.ref_name }}>\n*Scope*: ${{ inputs.test_suite || 'Smoke' }}\n*Runner*: ${{ needs.setup.outputs.runner_type || 'farm' }}\n*Triggered by*: <https://github.com/${{ github.actor }}|@${{ github.actor }}>\n*Report*: <https://testreports.zencoder.ai/${{ github.run_number }}/>\n*QASE Results*: <https://app.qase.io/run/${{ env.QASE_PROJECT_CODE }}/${{ inputs.qase_run_id || needs.setup.outputs.qase_run_id }}>\n*Changes*: <${{ github.event.pull_request.html_url || github.event.head_commit.url }}>\n*Run Result Details*: <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}>\n\n${{ needs.merge-reports-farm.outputs.test_analysis || needs.merge-reports-standalone.outputs.test_analysis || '📊 Test analysis not available' }}"
                  }
                }
              ]
            }
