name: VSCode Nightly build

on:
  schedule:
    - cron: '0 0 * * *'

  workflow_dispatch:
    inputs:
      qase_project_code_vs:
        description: 'QASE Project Code'
        required: false
        default: 'ZEN'
      qase_run_id_vs:
        description: 'QASE Run ID (for updating existing runs)'
        required: false
        default: ''
      qase_project_code_jb:
        description: 'QASE Project Code'
        required: false
        default: 'ZENJB'
      qase_run_id_jb:
        description: 'QASE Run ID JB (for updating existing runs)'
        required: false
        default: ''

permissions:
  id-token: write
  contents: write
  pull-requests: write

jobs:
  get_current_base_version:
    name: Get Current Base Version
    runs-on:
      group: buildfarm
      labels: "buildfarm-small"
    outputs:
      current_base_version: ${{ steps.get_current_base_version.outputs.current_base_version }}
    steps:
      - name: Cleanup build folder
        run: |
          rm -rf ./*
          rm -rf ./.??*

      - name: Fetch Sources
        uses: actions/checkout@v4

      - name: Get current base version
        id: get_current_base_version
        run: |
          # Extract the version from package.json
          PACKAGE_VERSION=$(cat plugins/vscode/package.json | jq -r .version)
          CURRENT_BASE_VERSION=${PACKAGE_VERSION%-SNAPSHOT}
          echo "current_base_version=${CURRENT_BASE_VERSION}" >> $GITHUB_OUTPUT

  use_prepare_release:
    name: Prepare Release
    needs: get_current_base_version
    uses: ./.github/workflows/_prepare_nightly_release.yml
    with:
      tag_prefix: vsc
      current_base_version: ${{ needs.get_current_base_version.outputs.current_base_version }}
      paths: 'plugins/vscode libs/webview'

  use_vs_lint:
    name: Linters
    needs: [use_prepare_release]
    if: ${{ needs.use_prepare_release.outputs.changes_detected == 'true' }}
    uses: ./.github/workflows/_vs-lint.yaml

  use_unit_tests:
    name: Unit tests
    needs: [use_prepare_release]
    if: ${{ needs.use_prepare_release.outputs.changes_detected == 'true' }}
    uses: ./.github/workflows/_vs-unit-tests.yaml

  use_e2e_tests:
    name: E2E Linux tests
    needs: [use_prepare_release]
    secrets: inherit
    uses: ./.github/workflows/vs-e2e-linux.yaml
    if: ${{ needs.use_prepare_release.outputs.changes_detected == 'true' }}
    with:
      test_suite: "Regression"
      skip_error: "true"
      slack_channel: "nightly-builds"
      qase_project_code: ${{ inputs.qase_project_code_vs }}
      qase_run_id: ${{ inputs.qase_run_id_vs }}
      backend_environment: "dev"

  release:
    needs: [use_prepare_release, use_vs_lint, use_unit_tests]
    name: Publish Nightly Build
    runs-on:
      group: buildfarm
      labels: "buildfarm-large"
    steps:
      - name: Cleanup build folder
        run: |
          rm -rf ./*
          rm -rf ./.??*

      - name: Fetch Sources
        uses: actions/checkout@v4

      - name: Commit and Tag New Version
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag vsc-${{ needs.use_prepare_release.outputs.new_version }}
          git push origin --tags

      - name: Create Github Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_TOKEN }}
        with:
          tag_name: vsc-${{ needs.use_prepare_release.outputs.new_version }}
          release_name: vsc-${{ needs.use_prepare_release.outputs.new_version }}
          body: ${{ needs.use_prepare_release.outputs.commits }}
          draft: false
          prerelease: true

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: Enable Corepack
        run: corepack enable

      - name: Setup env
        run: |
          cp plugins/vscode/.env.production plugins/vscode/.env

      - name: Update version in package.json
        run: |
          NEW_VERSION=${{ needs.use_prepare_release.outputs.new_version }}
          # vscode marketplace supports only major.minor.patch versions so we need to convert it to a valid semver
          # Extract the nightly release number and add 1000 to it for the patch version
          BASE_VERSION="${NEW_VERSION%-nightly.*}"
          NIGHTLY_NUMBER="${NEW_VERSION##*.}"
          IFS='.' read -r -a version_parts <<< "$BASE_VERSION"
          PATCH_VERSION=$((NIGHTLY_NUMBER + 1000))
          MARKETPLACE_VERSION="${version_parts[0]}.${version_parts[1]}.${PATCH_VERSION}"
          jq --arg new_version "$MARKETPLACE_VERSION" '.version = $new_version' plugins/vscode/package.json > tmp.$$.json && mv tmp.$$.json plugins/vscode/package.json
          cat plugins/vscode/package.json

      - name: Publish to marketplace
        env:
          PLUGIN_ENV: pre-release
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG_ID: ${{ vars.SENTRY_ORG_ID }}
          SENTRY_VSCODE_PLUGIN_PROJECT: ${{ vars.SENTRY_VSCODE_PLUGIN_PROJECT }}
          SENTRY_WEBVIEW_PROJECT: ${{ vars.SENTRY_WEBVIEW_PROJECT }}
          SENTRY_DIFF_VIEWER_PROJECT: ${{ vars.SENTRY_DIFF_VIEWER_PROJECT }}
          SENTRY_RELEASE: ${{ needs.use_prepare_release.outputs.new_version }}
          FEATURE_FLAGS_API_KEY_VSCODE: ${{ secrets.FEATURE_FLAGS_API_KEY_VSCODE }}
        run: |
          yarn install --immutable && yarn run vscode:package
          cd plugins/vscode && yarn vsce publish -p ${{ secrets.VSCODE_MARKETPLACE_PAT }} --pre-release --no-git-tag-version

      - name: Get Vsix file path
        id: get_vsix_path
        run: |
          VSCODE_VSIX_PATH=$(ls plugins/vscode/*.vsix)
          echo "Vsix Path: ${VSCODE_VSIX_PATH}"
          echo "VSCODE_VSIX_PATH=${VSCODE_VSIX_PATH}" >> $GITHUB_OUTPUT

      - name: Upload Release Asset
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_TOKEN }}
        run: gh release upload vsc-${{ needs.use_prepare_release.outputs.new_version }} ${{ steps.get_vsix_path.outputs.VSCODE_VSIX_PATH }}

      - name: GCloud auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: '${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}'
          service_account: '${{ secrets.SERVICE_ACCOUNT }}'

      - name: Upload to GCloud
        run: gcloud storage cp ${{ steps.get_vsix_path.outputs.VSCODE_VSIX_PATH }} gs://zencoder-plugins/vscode/releases/zencoder-vscode-${{ needs.use_prepare_release.outputs.new_version }}.vsix
