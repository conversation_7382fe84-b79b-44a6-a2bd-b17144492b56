name: PR Title Checker

# can be ignored by assigning label to the PR - ignore-semantic-pull-request

# Examples of correct PR name:
#   feat(vsc): [TET-123] simple message
#   fix(vsc,wv): [TET-123] multiple scopes

on:
  pull_request:
    types: [opened, edited, synchronize, reopened, labeled, unlabeled]

jobs:
  check-pr-title:
    name: Validate PR Title
    runs-on:
      group: buildfarm
      labels: "buildfarm-small"
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          # VS Code
          # JetBrains
          # JetBrains Test Automation Framework
          # Webview
          # DiffViewer
          # Secrets Replacer
          # Workflow Service
          # gql - for all gql related changes
          # ci - for all CI/CD related changes
          scopes: |
            vsc
            jb
            taf-jb
            wv
            dv
            sr
            gql
            agent
            ws
            chrome-ext
            ci
          requireScope: true
          # The message should start either with a ticket number (for example [TET-123])
          subjectPattern: ^\[([A-Z]+-[0-9]+)\]\s.+$
          subjectPatternError: |
            The subject "{subject}" found in the pull request title "{title}"
            didn't match the configured pattern. Please ensure that the subject
            contains a valid ticket number in the format [XXX-123].
          ignoreLabels: |
            ignore-semantic-pull-request
