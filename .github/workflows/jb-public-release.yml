name: Jetbrains Public release

on:
  workflow_dispatch:

permissions:
  id-token: write
  contents: write
  pull-requests: write

jobs:
  prepare:
    name: Prepare Release
    runs-on:
      group: buildfarm
      labels: "buildfarm-small"
    outputs:
      release_version: ${{ steps.calculate_versions.outputs.release_version }}
      new_unreleased_version: ${{ steps.calculate_versions.outputs.new_unreleased_version }}
    steps:
      - name: Check if triggered from RC branch
        run: |
          if [[ ! "${{ github.ref }}" =~ ^refs/heads/jb/release-candidate/.*$ ]]; then
            echo "This workflow is allowed to run only from RC branches matching 'jb/release-candidate/**'."
            exit 1
          fi

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Calculate release version
        id: calculate_versions
        run: |
          GRADLE_VERSION=$(grep '^pluginVersion=' plugins/jetbrains/gradle.properties | cut -d'=' -f2)
          CURRENT_UNRELEASED_VERSION=${GRADLE_VERSION%-RC.*}
          echo "Current unreleased version: ${CURRENT_UNRELEASED_VERSION}"

          # Calculate new unreleased version (release version + minor increment)
          IFS='.' read -r -a version_parts <<< "$CURRENT_UNRELEASED_VERSION"
          minor=${version_parts[1]}
          NEW_UNRELEASED_VERSION="${version_parts[0]}.$((minor + 1)).0"

          echo "release_version=${CURRENT_UNRELEASED_VERSION}"
          echo "release_version=${CURRENT_UNRELEASED_VERSION}" >> $GITHUB_OUTPUT

          echo "new_unreleased_version=${NEW_UNRELEASED_VERSION}"
          echo "new_unreleased_version=${NEW_UNRELEASED_VERSION}" >> $GITHUB_OUTPUT

  release:
    needs: prepare
    name: Release
    runs-on:
      group: buildfarm
      labels: "buildfarm-large"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: ./.github/actions/jb-setup-build-env
        with:
          gcloud_workload_identity_provider: "${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}"
          gcloud_service_account: "${{ secrets.SERVICE_ACCOUNT }}"

      - name: Create release branch
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          git checkout -b jb/release/${{ needs.prepare.outputs.release_version }}

      - name: Update version
        run: |
          sed -i "s/^pluginVersion=.*/pluginVersion=${{ needs.prepare.outputs.release_version }}/" plugins/jetbrains/gradle.properties
          cat plugins/jetbrains/gradle.properties

      - name: Publish Plugin
        env:
          PUBLISH_TOKEN: ${{ secrets.PUBLISH_TOKEN }}
          CERTIFICATE_CHAIN: ${{ secrets.CERTIFICATE_CHAIN }}
          PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
          PRIVATE_KEY_PASSWORD: ${{ secrets.PRIVATE_KEY_PASSWORD }}
          RELEASE_CHANNELS: nightly,default
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG_ID: ${{ vars.SENTRY_ORG_ID }}
          SENTRY_JETBRAINS_PLUGIN_PROJECT: ${{ vars.SENTRY_JETBRAINS_PLUGIN_PROJECT }}
          SENTRY_WEBVIEW_PROJECT: ${{ vars.SENTRY_WEBVIEW_PROJECT }}
          FEATURE_FLAGS_API_KEY_JB: ${{ secrets.FEATURE_FLAGS_API_KEY_JB }}
        run: plugins/jetbrains/gradlew -p plugins/jetbrains publishPlugin

      - name: Commit changes
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git add plugins/jetbrains/gradle.properties
          git add plugins/jetbrains/CHANGELOG.md
          git commit -m "Release jetbrains plugin version ${NEW_RELEASE_VERSION}"
          git push --set-upstream origin jb/release/${{ needs.prepare.outputs.release_version }}
          git tag jb-${{ needs.prepare.outputs.release_version }}
          git push origin --tags

      - name: Create Github Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          plugins/jetbrains/gradlew -p plugins/jetbrains getChangelog --console=plain -q --no-header --no-summary > release-notes.md

          gh release create jb-${{ needs.prepare.outputs.release_version }} \
            --title jb-${{ needs.prepare.outputs.release_version }} \
            --verify-tag \
            --notes-file release-notes.md

      - name: Upload Release Asset
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: gh release upload jb-${{ needs.prepare.outputs.release_version }} plugins/jetbrains/build/distributions/*

      - name: Prepare PR to main branch after release
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_TOKEN }}
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          BRANCH=after-jb-release-${{ needs.prepare.outputs.release_version }}

          git checkout -b $BRANCH

          NEW_VERSION=${{ needs.prepare.outputs.new_unreleased_version }}-SNAPSHOT
          sed -i "s/^pluginVersion=.*/pluginVersion=${NEW_VERSION}/" plugins/jetbrains/gradle.properties
          cat plugins/jetbrains/gradle.properties

          git add plugins/jetbrains/gradle.properties
          git commit -m "Prepare for next development iteration"
          git push --set-upstream origin $BRANCH

          gh pr create \
            --title "chore(jb): Updates current version after ${{ needs.prepare.outputs.release_version }} plugin release" \
            --body "Updates current version after ${{ needs.prepare.outputs.release_version }} plugin release" \
            --label "ignore-semantic-pull-request" \

      - name: Upload to GCloud
        run: gcloud storage cp plugins/jetbrains/build/distributions/zencoder-${{ needs.prepare.outputs.release_version }}.zip gs://zencoder-plugins/jetbrains/releases/
