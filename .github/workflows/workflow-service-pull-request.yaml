name: Workflow Service Lint & Tests

on:
  pull_request:
    paths:
      - 'services/workflow/**'
      - 'workflow.Dockerfile'
      - 'poetry.lock'
      - 'pyproject.toml'

permissions:
  id-token: write
  contents: read

jobs:
  run_checks:
    name: Check PR
    runs-on:
      group: buildfarm
      labels: "buildfarm-large"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.4
          virtualenvs-in-project: true
          virtualenvs-path: .venv

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

      - name: GCloud auth
        id: auth
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: "${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}"
          service_account: "${{ secrets.SERVICE_ACCOUNT }}"

      - name: Configure Poetry for Google Artifact Registry
        run: |
          export PATH="/home/<USER>/.local/bin:$PATH"
          poetry config http-basic.forgood oauth2accesstoken $(gcloud auth print-access-token)

      - name: Install dependencies
        run: |
          poetry install --all-extras

      - name: Run linters
        run: |
          poetry run pre-commit run -a

      - name: Run tests
        env:
          ENVIRONMENT: local
          REDIS_URL: redis://
          FORGOOD_VERIFICATOR: ""
          OPENAI_LLM_MODEL: ""
          OPENAI_API_KEY: ""
          OPIK_WORKSPACE: forgoodai
          LLM_MODEL: ""
        run: |
          poetry run pytest -s -vv ./services/workflow/tests
