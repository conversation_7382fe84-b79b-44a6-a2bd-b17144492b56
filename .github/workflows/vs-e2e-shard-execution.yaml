name: VSCode E2E Shard Execution

on:
  workflow_call:
    inputs:
      test_suite:
        required: true
        type: string
      test_command:
        required: false
        type: string
        default: ''
      backend_environment:
        required: false
        type: string
        default: 'dev'
      dynamic_backend_url:
        required: false
        type: string
        default: ''
      runner_type:
        required: false
        type: string
        default: 'farm'
      shard:
        required: true
        type: string
      shard_index:
        required: true
        type: number
      qase_project_code:
        required: false
        type: string
        default: 'ZEN'
      qase_run_id:
        required: false
        type: string
        default: ''

permissions:
  id-token: write
  contents: read

env:
  GIT_CUSTOM_SSH_COMMAND: 'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=$HOME/.ssh/known_hosts -i $HOME/.ssh/cicd_id_rsa'
  # QASE Configuration
  QASE_API_TOKEN: ${{ secrets.QASE_TOKEN }}
  QASE_PROJECT_CODE: ${{ inputs.qase_project_code }}
  QASE_RUN_ID: ${{ inputs.qase_run_id }}
  QASE_MODE: 'testops'

jobs:
  workflow_info_farm:
    if: inputs.runner_type != 'standalone'
    environment: linux
    runs-on:
      group: buildfarm
      labels: 'buildfarm-aqa'
    steps:
      - name: Show dispatch inputs
        run: |
          echo "Branch: ${{ github.ref_name }}"
          echo "Inputs: ${{ toJSON(inputs) }}"

  workflow_info_standalone:
    if: inputs.runner_type == 'standalone'
    environment: linux
    runs-on:
      group: self-hosted-aqa-runners
      labels: 'Linux'
    steps:
      - name: Show dispatch inputs
        run: |
          echo "Branch: ${{ github.ref_name }}"
          echo "Inputs: ${{ toJSON(inputs) }}"

  test-shard-farm:
    if: inputs.runner_type != 'standalone'
    environment: linux
    runs-on:
      group: buildfarm
      labels: 'buildfarm-aqa'

    steps:
      - uses: actions/checkout@v4

      - name: Setup Test Environment
        uses: ./.github/actions/setup-test-environment
        with:
          backend_environment: ${{ inputs.backend_environment }}
          dynamic_backend_url: ${{ inputs.dynamic_backend_url }}
          qase_api_token: ${{ env.QASE_API_TOKEN }}
          qase_project_code: ${{ env.QASE_PROJECT_CODE }}
          qase_run_id: ${{ env.QASE_RUN_ID }}
          qase_mode: ${{ env.QASE_MODE }}
          qase_run_complete: ${{ inputs.qase_run_id == '' && 'true' || 'false' }}
          runner_type: ${{ inputs.runner_type }}
          mistral_api_key: ${{ secrets.MISTRAL_API_KEY }}
          openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          frontegg_dev_client_id: ${{ secrets.FRONTEGG_DEV_CLIENT_ID }}
          frontegg_dev_api_key: ${{ secrets.FRONTEGG_DEV_API_KEY }}
          sa_email: ${{ secrets.SA_EMAIL }}
          sa_password: ${{ secrets.SA_PASSWORD }}
          syngrisi_api_key: ${{ secrets.SYNGRISI_API_KEY }}
          gh_runner_ssh_private_key: ${{ secrets.GH_RUNNER_SSH_PRIVATE_KEY }}

      - name: Run E2E Tests
        uses: ./.github/actions/run-e2e-tests
        with:
          test_suite: ${{ inputs.test_suite }}
          test_command: ${{ inputs.test_command }}
          shard: ${{ inputs.shard }}
          shard_index: ${{ inputs.shard_index }}

  test-shard-standalone:
    if: inputs.runner_type == 'standalone'
    environment: linux
    runs-on:
      group: self-hosted-aqa-runners
      labels: 'Linux'

    steps:
      - uses: actions/checkout@v4

      - name: Setup Test Environment
        uses: ./.github/actions/setup-test-environment
        with:
          backend_environment: ${{ inputs.backend_environment }}
          dynamic_backend_url: ${{ inputs.dynamic_backend_url }}
          qase_api_token: ${{ env.QASE_API_TOKEN }}
          qase_project_code: ${{ env.QASE_PROJECT_CODE }}
          qase_run_id: ${{ env.QASE_RUN_ID }}
          qase_mode: ${{ env.QASE_MODE }}
          qase_run_complete: ${{ inputs.qase_run_id == '' && 'true' || 'false' }}
          runner_type: ${{ inputs.runner_type }}
          mistral_api_key: ${{ secrets.MISTRAL_API_KEY }}
          openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          frontegg_dev_client_id: ${{ secrets.FRONTEGG_DEV_CLIENT_ID }}
          frontegg_dev_api_key: ${{ secrets.FRONTEGG_DEV_API_KEY }}
          sa_email: ${{ secrets.SA_EMAIL }}
          sa_password: ${{ secrets.SA_PASSWORD }}
          syngrisi_api_key: ${{ secrets.SYNGRISI_API_KEY }}
          gh_runner_ssh_private_key: ${{ secrets.GH_RUNNER_SSH_PRIVATE_KEY }}

      - name: Run E2E Tests
        uses: ./.github/actions/run-e2e-tests
        with:
          test_suite: ${{ inputs.test_suite }}
          test_command: ${{ inputs.test_command }}
          shard: ${{ inputs.shard }}
          shard_index: ${{ inputs.shard_index }}
