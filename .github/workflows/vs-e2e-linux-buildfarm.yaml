name: VSCode E2E Linux Tests on BuildFarm

on:
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Select test suite'
        required: false
        type: choice
        options:
          - Custom
          - Regression
          - Web
          - Core
          - Smoke
          - Blocked Smoke
        default: 'Custom'
      test_command:
        description: 'Custom test command (overrides test suite selection)'
        required: false
        default: ''
  workflow_call:
    inputs:
      test_suite:
        required: true
        type: string
      skip_error:
        required: false
        type: string
        default: 'false'
      slack_channel:
        required: false
        type: string
        default: 'aqa-notify'

permissions:
  id-token: write
  contents: read

env:
  GIT_CUSTOM_SSH_COMMAND: 'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=$HOME/.ssh/known_hosts -i $HOME/.ssh/id_ed25519'
  TEST_REPORT_IS_MERGED: ${{ inputs.test_suite == 'Regression' || inputs.test_suite == 'Core' }}

jobs:
  linux-e2e:
    environment: linux

    runs-on:
      group: buildfarm
      labels: 'buildfarm-aqa'

    continue-on-error: ${{ inputs.skip_error == 'true' }}

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.4.0'
          cache: 'yarn'

      - name: Install Yarn
        run: npm install --global yarn

      - name: Configure environment
        run: |
          echo "MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }}" > test/taf-ms/.env
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> test/taf-ms/.env
          echo "FGAI_TEST_AUTH_STRING=vscode://zencoderai.zencoder/25f43bc3cec0c64e9977d183733a3529?windowId%3D10=&authToken=STUB&refreshToken=STUB" >> test/taf-ms/.env

          echo "TEST_CONF=dev" >> test/taf-ms/.env
          echo "LOG_LEVEL=debug" >> test/taf-ms/.env

          # Vendor token
          url="https://api.us.frontegg.com/auth/vendor/"
          headers='-H "accept: application/json" -H "content-type: application/json"'
          body=$(jq -n \
            --arg clientId "${{ secrets.FRONTEGG_DEV_CLIENT_ID }}" \
            --arg secret "${{ secrets.FRONTEGG_DEV_API_KEY }}" \
            '{clientId: $clientId, secret: $secret}')
          response=$(curl -s -X POST "$url" \
            -H "Accept: application/json" \
            -H "Content-Type: application/json" \
            -d "$body")
          vendor_token=$(echo "$response" | jq -r ".token")

          # User token
          reportUrl="https://api.us.frontegg.com/identity/resources/auth/v1/user"
          response=$(curl -s -X POST "$reportUrl" \
            -H "accept: */*" \
            -H "accept-language: en-GB,en-US;q=0.9,en;q=0.8" \
            -H "authorization: Bearer $vendor_token" \
            -H "content-type: application/json" \
            -H "dnt: 1" \
            -H "frontegg-vendor-host: dev.fe.zencoder.ai" \
            -H "origin: http://localhost:3000" \
            -H "priority: u=1, i" \
            -H "referer: http://localhost:3000/" \
            -H "sec-ch-ua: \"Not?A_Brand\";v=\"99\", \"Chromium\";v=\"130\"" \
            -H "sec-ch-ua-mobile: ?0" \
            -H "sec-ch-ua-platform: \"macOS\"" \
            -H "sec-fetch-dest: empty" \
            -H "sec-fetch-mode: cors" \
            -H "sec-fetch-site: cross-site" \
            -H "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36" \
            --data-raw "{\"email\":\"${{ secrets.SA_EMAIL }}\",\"password\":\"${{ secrets.SA_PASSWORD }}\"}")


          user_token=$(echo "$response" | jq -r ".accessToken")
          refresh_token=$(echo "$response" | jq -r ".refreshToken")


          echo "FGAI_TEST_AUTH_STRING_DEV=vscode://zencoderai.zencoder/25f43bc3cec0c64e9977d183733a3529?windowId%3D10%26&authToken=$user_token&refreshToken=$refresh_token" >> test/taf-ms/.env

          echo "FGAI_SERVICES_PATH=/shared/taf-cache/services" >> test/taf-ms/.env
          echo "FGAI_TEST_BASE_CACHE_PATH=/shared/taf-cache/vscode-cache" >> test/taf-ms/.env
          echo "=/shared/taf-cache/vscode-plugin" >> test/taf-ms/.env
          echo "FGAI_TEST_COMMAND_CACHE=/shared/taf-cache/command-cache" >> test/taf-ms/.env
          echo "FGAI_TEST_REPOS_PATH=/shared/taf-cache/repositories" >> test/taf-ms/.env
          echo "SYNGRISI_API_KEY=${{ secrets.SYNGRISI_API_KEY }}" >> test/taf-ms/.env

      - name: Copy .env.production to .env
        run: cp plugins/vscode/.env.production plugins/vscode/.env

      - name: User info
        run: whoami

      - name: Setup SSH key
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          # Define the path for the .ssh directory and the key file
          sshDir="$HOME/.ssh"
          keyPath="$sshDir/id_ed25519"
          knownHostsPath="$sshDir/known_hosts"

          # Check if .ssh directory exists, create if it doesn't
          mkdir -p $sshDir

          # Write the private key to the file
          echo "$SSH_PRIVATE_KEY" > $keyPath
          chmod 600 $keyPath

          # Add GitHub's SSH key to known_hosts
          ssh-keyscan github.com >> $knownHostsPath

          # Use the key for SSH connection
          ssh -i $keyPath -o StrictHostKeyChecking=no -o UserKnownHostsFile=$knownHostsPath ************** || true

      - name: Install dependencies
        run: |
          yarn install --immutable

      - name: Run tests
        run: |
          cd test/taf-ms
          if [[ "${{ inputs.test_command }}" != "" ]]; then
            test_command="${{ inputs.test_command }}"
          else
            case "${{ inputs.test_suite }}" in
              "Regression")
                test_command="yarn run retest"
                ;;
              "Web")
                test_command="yarn test:web"
                ;;
              "Core")
                test_command="yarn test"
                ;;
              "Smoke")
                test_command="yarn smoke:singlethread"
                ;;
              "Blocked Smoke")
                test_command="yarn smoke:blocked"
                ;;
              *)
                test_command="yarn smoke:singlethread"
                ;;
            esac
          fi
          echo "Running test command: xvfb-run $test_command"
          xvfb-run $test_command

      - name: Clear plugin compile command cache
        run: |
          rm -rf /shared/taf-cache/command-cache/*compile*

      - name: Upload test reports [single]
        if: ${{ !cancelled() && env.TEST_REPORT_IS_MERGED == 'false' }}
        uses: actions/upload-artifact@v4
        with:
          name: test-report-single
          path: test/taf-ms/reports/html

      - name: Upload test reports [merged]
        if: ${{ !cancelled() && env.TEST_REPORT_IS_MERGED == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: test-report-merged
          path: test/taf-ms/reports/merged-html

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.SERVICE_ACCOUNT }}

      - name: Install gcloud CLI
        run: |
          echo "Installing gcloud CLI..."
          if ! command -v gcloud &> /dev/null; then
            echo "gcloud not found, installing..."
            curl -sSL https://sdk.cloud.google.com | bash
            exec -l $SHELL
          fi
          gcloud --version

      - name: Upload test report to GCS [single]
        if: ${{ !cancelled() && env.TEST_REPORT_IS_MERGED == 'false' }}
        run: |
          gsutil -m cp -r test/taf-ms/reports/html/* gs://forgood-test-reports/${{ github.run_number }}/

      - name: Upload test report to GCS [merged]
        if: ${{ !cancelled() && env.TEST_REPORT_IS_MERGED == 'true' }}
        run: |
          gsutil -m cp -r test/taf-ms/reports/merged-html/* gs://forgood-test-reports/${{ github.run_number }}/

      - name: Prepare report URL
        if: ${{ !cancelled() }}
        run: echo "https://testreports.zencoder.ai/${{ github.run_number }}/"

      - name: Create issue on failure with artifact and report link
        if: failure()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.CICD_TOKEN }}
          script: |
            const { owner, repo } = context.repo;
            const issueTitle = `Test failed: Run #${{ github.run_number }}`;
            const reportUrl = `https://testreports.zencoder.ai/${{ github.run_number }}/`;
            const runDetailsUrl = `https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}`;

            const issueBody = `
            View the test report [here](${reportUrl}).

            View more run details and download artifacts [here](${runDetailsUrl}).

            Please note that the report may take a few minutes to become available.`;

            github.rest.issues.create({
              owner,
              repo,
              title: issueTitle,
              body: issueBody
            });

      - name: Post to the Slack channel
        if: ${{ !cancelled() }}
        uses: slackapi/slack-github-action@v2.0.0
        with:
          token: ${{ secrets.AQA_SLACK_BOT_TOKEN }}
          method: chat.postMessage
          payload: |
            {
              "channel": "${{ inputs.slack_channel || 'aqa-notify' }}",
              "text": "GitHub Action tests result: ${{ job.status }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Test Result status*: ${{ job.status }}\n\n*Branch*: <https://github.com/${{ github.repository }}/tree/${{ github.ref_name }}|${{ github.ref_name }}>\n\n*Scope*: ${{ inputs.test_suite || 'Smoke' }}\n\n*Triggered by*: <https://github.com/${{ github.actor }}|@${{ github.actor }}>\n\n*Report*: <https://testreports.zencoder.ai/${{ github.run_number }}/>\n\n*Changes*: <${{ github.event.pull_request.html_url || github.event.head_commit.url }}>\n\n*Run Result Details*: <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}>"
                  }
                }
              ]
            }
