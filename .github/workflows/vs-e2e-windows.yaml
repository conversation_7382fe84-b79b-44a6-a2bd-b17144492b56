name: VSCode E2E Windows Tests

on:
  workflow_dispatch:
    inputs:
      test_command:
        description: 'Test command to run'
        required: false
        default: 'yarn smoke'

env:
  GIT_CUSTOM_SSH_COMMAND: 'ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=C:/Windows/ServiceProfiles/NetworkService/.ssh/known_hosts -i C:/Windows/ServiceProfiles/NetworkService/.ssh/id_ed25519'

jobs:
  windows-e2e:
    environment: windows

    runs-on:
      group: self-hosted-aqa-runners
      labels: 'Windows'

    steps:
      - uses: actions/checkout@v4

      - name: Add Cygwin to PATH
        run: echo "C:\cygwin64\bin" >> $GITHUB_PATH

      - name: Display hostname
        run: hostname

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.4.0'

      - name: Install Yarn
        run: npm install --global yarn

      - name: Configure environment
        run: |
          echo "${{ vars.ENV_FILE }}" > test/taf-ms/.env
          echo "MISTRAL_API_KEY=${{ secrets.MISTRAL_API_KEY }}" >> test/taf-ms/.env
          echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> test/taf-ms/.env
          echo "# FGAI_TEST_AUTH_STRING=${{ secrets.FGAI_TEST_AUTH_STRING }}" >> test/taf-ms/.env
          echo "FGAI_TEST_AUTH_STRING=" >> test/taf-ms/.env
          echo "SYNGRISI_API_KEY=${{ secrets.SYNGRISI_API_KEY }}" >> test/taf-ms/.env
          echo "TEST_CONF=dev" >> test/taf-ms/.env
          echo "LOG_LEVEL=debug" >> test/taf-ms/.env
          # Vendor token
          $url = "https://api.us.frontegg.com/auth/vendor/"
          $headers = @{
              "accept" = "application/json"
              "content-type" = "application/json"
          }
          $body = @{
              clientId = "${{ secrets.FRONTEGG_DEV_CLIENT_ID }}"
              secret = "${{ secrets.FRONTEGG_DEV_API_KEY }}"
          } | ConvertTo-Json
          $response = Invoke-WebRequest -Uri $url -Method Post -Headers $headers -Body $body
          $vendor_token = ($response.Content | ConvertFrom-Json).token

          # User token
          $headers = @{
              "accept" = "*/*"
              "accept-language" = "en-GB,en-US;q=0.9,en;q=0.8"
              "authorization" = "Bearer $vendor_token"
              "content-type" = "application/json"
              "dnt" = "1"
              "frontegg-vendor-host" = "dev.fe.zencoder.ai"
              "origin" = "http://localhost:3000"
              "priority" = "u=1, i"
              "referer" = "http://localhost:3000/"
              "sec-ch-ua" = "`"Not?A_Brand`";v=`"99`", `"Chromium`";v=`"130`""
              "sec-ch-ua-mobile" = "?0"
              "sec-ch-ua-platform" = "`"macOS`""
              "sec-fetch-dest" = "empty"
              "sec-fetch-mode" = "cors"
              "sec-fetch-site" = "cross-site"
              "user-agent" = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36"
          }
          $body = @{
              email = "${{ secrets.SA_EMAIL }}"
              password = "${{ secrets.SA_PASSWORD }}"
          } | ConvertTo-Json
          $response = Invoke-WebRequest -Uri "https://api.us.frontegg.com/identity/resources/auth/v1/user" -Method Post -Headers $headers -Body $body
          $respContent = ($response.Content | ConvertFrom-Json)
          $accessToken = $respContent.accessToken
          $refreshToken = $respContent.refreshToken
          echo "FGAI_TEST_AUTH_STRING_DEV=vscode://zencoderai.zencoder/25f43bc3cec0c64e9977d183733a3529?windowId%3D10%26&authToken=$accessToken&refreshToken=$refreshToken" >> test/taf-ms/.env

      - name: Copy .env.production to .env
        run: |
          Copy-Item "plugins\vscode\.env.production" "plugins\vscode\.env" -Force

      - name: User info
        run: whoami

      - name: Setup SSH key
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          # Define the path for the .ssh directory and the key file
          $sshDir = "C:\Users\<USER>\.ssh"
          $keyPath = Join-Path $sshDir "id_ed25519"
          $knownHostsPath = Join-Path $sshDir "known_hosts"

          # Check if .ssh directory exists, create if it doesn't
          if (-not (Test-Path $sshDir)) {
            New-Item -ItemType Directory -Path $sshDir | Out-Null
          }

          # Write the private key to the file
          $env:SSH_PRIVATE_KEY | Out-File $keyPath -Encoding ascii -NoNewline

          # Add GitHub's SSH key to known_hosts
          ssh-keyscan github.com | Out-File -Append -Encoding ascii $knownHostsPath

          # Use the key for SSH connection
          ssh -i $keyPath -o StrictHostKeyChecking=no -o UserKnownHostsFile=$knownHostsPath -T **************
          if ($LASTEXITCODE -ne 0) { $host.SetShouldExit(0) }

      # - name: Cache node_modules
      #   uses: actions/cache@v3
      #   with:
      #     path: node_modules
      #     key: ${{ runner.OS }}-node-modules-${{ hashFiles('package-lock.json') }}
      #     restore-keys: |
      #       ${{ runner.OS }}-node-modules-

      - name: Install dependencies
        run: |
          yarn install --immutable && yarn run vscode:package

      - name: Run tests
        run: |
          cd test/taf-ms
          $test_command = "${{ github.event.inputs.test_command || 'yarn smoke' }}"
          echo "Running test command: $test_command"
          $test_command

      - name: Upload test reports
        if: ${{ !cancelled() }}
        id: upload-test-reports
        uses: actions/upload-artifact@v4
        with:
          name: test-report
          path: test/taf-ms/reports/html

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          token_format: access_token
          workload_identity_provider: ${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.SERVICE_ACCOUNT }}

      - name: Install gcloud CLI
        run: |
          echo "Installing gcloud CLI..."
          if ! command -v gcloud &> /dev/null; then
            echo "gcloud not found, installing..."
            curl -sSL https://sdk.cloud.google.com | bash
            exec -l $SHELL
          fi
          gcloud --version

      - name: Upload test report to GCS
        if: ${{ !cancelled()
        run: |
          gsutil -m cp -r test/taf-ms/reports/html/* gs://forgood-test-reports/${{ github.run_number }}/

      - name: Prepare report URL
        if: ${{ !cancelled() }}
        run: echo "https://testreports.zencoder.ai/${{ github.run_number }}/"


      - name: Create issue on failure with artifact and report link
        if: failure()
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const { owner, repo } = context.repo;
            const issueTitle = `Test failed: Run #${{ github.run_number }}`;
            const reportUrl = `https://testreports.zencoder.ai/${{ github.run_number }}`;
            const runDetailsUrl = `https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}`;

            const issueBody = `

            View the test report [here](${reportUrl}).

            View more run details and download artifacts [here](${runDetailsUrl}).

            Please note that the report may take a few minutes to become available.`;

            github.rest.issues.create({
                owner,
                repo,
                title: issueTitle,
                body: issueBody
            });

      - name: Post to the Slack channel
        if: ${{ !cancelled() }}
        id: slack
        uses: slackapi/slack-github-action@v1.27.0
        with:
          channel-id: 'aqa-notify'
          payload: |
            {
              "text": "GitHub Action tests result: ${{ job.status }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Test Result status*: ${{ job.status }}\n\n*Report*: https://testreports.zencoder.ai/${{ github.run_number }}/\n\n*Changes*: ${{ github.event.pull_request.html_url || github.event.head_commit.url }}\n\n*Run Result Details*: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.AQA_SLACK_BOT_TOKEN }}
