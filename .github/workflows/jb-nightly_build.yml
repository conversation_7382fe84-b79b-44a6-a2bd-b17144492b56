name: Jetbrains Nightly build

on:
  schedule:
    - cron: '0 0 * * *'

  workflow_dispatch:

env:
  PLUGIN_VERIFIER_HOME_DIR: ${{ github.workspace }}/plugins/jetbrains/pluginVerifier

permissions:
  id-token: write
  contents: write
  pull-requests: write

jobs:
  get_current_base_version:
    name: Get Current Base Version
    runs-on:
      group: buildfarm
      labels: "buildfarm-small"
    outputs:
      current_base_version: ${{ steps.get_current_base_version.outputs.current_base_version }}
    steps:

      - name: Fetch Sources
        uses: actions/checkout@v4

      - name: Get current base version
        id: get_current_base_version
        run: |
          # Extract the version from gradle.properties
          GRADLE_VERSION=$(grep '^pluginVersion=' plugins/jetbrains/gradle.properties | cut -d'=' -f2)
          CURRENT_BASE_VERSION=${GRADLE_VERSION%-SNAPSHOT}
          echo "current_base_version=${CURRENT_BASE_VERSION}" >> $GITHUB_OUTPUT

  use_prepare_release:
    name: Prepare Release
    needs: get_current_base_version
    uses: ./.github/workflows/_prepare_nightly_release.yml
    with:
      tag_prefix: jb
      current_base_version: ${{ needs.get_current_base_version.outputs.current_base_version }}
      paths: 'plugins/jetbrains libs/webview'

  release_nightly:
    needs: [use_prepare_release]
    if: ${{ needs.use_prepare_release.outputs.changes_detected == 'true' }}
    name: Publish Nightly Build
    runs-on:
      group: buildfarm
      labels: "buildfarm-large"

    steps:
      - name: Fetch Sources
        uses: actions/checkout@v4

      - name: Update version in gradle.properties
        run: |
          NEW_VERSION=${{ needs.use_prepare_release.outputs.new_version }}
          sed -i "s/^pluginVersion=.*/pluginVersion=${NEW_VERSION}/" plugins/jetbrains/gradle.properties
          cat plugins/jetbrains/gradle.properties

      - uses: ./.github/actions/jb-setup-build-env
        with:
          gcloud_workload_identity_provider: "${{ secrets.WORKLOAD_IDENTITY_PROVIDER }}"
          gcloud_service_account: "${{ secrets.SERVICE_ACCOUNT }}"

#      - name: Setup Plugin Verifier IDEs Cache
#        uses: actions/cache@v4
#        with:
#          path: ${{ env.PLUGIN_VERIFIER_HOME_DIR }}/ides
#          key: plugin-verifier-${{ hashFiles('plugins/jetbrains/build/listProductsReleases.txt') }}
#
#      - name: Run Plugin Verification tasks
#        run: plugins/jetbrains/gradlew -p plugins/jetbrains verifyPlugin -Dplugin.verifier.home.dir=${{ env.PLUGIN_VERIFIER_HOME_DIR }}
#
#      - name: Collect Plugin Verifier Result
#        if: ${{ failure() }}
#        uses: actions/upload-artifact@v4
#        with:
#          name: pluginVerifier-result
#          path: ${{ github.workspace }}/plugins/jetbrains/build/reports/pluginVerifier

      - name: Publish Plugin
        env:
          PUBLISH_TOKEN: ${{ secrets.PUBLISH_TOKEN }}
          CERTIFICATE_CHAIN: ${{ secrets.CERTIFICATE_CHAIN }}
          PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
          PRIVATE_KEY_PASSWORD: ${{ secrets.PRIVATE_KEY_PASSWORD }}
          RELEASE_CHANNELS: nightly
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG_ID: ${{ vars.SENTRY_ORG_ID }}
          SENTRY_JETBRAINS_PLUGIN_PROJECT: ${{ vars.SENTRY_JETBRAINS_PLUGIN_PROJECT }}
          SENTRY_WEBVIEW_PROJECT: ${{ vars.SENTRY_WEBVIEW_PROJECT }}
          FEATURE_FLAGS_API_KEY_JB: ${{ secrets.FEATURE_FLAGS_API_KEY_JB }}
        run: plugins/jetbrains/gradlew -p plugins/jetbrains publishPlugin

      - name: Push tag
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag jb-${{ needs.use_prepare_release.outputs.new_version }}
          git push origin --tags

      - name: Create Github Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_TOKEN }}
        with:
          tag_name: jb-${{ needs.use_prepare_release.outputs.new_version }}
          release_name: jb-${{ needs.use_prepare_release.outputs.new_version }}
          body: ${{ needs.use_prepare_release.outputs.commits }}
          draft: false
          prerelease: true

      - name: Upload Release Asset
        env:
          GITHUB_TOKEN: ${{ secrets.CICD_TOKEN }}
        run: gh release upload jb-${{ needs.use_prepare_release.outputs.new_version }} plugins/jetbrains/build/distributions/zencoder-${{ needs.use_prepare_release.outputs.new_version }}.zip

      - name: Upload to GCloud
        run: gcloud storage cp plugins/jetbrains/build/distributions/zencoder-${{ needs.use_prepare_release.outputs.new_version }}.zip gs://zencoder-plugins/jetbrains/releases/
