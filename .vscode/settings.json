// Place your settings in this file to overwrite default and user settings.
{
  "files.exclude": {
    "out": false // set this to true to hide the "out" folder with the compiled JS files
  },
  "search.exclude": {
    "out": true // set this to false to include "out" folder in search results
  },
  // Turn off tsc task auto detection since we have the necessary tasks as npm scripts
  "typescript.tsc.autoDetect": "off",
  "prettier.configPath": ".prettierrc.json",
  "cSpell.words": [
    "agentic",
    "astream",
    "dones",
    "Forgood",
    "httpx",
    "Onigasm",
    "pytest",
    "truncator",
    "Zencoder"
  ],
  "[json]": {
    "editor.formatOnSave": false
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.tabSize": 2
  },
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter"
  },
  "zencoder.enableRepoIndexing": true,
  "zencoder.excludeGitIgnoreFromIndexing": true,
  "zencoder.featureFlags": {
    // it's an example of a feature flag that can be used by the extension...
    "test-feature-flag": {
      "value": "off",
      "payload": {}
    }
  },
  "python.analysis.exclude": ["**/swe-bench", "**/.git", "**/node_modules"],
  "editor.formatOnSave": true,

  // Test Automation Framework
  "playwright-bdd-helper.command-args": "",
  "playwright-bdd-helper.componentsFilePath": "test/taf-ms/src/po/Workbench.po.ts",
  "cucumberautocomplete.steps": ["test/taf-ms/src/step_definitions/**/*.ts"],
  "cucumberautocomplete.syncfeatures": "test/taf-ms/features/**/*.feature",
  "cucumberautocomplete.smartSnippets": true,
  "cucumberautocomplete.stepsInvariants": true,
  "cucumberautocomplete.skipDocStringsFormat": true,
  "cucumberautocomplete.customParameters": [
    {
      "parameter": "{role}",
      // (button|input|element|link|heading|checkbox|listitem)
      "value": "(element|.+?)"
      // "value": "(element|\\w+)"
    },
    {
      "parameter": "{verbs}",
      "value": "(create|update|delete|path)"
    },
    {
      "parameter": "{state}",
      "value": "(attached|detached|visible|hidden|undefined)"
    },
    {
      "parameter": "{action}",
      "value": "(click|hover|fill|type|check|select|press|fill in|set|focus|clear|hide|scroll to)"
    },
    {
      "parameter": "{attribute}",
      "value": "(name|test-id|label|role|text|placeholder|alt-text|title|tag|id|selector)"
    },
    {
      // Options: is visible|is not visible|is checked|is not checked|has text|does not have text|contains text|does not contain text|has value|does not have value|is present|is not present|is in viewport|is not in viewport|is focused|is not focused|is enabled|is not enabled|is editable|is not editable"
      "parameter": "{assert}",
      "value": "(be visible|is visible|is not visible|is checked|is not checked|has text|does not have text|contains text|does not contain text|has value|does not have value|value contains|value does not contain|is present|is not present|is in viewport|is not in viewport|is focused|is not focused|is enabled|is not enabled|is editable|is not editable|count equals|count is greater than|count is less than)"
    },
    {
      "parameter": "{string}",
      "value": "\".*\""
    },
    {
      "parameter": "{component}",
      "value": "\".*\""
    }
  ],
  "[cucumber]": {
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "editor.formatOnSave": true
  },

  "highlight.regexes": {
    "(<[^:]+?>)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#87CEEB"
        }
      ]
    },

    "(Then )": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#87CEEB",
          "fontWeight": "bold"
        }
      ]
    },

    "(<.+?[:].+?>)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "green"
        }
      ]
    },
    "(?<!#\\s*)(@only)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#ff00e1",
          "fontWeight": "bold"
        }
      ]
    },
    "(?<!#\\s*)(@skip)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#ff00e1",
          "fontWeight": "bold"
        }
      ]
    },
    "(?<!#\\s*)((?:When|Then|Given|And)\\s+I pause the test execution)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#f08f06",
          "fontWeight": "bold"
        }
      ]
    },
    "(?<!#\\s*)((?:When|Then|Given|And)\\s+I pause)": {
      "regexFlags": "g",
      "filterFileRegex": ".*\\.feature",
      "decorations": [
        {
          "color": "#f08f06",
          "fontWeight": "bold"
        }
      ]
    }
  },

  "jest.jestCommandLine": "yarn test:unit",
  "jest.runMode": {
    "type": "on-demand",
    "showInlineError": true
  },
  "python.testing.unittestEnabled": false,
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["-vv"]
}
