import { IntegrationConfig, IntegrationId, IntegrationsConfig } from './types';

const configs: IntegrationConfig[] = [
  {
    id: 'github.pr_comment',
    integrationId: IntegrationId.GitPrComment,
    urlMatchRegex: 'github.com.*/pull/[0-9]+[^/]*$',
    gitProvider: 'github',
    addButton: {
      triggerSelector: "//button[contains(., 'Resolve conversation')]",
      position: 'afterend',
      styles: {
        alignSelf: 'center',
        margin: '0 16px',
      },
      content: 'Address comments with Zencoder',
      size: 'L',
    },
    variables: {
      gitOwner: {
        variableName: 'Git Owner/Org',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 0,
      },
      gitRepo: {
        variableName: 'Git Repo name',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 1,
      },
      targetBranch: {
        type: 'fromPage',
        variableName: 'git PR base branch',
        valueType: 'single',
        selector: '.base-ref',
        extractContentBy: {
          type: 'textContent',
        },
      },
      currentBranch: {
        type: 'fromPage',
        variableName: 'git PR current branch',
        valueType: 'single',
        selector: '.head-ref',
        extractContentBy: {
          type: 'textContent',
        },
      },
      filePath: {
        type: 'fromPage',
        variableName: 'file path the comments related to',
        valueType: 'single',
        selector: 'summary a',
        commonParentSelector: '.review-thread-component',
        extractContentBy: {
          type: 'textContent',
        },
      },
      diff: {
        type: 'fromIde',
        ideVariable: 'diff',
        target: '{{targetBranch}}',
        current: '{{currentBranch}}',
      },
      addedLines: {
        type: 'fromPage',
        variableName:
          'List of added lines in the block of code comments related to. (list of numbers separated by comma)',
        valueType: 'array',
        selector: '.blob-num-addition[data-line-number]',
        commonParentSelector: '.review-thread-component',
        extractContentBy: {
          type: 'attributeValue',
          attributeName: 'data-line-number',
        },
        itemTemplate: '{{item}}',
        joinSymbol: ',',
      },
      deletedLines: {
        type: 'fromPage',
        variableName:
          'List of deleted lines in the block of code comments related to. (list of numbers separated by comma)',
        valueType: 'array',
        selector: '.blob-num-deletion[data-line-number]',
        commonParentSelector: '.review-thread-component',
        extractContentBy: {
          type: 'attributeValue',
          attributeName: 'data-line-number',
        },
        itemTemplate: '{{item}}',
        joinSymbol: ',',
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of the comments, comma separated',
        valueType: 'array',
        selector: '.js-comments-holder .comment-body',
        commonParentSelector: '.review-thread-component',
        extractContentBy: {
          type: 'textContent',
        },
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template:
            'I made a pull request. Please help me to address comments that are left during the review for the changes for the file {{filePath}}. Below find the changes has been made for this file, review comments and the lines these comments related to.',
        },
        {
          type: 'text',
          template: 'Changes made for the file in the PR:',
        },
        {
          type: 'codeSnippet',
          template: '{{diff}}',
        },
        {
          type: 'text',
          template: 'Lines of the changes, comments related to:',
        },
        {
          type: 'text',
          template: 'added: {{addedLines}}, deleted: {{deletedLines}}',
        },
        {
          type: 'text',
          template: 'Review comments need to be addressed:',
        },
        {
          type: 'codeSnippet',
          template: '{{comments}}',
        },
      ],
      attachedFilesPaths: ['{{filePath}}'],
    },
  },
  // gitlab pr comment
  {
    id: 'gitlab.pr_comment',
    integrationId: IntegrationId.GitPrComment,
    urlMatchRegex: 'gitlab.com.*/merge_requests/[0-9]+[^/]*$',
    gitProvider: 'gitlab',
    addButton: {
      triggerSelector: "[data-testid='discussion-content'] [data-testid='reply-wrapper']",
      position: 'beforeend',
      styles: {
        alignSelf: 'center',
        margin: '8px 0 0 0',
      },
      content: 'Address comments with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '[data-testid="discussion-content"]',
    },
    variables: {
      gitOwner: {
        variableName: 'Git Owner/Org',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 0,
      },
      gitRepo: {
        variableName: 'Git Repo name',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 1,
      },
      targetBranch: {
        type: 'fromPage',
        variableName: 'git PR base branch',
        valueType: 'single',
        selector:
          "(//*[contains(@class, 'detail-page-description')]//*[contains(@class, 'ref-container')])[2]",
        extractContentBy: {
          type: 'textContent',
        },
      },
      currentBranch: {
        type: 'fromPage',
        variableName: 'git PR current branch',
        valueType: 'single',
        selector:
          "(//*[contains(@class, 'detail-page-description')]//*[contains(@class, 'ref-container')])[1]",
        extractContentBy: {
          type: 'textContent',
        },
      },
      filePath: {
        type: 'fromPage',
        variableName: 'file path the comments related to',
        valueType: 'single',
        selector: "[data-testid='file-name-content']",
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      diff: {
        type: 'fromIde',
        ideVariable: 'diff',
        target: '{{targetBranch}}',
        current: '{{currentBranch}}',
      },
      addedLines: {
        type: 'fromPage',
        variableName:
          'List of added lines in the block of code comments related to. (list of numbers separated by comma)',
        valueType: 'array',
        selector: '.diff-line-num.new_line.new',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
        itemTemplate: '{{item}}',
        joinSymbol: ',',
      },
      deletedLines: {
        type: 'fromPage',
        variableName:
          'List of deleted lines in the block of code comments related to. (list of numbers separated by comma)',
        valueType: 'array',
        selector: '.diff-line-num.old_line.old',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
        itemTemplate: '{{item}}',
        joinSymbol: ',',
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of the comments, comma separated',
        valueType: 'array',
        selector: '.note-body',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template:
            'I made a pull request. Please help me to address comments that are left during the review for the changes for the file {{filePath}}. Below find the changes has been made for this file, review comments and the lines these comments related to.',
        },
        {
          type: 'text',
          template: 'Changes made for the file in the PR:',
        },
        {
          type: 'codeSnippet',
          template: '{{diff}}',
        },
        {
          type: 'text',
          template: 'Lines of the changes, comments related to:',
        },
        {
          type: 'text',
          template: 'added: {{addedLines}}, deleted: {{deletedLines}}',
        },
        {
          type: 'text',
          template: 'Review comments need to be addressed:',
        },
        {
          type: 'codeSnippet',
          template: '{{comments}}',
        },
      ],
      attachedFilesPaths: ['{{filePath}}'],
    },
  },
  // jira solve task
  {
    id: 'jira.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: [
      'atlassian.net/jira.*selectedIssue=',
      'atlassian.net/browse/[A-Z]+-[0-9]+[^/]*$',
    ],
    addButton: {
      triggerSelector:
        "[data-testid='issue.views.issue-base.context.status-and-approvals-wrapper.status-and-approval']",
      position: 'afterend',
      styles: {
        display: 'inline-flex',
        margin: '8px 0 8px 0',
      },
      size: 'L',
      hasLogo: true,
    },
    anchor: {
      isTriggerParent: true,
      selector:
        "[data-testid='issue.views.issue-details.issue-layout.issue-layout'], #ak-main-content ",
    },
    variables: {
      taskName: {
        type: 'fromPage',
        variableName: 'Jira Task name',
        valueType: 'single',
        selector: "[data-testid='issue.views.issue-base.foundation.summary.heading']",
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        type: 'fromPage',
        variableName: 'Jira Task name',
        valueType: 'single',
        selector: "[data-editor-container-id='issue-description-editor']",
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the task, comma separated',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector:
          "[data-testid*='issue-comment-base.ui.comment.ak-comment'][data-testid*='content']",
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please resolve the Jira issue',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'trello.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'trello.com.*/[0-9]+[^/]*$',
    addButton: {
      triggerSelector: '[data-testid="card-back-header"]',
      position: 'afterend',
      styles: {
        margin: '-8px 0 16px 40px',
        display: 'inline-flex',
      },
      content: 'Solve with Zencoder',
      hasLogo: true,
    },
    anchor: {
      isTriggerParent: true,
      selector: "[data-testid='card-back-name']",
    },
    variables: {
      taskName: {
        variableName: 'Task Name',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="card-back-title-input"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        variableName: 'Task Description',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="description-content-area"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the task, comma separated',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: "[data-testid='comment-container']",
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please resolve the Trello issue',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'linear.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'linear.app.*/issue/[A-Z]+-[0-9]+/[^/]*$',
    addButton: {
      triggerSelector: 'header',
      position: 'beforeend',
      styles: {},
      content: 'Solve with Zencoder',
      hasLogo: true,
      size: 'M',
    },
    anchor: {
      commonTriggerParentSelector: 'body',
      selector: "[data-view-id='issue-view']",
    },
    variables: {
      taskName: {
        variableName: 'Task Name',
        valueType: 'single',
        type: 'fromPage',
        selector: '[aria-label="Issue title"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        variableName: 'Task Description',
        valueType: 'single',
        type: 'fromPage',
        selector: '[aria-label="Issue description"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the task, comma separated',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: '[aria-label="Comment"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please resolve the Linear issue',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'monday.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'monday.com/.*/pulses/[0-9]+[^/]*$',
    addButton: {
      triggerSelector: '.pulse-overview-actions-wrapper',
      position: 'beforeend',
      styles: {},
      content: 'Solve with Zencoder',
      hasLogo: true,
      size: 'L',
    },
    anchor: {
      isTriggerParent: true,
      selector: '[role="dialog"]',
    },
    variables: {
      taskName: {
        variableName: 'Task Name',
        valueType: 'single',
        type: 'fromPage',
        selector: '.item-page-name',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        variableName: 'Task Description',
        valueType: 'single',
        type: 'fromPage',
        selector: '//*[contains(@class, "docItemDescription")]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please resolve the Monday task',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
      ],
    },
  },
  {
    id: 'wrike.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: ['wrike.com/.*sidePanelItemId=[0-9].*', 'wrike.com/.*task-view[^/]*'],
    addButton: {
      triggerSelector: '//work-item-header',
      position: 'beforeend',
      styles: {
        display: 'inline-flex',
      },
      content: 'Solve with Zencoder',
      hasLogo: true,
      size: 'L',
    },
    anchor: {
      isTriggerParent: true,
      selector: '.work-item-view__container',
    },
    variables: {
      taskName: {
        variableName: 'Task Name',
        valueType: 'single',
        type: 'fromPage',
        selector: '//task-title',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        variableName: 'Task Description',
        valueType: 'single',
        type: 'fromPage',
        selector: 'description .ql-editor',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of Comments',
        valueType: 'array',
        itemTemplate: 'Comment {{index}}: {{item}}',
        joinSymbol: '\n',
        selector: '//comment-content',
        extractContentBy: {
          type: 'textContent',
        },
        isInsideAnchor: true,
        limit: 5,
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please resolve the Wrike task',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'asana.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: ['asana.com/.*/[0-9]+[^/]*'],
    addButton: {
      triggerSelector: '.TaskPaneToolbar',
      position: 'afterbegin',
      styles: {
        margin: '0 8px 0 0',
      },
      size: 'M',
      hasLogo: true,
    },
    anchor: {
      isTriggerParent: true,
      selector: '.TaskPane',
    },
    variables: {
      taskName: {
        type: 'fromPage',
        variableName: 'Asana Task name',
        valueType: 'single',
        selector: '.TitleInput-objectName textarea',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        type: 'fromPage',
        variableName: 'Asana Task description',
        valueType: 'single',
        selector: '.TaskDescription .ProsemirrorEditor',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the task',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: '[data-testid="FeedBlockStory"] .BlockStoryStructure-body',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please help me resolve this Asana task',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'github.sonarcloud.resolve_security_issues',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'github.com.*/pull/[0-9]+[^/]*$',
    addButton: {
      triggerSelector:
        '//a[contains(@href, "sonarcloud.io") and contains(text(), "See analysis details")]',
      position: 'afterend',
      size: 'L',
      styles: {
        alignSelf: 'center',
        margin: '8px 0 8px 0',
        width: 'fit-content',
      },
      content: 'Fix with Zencoder',
    },
    variables: {
      sonarqubeUrl: {
        variableName: 'SonarQube URL',
        type: 'fromPage',
        valueType: 'single',
        selector: "//a[contains(@href,'sonarcloud.io/dashboard')]",
        extractContentBy: {
          type: 'attributeValue',
          attributeName: 'href',
        },
      },
      componentKeys: {
        variableName: 'Id of Sonarqube project',
        type: 'fromUrl',
        customUrl: '{{sonarqubeUrl}}',
        dependencies: ['sonarqubeUrl'],
        urlPart: 'search',
        queryName: 'id',
      },
      pullRequestNumber: {
        variableName: 'Pull Request Number',
        type: 'fromUrl',
        customUrl: '{{sonarqubeUrl}}',
        dependencies: ['sonarqubeUrl'],
        urlPart: 'search',
        queryName: 'pullRequest',
      },
      issues: {
        type: 'fromRequest',
        variableName: 'Security issues list',
        responseType: 'json',
        valueType: 'array',
        dependencies: ['pullRequestNumber', 'componentKeys'],
        itemTemplate:
          '{{index}}. Component: {{component}}, Line number: {{line}}, Severity: {{severity}}, Message: {{message}}',
        joinSymbol: '\n',
        url: 'https://sonarcloud.io/api/issues/search?pullRequest={{pullRequestNumber}}&s=FILE_LINE&issueStatuses=OPEN%2CCONFIRMED&ps=100&componentKeys={{componentKeys}}&p=1',
        query:
          "issues[?severity != 'INFO'].{severity: severity, component: component, message: message, line: line }",
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'There are some security issues:',
        },
        {
          type: 'text',
          template: '{{issues}}',
        },
        {
          type: 'text',
          template: 'Please resolve them',
        },
      ],
    },
  },
  {
    id: 'sonarcloud.resolve_security_issues',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'sonarcloud.io/project/issues.*',
    addButton: {
      triggerSelector: "[data-component='subnavigation-group']",
      position: 'beforebegin',
      size: 'M',
      styles: {
        display: 'flex',
        margin: '8px',
      },
      content: 'Fix code smell',
    },
    variables: {
      pullRequestNumber: {
        type: 'fromUrl',
        variableName: 'Pull Request Number',
        urlPart: 'search',
        queryName: 'pullRequest',
      },
      componentKeys: {
        type: 'fromUrl',
        variableName: 'Component Keys',
        urlPart: 'search',
        queryName: 'id',
      },
      issues: {
        type: 'fromRequest',
        variableName: 'Security issues list',
        responseType: 'json',
        valueType: 'array',
        dependencies: ['pullRequestNumber', 'componentKeys'],
        itemTemplate:
          '{{index}}. Component: {{component}}, Line number: {{line}}, Severity: {{severity}}, Message: {{message}}',
        joinSymbol: '\n',
        url: 'https://sonarcloud.io/api/issues/search?pullRequest={{pullRequestNumber}}&s=FILE_LINE&issueStatuses=OPEN%2CCONFIRMED&ps=100&componentKeys={{componentKeys}}&p=1',
        query:
          "issues[?severity != 'INFO'].{severity: severity, component: component, message: message, line: line }",
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'There are some security issues:',
        },
        {
          type: 'codeSnippet',
          template: '{{issues}}',
        },
        {
          type: 'text',
          template: 'Please resolve them',
        },
      ],
    },
  },
  {
    id: 'snyk.resolve_security_issues',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'app.snyk.io/.*',
    iframeSelector: '[data-snyk-test="IframeProxy"] iframe:not(.iframe-proxy-iframe--loading)',
    addButton: {
      triggerSelector: 'div.vue--issue-header__wrapper',
      position: 'beforeend',
      size: 'L',
      styles: {
        margin: '12px 0',
        width: 'fit-content',
      },
      content: 'Fix with zencoder',
    },
    variables: {
      issueName: {
        type: 'fromPage',
        variableName: 'Issue name',
        valueType: 'single',
        selector: 'h2.vue--issue-title__title',
        extractContentBy: {
          type: 'innerText',
        },
      },
      snykCode: {
        type: 'fromPage',
        variableName: 'Snyk Code',
        valueType: 'single',
        selector: 'li.vue--issue-meta-item a span',
        extractContentBy: {
          type: 'textContent',
        },
      },
      codeSnippet: {
        type: 'fromPage',
        variableName: 'Code snippet',
        valueType: 'single',
        selector: '.monaco-scrollable-element',
        extractContentBy: {
          type: 'innerText',
        },
      },
      fileName: {
        type: 'fromPage',
        variableName: 'File name',
        valueType: 'single',
        selector: "a[data-snyk-test='IssueSASTFileUrl'] strong",
        extractContentBy: {
          type: 'innerText',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'There is a security issue: {{issueName}}',
        },
        {
          type: 'text',
          template: 'Snyk code: {{snykCode}}',
        },
        {
          type: 'text',
          template: 'File name: {{fileName}}',
        },
        {
          type: 'codeSnippet',
          template: '{{codeSnippet}}',
        },
        {
          type: 'text',
          template: 'Please fix it.',
        },
      ],
      attachedFilesPaths: ['{{fileName}}'],
    },
  },
  {
    id: 'rollbar.resolve_error_report',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'app.rollbar.com/a/([^/]+)/fix/item/([^/]+)/([^/]+)',
    addButton: {
      triggerSelector:
        'main header:has(div > button > span) > div:first-of-type > div:first-of-type',
      position: 'afterbegin',
      size: 'NONE',
      styles: {
        height: '32px',
        textWrap: 'nowrap',
        color: 'rgb(242, 74, 7)',
        fontSize: '12px',
        display: 'block',
        alignSelf: 'center',
        padding: '7px 8px',
        border: '1.5px solid var(--ui-border)',
        borderRadius: '4px',
      },
      className: 'druids_typography_text',
      content: 'Fix Error',
      title: 'Fix with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: 'main',
    },
    variables: {
      errorName: {
        variableName: 'Error Name',
        valueType: 'single',
        type: 'fromPage',
        selector: 'h2:first-of-type, [data-testid="editable-title-textarea"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      stacktrace: {
        variableName: 'Stacktrace',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="trace-content"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'innerText',
        },
        replace: ['\\s*copy\\s+filepath\\s*', 'gi', ''],
      },
      crashReport: {
        variableName: 'Crash Report',
        valueType: 'single',
        type: 'fromPage',
        selector: '#headlessui-disclosure-panel-\\:r14\\:',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'innerText',
        },
        replace: ['Copy\n', 'g', ''],
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please fix this Rollbar error report',
        },
        {
          type: 'text',
          template: 'Error:',
        },
        {
          type: 'codeSnippet',
          template: '{{errorName}}',
        },
        {
          type: 'text',
          template: 'Error stacktrace:',
        },
        {
          type: 'codeSnippet',
          template: '{{stacktrace}}',
        },
        {
          type: 'text',
          template: 'Crash report:',
        },
        {
          type: 'codeSnippet',
          template: '{{crashReport}}',
        },
      ],
    },
  },
  {
    id: 'sentry.resolve_error_report',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'sentry.io/issues/\\w+/',
    addButton: {
      triggerSelector: '[aria-label="More Actions"]',
      position: 'afterend',
      styles: {
        height: '32px',
        textWrap: 'nowrap',
        backgroundColor: 'rgb(242, 74, 7)',
        color: '#fff',
        fontWeight: 'bold',
        display: 'block',
        alignSelf: 'center',
        padding: '5px 10px',
        border: '1px solid rgb(224, 220, 229)',
        borderRadius: '6px',
      },
      content: 'Fix Error',
      title: 'Fix the Error with Zencoder',
    },
    variables: {
      issueId: {
        variableName: 'ID of the Issue',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 1,
      },
      organization: {
        variableName: 'Organization name',
        type: 'fromUrl',
        urlPart: 'hostname',
        match: '^([^\\.]+)',
      },
      errorDescription: {
        type: 'fromRequest',
        variableName: 'Error Description',
        responseType: 'json',
        valueType: 'single',
        dependencies: ['issueId', 'organization'],
        itemTemplate: '{{type}}: {{value}}',
        url: 'https://de.sentry.io/api/0/organizations/{{organization}}/issues/{{issueId}}/events/recommended/?collapse=fullRelease',
        query: "entries[?type=='exception'] | [0].data.values | [0].{type: type, value: value}",
      },
      stacktrace: {
        type: 'fromRequest',
        variableName: 'Error Description',
        responseType: 'json',
        valueType: 'array',
        dependencies: ['issueId', 'organization'],
        itemTemplate: '{{filename}}:{{lineNo}}',
        joinSymbol: '\n',
        url: 'https://de.sentry.io/api/0/organizations/{{organization}}/issues/{{issueId}}/events/recommended/?collapse=fullRelease',
        query:
          "entries[?type=='exception'] | [0].data.values | [0].stacktrace.frames[*].{filename:filename, lineNo:lineNo}",
      },
      comments: {
        type: 'fromRequest',
        variableName: 'List of Comments',
        responseType: 'json',
        valueType: 'array',
        dependencies: ['issueId', 'organization'],
        itemTemplate: 'Comment {{index}}: {{text}}',
        joinSymbol: '\n',
        url: 'https://de.sentry.io/api/0/organizations/{{organization}}/issues/{{issueId}}/?collapse=release&collapse=tags&expand=inbox&expand=owners',
        query: "activity[?type == 'note'].data.{text:text}",
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please debug the following Sentry error report',
        },
        {
          type: 'text',
          template: 'Error:',
        },
        {
          type: 'codeSnippet',
          template: '{{errorDescription}}',
        },
        {
          type: 'text',
          template: 'Error stacktrace:',
        },
        {
          type: 'codeSnippet',
          template: '{{stacktrace}}',
        },
      ],
    },
  },
  {
    id: 'datadog.resolve_error_report',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'datadoghq.com/error-tracking\\?.*issueId=([^&]+)',
    addButton: {
      triggerSelector: '.error-tracking_issue-details-header > * > *:first-child',
      position: 'afterbegin',
      size: 'NONE',
      styles: {
        textWrap: 'nowrap',
        color: 'rgb(242, 74, 7)',
        fontSize: '13px',
        display: 'block',
        alignSelf: 'center',
        padding: '1px 4px',
        border: '1px solid var(--ui-border)',
        borderRadius: '4px',
      },
      className: 'druids_typography_text',
      content: 'Fix Error',
      title: 'Fix the Error with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '.error-tracking_issue-details',
    },
    variables: {
      issueId: {
        variableName: 'ID of the Issue',
        type: 'fromUrl',
        urlPart: 'search',
        queryName: 'issueId',
      },
      errorDescription: {
        type: 'fromRequest',
        variableName: 'Error Description',
        responseType: 'json',
        valueType: 'single',
        dependencies: ['issueId'],
        itemTemplate:
          '{{type}}: `{{message}}` in function `{{function_name}}` at file `{{file_path}}`',
        url: 'https://app.datadoghq.com/api/ui/error-tracking/issues/{{issueId}}',
        query: 'data.attributes.error',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please fix the DataDog error report',
        },
        {
          type: 'text',
          template: 'Error:',
        },
        {
          type: 'codeSnippet',
          template: '{{errorDescription}}',
        },
      ],
    },
  },
  {
    id: 'azure.devops.resolve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: ['dev.azure.com/.*/edit/[0-9]+', 'dev.azure.com/.*/_workitems/edit/[0-9]+'],
    addButton: {
      triggerSelector: '.work-item-header-command-bar[role="menubar"]',
      position: 'afterbegin',
      styles: {
        margin: '0 8px',
        alignSelf: 'center',
      },
      size: 'L',
      hasLogo: true,
      content: 'Solve with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '.work-item-form-page',
    },
    variables: {
      taskName: {
        type: 'fromPage',
        variableName: 'Azure DevOps Task name',
        valueType: 'single',
        selector: '.work-item-title-textfield input',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'value',
        },
      },
      taskDescription: {
        type: 'fromPage',
        variableName: 'Azure DevOps Task description',
        valueType: 'single',
        selector: '.rooster-editor[aria-label="Description"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the task',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: '.comment-content',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please help me resolve this Azure DevOps work item',
        },
        {
          type: 'text',
          template: 'Task name: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Task description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments: {{comments}}',
        },
      ],
    },
  },
  {
    id: 'gitlab.resolve_issue',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: ['//gitlab.com/.*/issues/[^/]+/?', '//gitlab.com/.*/issues\\?show'],
    addButton: {
      triggerSelector: '[data-testid="work-item-type"]',
      position: 'afterend',
      styles: {
        flex: 'none',
        alignSelf: 'center',
      },
      size: 'L',
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '#content-body',
    },
    variables: {
      issueTitle: {
        variableName: 'Issue Title',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="work-item-title"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      issueDescription: {
        variableName: 'Issue Description',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="work-item-description"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'innerText',
        },
        replace: ['\\s*copy\\s+filepath\\s*', 'gi', ''],
      },
      comments: {
        type: 'fromPage',
        variableName: 'Comments',
        valueType: 'array',
        selector: '[data-testid="work-item-note-body"]',
        extractContentBy: {
          type: 'innerText',
        },
        itemTemplate: 'Comment {{index}}: {{item}}',
        joinSymbol: '\n',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please address the following issue.',
        },
        {
          type: 'text',
          template: 'Issue title: {{issueTitle}}',
        },
        {
          type: 'text',
          template: 'Issue description:',
        },
        {
          type: 'codeSnippet',
          template: '{{issueDescription}}',
        },
        {
          type: 'text',
          template: 'Issue comments:\n{{comments}}',
        },
      ],
    },
  },
  {
    id: 'github.resolve_issue',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '//github.com/.*/issues/[^/]+/?',
    addButton: {
      triggerSelector: '[data-component="PH_Actions"] > div',
      position: 'afterbegin',
      size: 'M',
      styles: {
        textWrap: 'nowrap',
        display: 'block',
        alignSelf: 'center',
        padding: '2px 8px',
        margin: '0 0.5rem',
      },
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '[data-testid="issue-viewer-container"]',
    },
    variables: {
      issueTitle: {
        variableName: 'Issue Title',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="issue-header"] .markdown-title',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      issueDescription: {
        variableName: 'Issue Description',
        valueType: 'single',
        type: 'fromPage',
        selector: '[data-testid="issue-viewer-issue-container"] .markdown-body .markdown-body',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'innerText',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'Comments',
        valueType: 'array',
        selector: '[class^="LayoutHelpers-module__timelineElement"] .markdown-body .markdown-body',
        extractContentBy: {
          type: 'innerText',
        },
        itemTemplate: 'Comment {{index}}: {{item}}',
        joinSymbol: '\n',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please address the following issue.',
        },
        {
          type: 'text',
          template: 'Issue title: {{issueTitle}}',
        },
        {
          type: 'text',
          template: 'Issue description:',
        },
        {
          type: 'codeSnippet',
          template: '{{issueDescription}}',
        },
        {
          type: 'text',
          template: 'Issue comments:\n{{comments}}',
        },
      ],
    },
  },
  {
    id: 'github.resolve_security_advisory',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '//github.com/[^/]+/[^/]+/security/advisories/[^/]+',
    addButton: {
      triggerSelector:
        "//div[contains(@class, 'col-12') and contains(@class, 'col-md-3')][.//div[contains(@class, 'discussion-sidebar-item')][.//h3[text()='Severity']]]",
      position: 'afterbegin',
      size: 'M',
      styles: {
        textWrap: 'nowrap',
        display: 'block',
        alignSelf: 'center',
        padding: '2px 8px',
        margin: '0 0.5rem',
      },
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '.application-main',
    },
    variables: {
      issueTitle: {
        variableName: 'Issue Title',
        valueType: 'single',
        type: 'fromPage',
        selector: '.gh-header .gh-header-title',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      issueDetails: {
        variableName: 'Issue Details',
        valueType: 'array',
        type: 'fromPage',
        selector: '.Details > * > .Box',
        itemTemplate: '```{{item}}```',
        joinSymbol: '\n\n',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'innerText',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please address the following security advisory issue.',
        },
        {
          type: 'text',
          template: 'Issue title: {{issueTitle}}',
        },
        {
          type: 'text',
          template: 'Issue details: \n{{issueDetails}}',
        },
      ],
    },
  },
  {
    id: 'circleci.failed_job',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '//app.circleci.com/pipelines/circleci/.+/workflows/[^/]+/jobs/[^/]+',
    addButton: {
      triggerSelector:
        'div[tabindex="1"]:has(button div[type="FAILED"]) #metadata > div:nth-of-type(2)',
      position: 'afterbegin',
      size: 'M',
      styles: {
        textWrap: 'nowrap',
        display: 'block',
        alignSelf: 'center',
        padding: '2px 8px',
        margin: '0 0.5rem',
      },
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: 'div[tabindex="1"]:has(button)',
    },
    variables: {
      path4: {
        variableName: 'Some ID',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 2,
      },
      path5: {
        variableName: 'Some other ID',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 3,
      },
      path6: {
        variableName: 'Build number (maybe)',
        type: 'fromUrl',
        urlPart: 'pathname',
        index: 4,
      },
      outputURL: {
        type: 'fromRequest',
        variableName: 'Console output URL',
        responseType: 'json',
        valueType: 'single',
        dependencies: ['path4', 'path5', 'path6'],
        itemTemplate: '{{output_url}}',
        url: 'https://circleci.com/api/v1.1/project/circleci/{{path4}}/{{path5}}/{{path6}}',
        query: 'steps[].actions[] | [?failed] | [0].{output_url:output_url}',
      },
      stepName: {
        type: 'fromRequest',
        variableName: 'Console output URL',
        responseType: 'json',
        valueType: 'single',
        dependencies: ['path4', 'path5', 'path6'],
        itemTemplate: '{{name}}',
        url: 'https://circleci.com/api/v1.1/project/circleci/{{path4}}/{{path5}}/{{path6}}',
        query: 'steps[].actions[] | [?failed] | [0].{name:name}',
      },
      errorOutput: {
        type: 'fromRequest',
        variableName: 'Console output',
        responseType: 'json',
        valueType: 'array',
        joinSymbol: '',
        dependencies: ['outputURL'],
        itemTemplate: '{{message}}',
        url: '{{outputURL}}',
        query: '[].{message:message}',
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Here is the output of a failing step in CI.',
        },
        {
          type: 'text',
          template: 'Step name: {{stepName}}',
        },
        {
          type: 'text',
          template: 'The console output:',
        },
        {
          type: 'codeSnippet',
          template: '{{errorOutput}}',
        },
      ],
    },
  },
  {
    id: 'grafana.log_entry',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '\\.grafana\\..*/explore\\?',
    addButton: {
      triggerSelector: 'tr:has(td[aria-label="Log level"]) [aria-label="Fields"]',
      position: 'beforeend',
      size: 'NONE',
      styles: {
        display: 'inline-block',
        textWrap: 'nowrap',
        alignSelf: 'center',
        padding: '2px 6px',
        borderRadius: '2px',
        marginLeft: '10px',
      },
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: 'tr:has(td[aria-label="Log level"])',
    },
    variables: {
      logEntry: {
        type: 'fromPage',
        variableName: 'Log Entry',
        valueType: 'single',
        selector: 'tr:has(td[aria-label="Log level"]) table:has([aria-label="Fields"])',
        extractContentBy: {
          type: 'table',
          itemTemplate: '{{column1}}: {{column2}}',
          joinSymbol: '\n',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template:
            'Here is a Grafala Loki log entry with an issue. Please help investigating what is the cause and provide instructions to address the issue.',
        },
        {
          type: 'text',
          template:
            'Log entry with tags. Focus on the `message` field first and use the rest as supporting data:',
        },
        {
          type: 'codeSnippet',
          template: '{{logEntry}}',
        },
      ],
    },
  },
  {
    id: 'logrocket.browser_console_output',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'app\\.logrocket\\.com/',
    addButton: {
      triggerSelector:
        '*:has([placeholder="Filter log entries"]) > [data-component="TabListContainer"]',
      position: 'beforeend',
      size: 'NONE',
      styles: {
        display: 'inline-block',
        textWrap: 'nowrap',
        alignSelf: 'center',
        padding: '2px 6px',
        borderRadius: '2px',
        marginLeft: '10px',
      },
      content: 'Address with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '*:has([placeholder="Filter log entries"])',
    },
    variables: {
      logEntries: {
        type: 'fromPage',
        variableName: 'Log Entry',
        valueType: 'single',
        isInsideAnchor: true,
        selector: '[role="tabpanel"]',
        extractContentBy: {
          type: 'innerText',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template:
            'Here is a browser console output. Please help investigating what is the cause and provide instructions to address the issue.',
        },
        {
          type: 'codeSnippet',
          template: '{{logEntries}}',
        },
      ],
    },
  },
  {
    id: 'shortcut.resolve_story',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'app.shortcut.com/.*/story/[0-9]+/.*',
    addButton: {
      triggerSelector: "//div[./span[contains(text(), 'Permalink')]]",
      position: 'afterend',
      size: 'M',
      hasLogo: true,
      styles: {
        alignSelf: 'center',
        margin: '8px 0',
      },
    },
    variables: {
      storyTitle: {
        variableName: 'Story Title',
        type: 'fromPage',
        valueType: 'single',
        selector: '.title-container',
        extractContentBy: {
          type: 'textContent',
        },
      },
      storyDescription: {
        variableName: 'Story Description',
        type: 'fromPage',
        valueType: 'single',
        selector: '#story-description-v2 .markdown-formatted',
        extractContentBy: {
          type: 'textContent',
        },
      },
      storyComments: {
        type: 'fromPage',
        variableName: 'List of Recent Comments',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector:
          "//div[@data-type='comment']/div[@class='threaded-comments']//div[@data-comment-id]//div[position() = 2]//div[contains(@class, 'markdown-formatted')]",
        extractContentBy: {
          type: 'textContent',
        },
      },
      storyTasks: {
        type: 'fromPage',
        variableName: 'List of Tasks',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: '.sortable-tasks .task-description .markdown-formatted',
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please solve the Shortcut story',
        },
        {
          type: 'text',
          template: 'Story Title: {{storyTitle}}',
        },
        {
          type: 'text',
          template: 'Story Description: {{storyDescription}}',
        },
        {
          type: 'text',
          template: 'Comments: {{storyComments}}',
        },
        {
          type: 'text',
          template: 'Tasks: {{storyTasks}}',
        },
      ],
    },
  },
  {
    id: 'bugsnag.error_report',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '//app\\.bugsnag\\.com/.+/errors/[^/]+',
    addButton: {
      triggerSelector: '.AssignButton',
      position: 'beforebegin',
      size: 'M',
      content: 'Fix with Zencoder',
    },
    variables: {
      errorTitle: {
        type: 'fromPage',
        variableName: 'Error Title',
        valueType: 'single',
        selector: '[class^="ErrorSummarystyled__Title"]',
        extractContentBy: {
          type: 'innerText',
        },
      },
      errorMessage: {
        type: 'fromPage',
        variableName: 'Error Message',
        valueType: 'single',
        selector: '[class^="ErrorSummarystyled__Message"]',
        extractContentBy: {
          type: 'innerText',
        },
      },
      stacktrace: {
        type: 'fromPage',
        variableName: 'Stack Trace',
        valueType: 'array',
        itemTemplate: '{{item}}',
        joinSymbol: '\n',
        selector: '.Stacktrace-frame',
        extractContentBy: {
          type: 'innerText',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'This is an error in the application. Please help fixing it:',
        },
        {
          type: 'codeSnippet',
          template: '{{errorTitle}}: {{errorMessage}}',
        },
        {
          type: 'text',
          template: 'This is the stack stace for the error:',
        },
        {
          type: 'codeSnippet',
          template: '{{stacktrace}}',
        },
      ],
    },
  },
  {
    id: 'buildkite.console_output',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: '//buildkite\\.com/.+/builds/[^/]+',
    addButton: {
      triggerSelector:
        '.build-details-pipeline-job-body .build-details-pipeline-job-body__controls > ul',
      position: 'beforeend',
      size: 'M',
      content: 'Fix with Zencoder',
    },
    variables: {
      output: {
        type: 'fromPage',
        variableName: 'Console Output',
        valueType: 'single',
        selector:
          '.build-details-pipeline-job-body .JobLogOutputComponent .JobLogOutputComponent__Body',
        extractContentBy: {
          type: 'innerText',
        },
        // clean up timestamps
        replace: ['\\d+s\\r?\\n\\r?\\n', 'gi', ''],
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template:
            'Here is the output of a failing build in CI. Please help analysing and fixing it:',
        },
        {
          type: 'codeSnippet',
          template: '{{output}}',
        },
      ],
    },
  },
  {
    id: 'clickup.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: 'app.clickup.com/t/.*',
    addButton: {
      triggerSelector: '.cu-task-title__text-wrapper',
      position: 'afterend',
      size: 'M',
      hasLogo: false,
      styles: {
        alignSelf: 'center',
        margin: '8px 0',
        width: 'fit-content',
      },
    },
    variables: {
      taskTitle: {
        type: 'fromPage',
        variableName: 'Task Title',
        valueType: 'single',
        selector: '[data-test=task-title__title-overlay]',
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        type: 'fromPage',
        variableName: 'Task Description',
        valueType: 'single',
        selector: '[data-test=task-editor] .ql-editor',
        extractContentBy: {
          type: 'textContent',
        },
      },
      checklist: {
        type: 'fromPage',
        variableName: 'Checklist',
        valueType: 'array',
        selector: "[data-test='checklist-item__name']",
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n',
        extractContentBy: {
          type: 'innerText',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'Comments',
        valueType: 'array',
        selector:
          "//div[contains(@data-test, 'comment-number')]/div[@data-test='comment__body']//div[@data-test='task-comment__body-container']",
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n',
        extractContentBy: {
          type: 'innerText',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please solve this ClickUp task:',
        },
        {
          type: 'text',
          template: 'Task Title: {{taskTitle}}',
        },
        {
          type: 'text',
          template: 'Task Description: {{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Checklist: {{checklist}}',
        },
        {
          type: 'text',
          template: 'Comments: {{comments}}',
        },
      ],
    },
  },
  // YouTrack integration
  {
    id: 'youtrack.solve_task',
    integrationId: IntegrationId.SolveTask,
    urlMatchRegex: [
      // .*\\.youtrack\\.cloud
      // we don't use domain to support self-hosted instances as well
      '/issue/[A-Z]+-[0-9]+.*',
      '/.*issues.*preview=[A-Z]+-[0-9]+.*',
    ],
    addButton: {
      triggerSelector: '//*[@data-test="issue-container"]//*[contains(@class, "ticketNavigation")]',
      position: 'afterend',
      styles: {
        display: 'inline-flex',
      },
      size: 'M',
      hasLogo: true,
      content: 'Solve with Zencoder',
    },
    anchor: {
      isTriggerParent: true,
      selector: '[data-test="issue-container"]',
    },
    variables: {
      taskName: {
        type: 'fromPage',
        variableName: 'YouTrack Issue Title',
        valueType: 'single',
        selector: '[data-test="ticket-summary"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      taskDescription: {
        type: 'fromPage',
        variableName: 'YouTrack Issue Description',
        valueType: 'single',
        selector: '//*[@data-test="ticket-body"]//*[contains(@class, "description")]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
      comments: {
        type: 'fromPage',
        variableName: 'List of recent comments on the issue',
        valueType: 'array',
        itemTemplate: '{{index}}. {{item}}',
        joinSymbol: '\n\n',
        limit: 5,
        selector: '[data-test="comment-content"]',
        isInsideAnchor: true,
        extractContentBy: {
          type: 'textContent',
        },
      },
    },
    messageTemplate: {
      content: [
        {
          type: 'text',
          template: 'Please help me resolve YouTrack issue.',
        },
        {
          type: 'text',
          template: 'Issue title: {{taskName}}',
        },
        {
          type: 'text',
          template: 'Issue description:',
        },
        {
          type: 'codeSnippet',
          template: '{{taskDescription}}',
        },
        {
          type: 'text',
          template: 'Recent comments:',
        },
        {
          type: 'codeSnippet',
          template: '{{comments}}',
        },
      ],
    },
  },
];

export const integrationsConfig: IntegrationsConfig = {
  version: '1',
  configs,
};
