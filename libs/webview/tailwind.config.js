import typography from '@tailwindcss/typography';

module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}', './src/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'secondary-foreground': 'var(--z-secondary-foreground)',
        'hover-background': 'var(--z-hover-background)',
        'secondary-background': 'var(--z-secondary-background)',
        'error-background': 'var(--z-error-background)',
        'editor-widget-border': 'var(--z-editor-widget-border)',
        'primary-background': 'var(--z-primary-background)',
        'primary-foreground': 'var(--z-primary-foreground)',
        'primary-hover': 'var(--z-primary-hover)',
        'primary-active': 'var(--z-primary-active)',
        'destructive-background': 'var(--z-destructive-background)',
        'destructive-foreground': 'var(--z-destructive-foreground)',
        'destructive-hover': 'var(--z-destructive-hover)',
        'destructive-active': 'var(--z-destructive-active)',
        'secondary-button-background': 'var(--z-secondary-button-background)',
        'secondary-button-foreground': 'var(--z-secondary-button-foreground)',
        'secondary-button-hover': 'var(--z-secondary-button-hover)',
        'secondary-button-active': 'var(--z-secondary-button-active)',
        'tabs-background': 'var(--z-tabs-background)',
        'tabs-foreground': 'var(--z-tabs-foreground)',
        'tabs-hover-background': 'var(--z-tabs-hover-background)',
        'disabled-foreground': 'var(--z-disabled-foreground)',
        'editor-background': 'var(--z-editor-background)',
        'editor-foreground': 'var(--z-editor-foreground)',
        'error-foreground': 'var(--z-error-foreground)',
        'warning-foreground': 'var(--z-warning-foreground)',
        foreground: 'var(--z-foreground)',
        'input-background': 'var(--z-input-background)',
        'input-foreground': 'var(--z-input-foreground)',
        'input-placeholder-foreground': 'var(--z-input-placeholder-foreground)',
        'menu-selection-foreground': 'var(--z-menu-selection-foreground)',
        'menu-selection-background': 'var(--z-menu-selection-background)',
        'checkbox-background': 'var(--z-checkbox-background)',
        background: 'var(--z-background)',
        'menu-background': 'var(--z-menu-background)',
        'link-foreground': 'var(--z-text-link-foreground)',
        'modal-dialog-backdrop': 'var(--z-modal-dialog-backdrop)',
        'inserted-line-background': 'var(--z-inserted-line-background)',
        'removed-line-background': 'var(--z-removed-line-background)',
        'icon-yellow': '#cbcb41',
        'error-button-background': '#C7000A',
        'error-button-active-background': '#8B000A',
      },
      fontSize: {
        xs: ['10px', '1.2'],
        sm: ['12px', '1.3'],
        base: ['13px', '1.4'],
        lg: ['16px', '1.4'],
        xl: ['20px', '1.4'],
      },
      fontFamily: {
        editor: 'var(--z-editor-font-family)',
        bricolage: 'Bricolage-Grotesque',
        jetbrains: 'JetBrains Mono',
      },
      transitionProperty: {
        height: 'height',
        'max-height': 'max-height',
        spacing: 'margin, padding',
      },
      screens: {
        '3xs': '150px',
        '2xs': '270px',
        xs: '400px',
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
      },
      boxShadow: {
        menu: '0px 2px 16px 0px rgba(0,0,0,0.36)',
        'disabled-box': 'inset 0 0 0 1px var(--z-editor-widget-border)',
      },
      animation: {
        'progress-bar-bouncing': 'progressBarBouncing 1s ease-in infinite alternate-reverse',
        wave: 'wave 1.5s ease-in-out infinite',
      },
      keyframes: {
        progressBarBouncing: {
          '0%': { height: '0.5rem', top: 0 },
          '50%': { height: '100%', top: 0 },
          '100%': { height: '0.5rem', top: 'calc(100% - 0.5rem)' },
        },
        wave: {
          '0%, 100%': {
            opacity: 0.1,
          },
          '50%': {
            opacity: 0.3,
          },
        },
      },
    },
  },
  variants: {
    extend: {},
  },
  plugins: [typography],
};
