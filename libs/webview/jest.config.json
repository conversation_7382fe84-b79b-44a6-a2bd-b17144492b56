{"testMatch": ["<rootDir>/src/**/?(*.)+(spec|test).[jt]s?(x)"], "transform": {"^.+\\.tsx?$": ["ts-jest", {}]}, "testEnvironment": "jsdom", "testPathIgnorePatterns": ["<rootDir>/.tmp"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "moduleNameMapper": {"@src/(.*)": "<rootDir>/src/$1", "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "identity-obj-proxy", "\\.(css|less|scss|sass)$": "identity-obj-proxy"}}