import React from 'react';
import ReactDOM from 'react-dom/client';
import './analytics';
import App from './app';
import { AppState } from './models/app-state.model';

import './index.css';

export interface WebviewApi<StateType> {
  postMessage(message: unknown): void;

  getState(): StateType | undefined;

  setState<T extends StateType | undefined>(newState: T): T;
}

export const ideApi = acquireVsCodeApi<AppState>();

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

export * from './models/settings.model';
