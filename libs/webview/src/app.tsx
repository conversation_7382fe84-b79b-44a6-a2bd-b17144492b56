import BannerView from '@src/components/banner/banner.view';
import { createContext, useEffect } from 'react';
import { useThemes } from './hooks/use-theme';
import { RouterView } from './router.view';
import { AppStoreProvider } from './store/app.store';

export const ThemeContext = createContext<Record<string, unknown> | undefined>(undefined);

function App() {
  const { codeSnippetTheme, globalTheme } = useThemes();
  useEffect(() => {
    if (!globalTheme) return;
    Object.entries(globalTheme).forEach(([name, value]) => {
      document.documentElement.style.setProperty(`--z-${name}`, value);
    });
  });
  return (
    <ThemeContext.Provider value={codeSnippetTheme}>
      <AppStoreProvider>
        <div className={'h-full flex flex-col'}>
          <BannerView />
          <div className={'flex-1 overflow-auto'}>
            <RouterView />
          </div>
        </div>
      </AppStoreProvider>
    </ThemeContext.Provider>
  );
}

export default App;
