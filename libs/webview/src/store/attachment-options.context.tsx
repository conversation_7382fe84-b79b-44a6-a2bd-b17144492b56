import { useToggleFeatureFlag } from '@src/hooks/feature-flags/use-toggle-feature-flag';
import { createContext, PropsWithChildren } from 'react';

export type AttachmentOptions = {
  allowImages: boolean;
};

export const AttachmentOptionsContext = createContext<AttachmentOptions>({
  allowImages: false,
});

export const AttachmentOptionsProvider = ({
  allowImages,
  children,
}: PropsWithChildren<{ allowImages: boolean }>) => {
  const isFeatureEnabled = useToggleFeatureFlag('enableImageAttachments', 'off');
  const finalAllowImages = isFeatureEnabled === 'on' && allowImages;

  return (
    <AttachmentOptionsContext.Provider value={{ allowImages: finalAllowImages }}>
      {children}
    </AttachmentOptionsContext.Provider>
  );
};
