import { BannerType } from '@src/models/banner.model';
import {
  createContext,
  Di<PERSON>atch,
  PropsWithChildren,
  useContext,
  useEffect,
  useReducer,
} from 'react';
import { ideApi } from '../index';
import { Action } from '../models/action';
import { AppState } from '../models/app-state.model';

const INITIAL_STATE: AppState = {};

interface AppStore {
  state: AppState;
  dispatch: Dispatch<Action>;
}

const AppStoreContext = createContext<AppStore>({
  state: INITIAL_STATE,
  dispatch: () => {},
});

export function AppStoreProvider({ children }: PropsWithChildren) {
  const [state, dispatch] = useReducer(appReducer, INITIAL_STATE);

  useEffect(() => {
    ideApi.setState(state);
  }, [state]);

  return (
    <AppStoreContext.Provider value={{ state, dispatch }}>{children}</AppStoreContext.Provider>
  );
}

export function useAppStore(): AppStore {
  return useContext(AppStoreContext);
}

function appReducer(state: AppState, action: Action): AppState {
  switch (action.type) {
    case 'set-state':
      return action.state;
    case 'set-settings':
      return {
        ...state,
        settings: action.settings,
      };
    case 'reset-state':
      const { settings } = state;
      return {
        ...INITIAL_STATE,
        ...(settings ? { settings } : {}),
      };
    case 'show-unit-tests-progress':
      return {
        ...state,
        unitTestsTask: {
          stage: 'loading',
          actionId: action.actionId,
          symbolName: action.symbolName,
          progressDetails: action.progressDetails,
        },
      };
    case 'init-generate-unit-tests':
      return {
        ...state,
        unitTestsTask: {
          stage: 'select',
          actionId: action.actionId,
          symbolName: action.symbolName,
          bulletPoints: action.bulletPoints,
        },
      };
    case 'set-command-context':
      return {
        ...state,
        commandContext: {
          symbolName: action.symbolName,
          context: action.context,
        },
      };
    case 'set-keybinding':
      return {
        ...state,
        keybindings: {
          ...state.keybindings,
          [action.keybinding.command]: action.keybinding,
        },
      };
    case 'set-active-chat':
      if (
        !action.overrideCurrent &&
        state.activeChat?.id &&
        state.activeChat.id !== action.chat.id
      ) {
        // there can be streaming messages from already inactive chat
        return state;
      }

      return {
        ...state,
        activeChat: {
          ...action.chat,
          isStreaming: action.isStreaming,
          hasClientOperations: action.hasClientOperations ?? state.activeChat?.hasClientOperations,
          warning: action.warning,
        },
      };
    case 'set-clear-chats-dialog-shown':
      return {
        ...state,
        clearChatsDialogShown: action.isShown,
      };
    case 'set-chat-sessions':
      return {
        ...state,
        chatSessions: action.chatSessions,
      };
    case 'set-custom-agents':
      return {
        ...state,
        customAgents: action.customAgents,
      };
    case 'set-repo-index-data':
      return {
        ...state,
        repoIndexData: action.repoIndexData,
      };
    case 'set-models-settings':
      return {
        ...state,
        modelsSettings: action.settings,
      };
    case 'set-jira-issues-search-result':
      return {
        ...state,
        jiraIssues: action.searchResult && {
          query: action.searchResult.query,
          issues: action.searchResult.issues,
        },
      };
    case 'set-custom-instructions':
      return {
        ...state,
        userInstructions: action.userInstructions,
      };
    case 'set-chat-memories':
      return {
        ...state,
        chatMemories: {
          areEnabled: state.chatMemories?.areEnabled ?? false,
          ...(typeof action.areEnabled === 'boolean' ? { areEnabled: action.areEnabled } : {}),
          items: action.items,
        },
      };
    case 'set-active-file':
      return {
        ...state,
        activeFile: action.activeFile,
      };
    case 'set-integrations':
      return {
        ...state,
        integrations: action.integrations,
      };
    case 'set-latest-version-installed':
      return {
        ...state,
        isLatestVersionInstalled: action.value,
      };
    case 'set-banner-state':
      let newBannerState = { ...state.banner };
      switch (action.state.type) {
        case BannerType.UPDATE:
          break;
        case BannerType.PRODUCT_HUNT:
          newBannerState.productHunt = action.state.state;
          break;
        default:
          action.state satisfies never;
          break;
      }
      return {
        ...state,
        banner: newBannerState,
      };
    case 'set-file-select-for-mention':
      return {
        ...state,
        fileSelectForMention: {
          ...state.fileSelectForMention,
          [action.requestId]: action.file,
        },
      };
    case 'set-shell-tool-config':
      return {
        ...state,
        shellToolConfig: action.config,
      };
    case 'set-feature-flags-variants':
      return {
        ...state,
        featureFlagsVariants: action.variants,
      };
    case 'set-agent-tools':
      return {
        ...state,
        agentTools: action.payload,
      };
  }
}
