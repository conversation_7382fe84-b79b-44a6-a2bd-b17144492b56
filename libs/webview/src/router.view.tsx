import * as Sentry from '@sentry/react';
import { ModelsSettingsView } from '@src/components/models-settings/models-settings.view';
import { NewChatView } from '@src/components/new-chat-view/new-chat.view';
import { SignInView } from '@src/components/sign-in-view/sign-in.view';
import { useCallback, useEffect } from 'react';
import {
  createMemoryRouter,
  Outlet,
  RouterProvider,
  useLocation,
  useNavigate,
} from 'react-router-dom';
import { analytics } from './analytics';
import { AgentToolsView } from './components/agent-tools-view/agent-tools.view';
import { AgentToolEditView } from './components/agent-tools-view/tool-edit.view';
import { ChatHistoryView } from './components/chat-history-view/chat-history.view';
import CustomAgentsView from './components/chat-settings/custom-agents-view/custom-agents.view';
import EditCustomAgentsView from './components/chat-settings/custom-agents-view/edit-custom-agents.view';
import ViewLibraryAgentView from './components/chat-settings/custom-agents-view/library-agent.view';
import { CustomInstructionsView } from './components/chat-settings/custom-instructions.view';
import { MemoriesSettingsView } from './components/chat-settings/memories-settings.view';
import { RepoIndexSettingsView } from './components/chat-settings/repo-index/repo-index-settings.view';
import { ChatView } from './components/chat-view/chat.view';
import { CommandContextView } from './components/command-context-view/command-context.view';
import { ErrorPage } from './components/error-page/error-page.view';
import { IntegrationsView } from './components/integrations/integrations.view';
import { BaseLoaderView } from './components/loaders/base-loader.view';
import { ZenLoaderView } from './components/loaders/zen-loader.view';
import { StartupScreenView } from './components/startup-screen-view/startup-screen.view';
import { UiKitDemo } from './components/ui-kit/ui-kit-demo';
import { UnitTestsView } from './components/unit-tests-view/unit-tests.view';
import { ForgoodDaemon } from './forgood.daemon';
import { useCurrentRoute } from './hooks/use-current-route';
import { useDebounce } from './hooks/use-debounce';
import { initialPaths, Routes } from './models/router';
import { useAppStore } from './store/app.store';

function RootView() {
  const { state } = useAppStore();
  const { settings, chatSessions } = state;
  const navigate = useNavigate();
  const location = useLocation();

  const trackAnalyticsPage = useCallback(
    ({
      routeId,
      analyticsScreenName,
    }: {
      routeId: Routes | null;
      analyticsScreenName: string | null;
    }) => {
      const props: Record<string, unknown> = {};

      if (analyticsScreenName) {
        if (routeId === Routes.chatHistory) {
          props.chats_num = chatSessions?.length;
        }

        analytics.track('Screen opened', { screen: analyticsScreenName, ...props });
      }
    },
    [chatSessions?.length]
  );
  // debounce for cases when we have multiple page changes in a row
  const debouncedAnalyticsPage = useDebounce(trackAnalyticsPage, 500);

  // TODO: refactor to use react-router's loaders
  useEffect(() => {
    if (!settings) {
      return;
    }
    const doNavigate = (path: string) => {
      if (location.pathname === `/${path}`) {
        return;
      }
      navigate(path, { replace: true });
    };
    if (!settings.isAuthenticated) {
      doNavigate(Routes.signIn);
      return;
    }
    if (initialPaths.includes(location.pathname)) {
      const route = settings.showChatByDefault ? Routes.chatHistory : Routes.startScreen;
      doNavigate(route);
    }
  }, [settings, location, navigate]);

  const currentRoute = useCurrentRoute();

  useEffect(() => {
    if (currentRoute) {
      debouncedAnalyticsPage(currentRoute);
    }
  }, [currentRoute, debouncedAnalyticsPage]);

  return (
    <>
      <ForgoodDaemon />
      {/*No settings mean application is not configured yet*/}
      {settings ? <Outlet /> : <BaseLoaderView />}
    </>
  );
}

const router = Sentry.wrapCreateMemoryRouterV6(createMemoryRouter)(
  [
    {
      path: '/',
      element: <RootView />,
      errorElement: <ErrorPage />,
      children: [
        {
          id: Routes.startScreen,
          path: Routes.startScreen,
          element: <StartupScreenView />,
        },
        {
          id: Routes.customAgents,
          path: Routes.customAgents,
          element: <CustomAgentsView />,
        },
        {
          id: Routes.editCustomAgent,
          path: `${Routes.editCustomAgent}/:agentId`,
          element: <EditCustomAgentsView />,
        },
        {
          id: Routes.viewLibraryAgent,
          path: `${Routes.viewLibraryAgent}/:agentId`,
          element: <ViewLibraryAgentView />,
        },
        {
          id: Routes.signIn,
          path: Routes.signIn,
          element: <SignInView />,
        },
        {
          id: Routes.chatHistory,
          path: Routes.chatHistory,
          element: <ChatHistoryView />,
        },
        {
          id: Routes.newChat,
          path: Routes.newChat,
          element: <NewChatView />,
        },
        {
          id: Routes.repoIndexSettings,
          path: Routes.repoIndexSettings,
          element: <RepoIndexSettingsView />,
        },
        {
          id: Routes.memoriesSettings,
          path: Routes.memoriesSettings,
          element: <MemoriesSettingsView />,
        },
        {
          id: Routes.customInstructions,
          path: Routes.customInstructions,
          element: <CustomInstructionsView />,
        },
        {
          id: Routes.chat,
          path: `${Routes.chat}/:chatId`,
          element: <ChatView />,
        },
        {
          id: Routes.contextViewer,
          path: Routes.contextViewer,
          element: <CommandContextView />,
        },
        {
          id: Routes.zenLoader,
          path: Routes.zenLoader,
          element: <ZenLoaderView />,
        },
        {
          id: Routes.unitTestsViewer,
          path: Routes.unitTestsViewer,
          element: <UnitTestsView />,
        },
        {
          id: Routes.uiKitDemo,
          path: Routes.uiKitDemo,
          element: <UiKitDemo />,
        },
        {
          id: Routes.modelsSettings,
          path: Routes.modelsSettings,
          element: <ModelsSettingsView />,
        },
        {
          id: Routes.integrations,
          path: Routes.integrations,
          element: <IntegrationsView />,
        },
        {
          id: Routes.agentTools,
          path: Routes.agentTools,
          element: <AgentToolsView />,
        },
        {
          id: Routes.agentToolEdit,
          path: `${Routes.agentToolEdit}/:toolId`,
          element: <AgentToolEditView />,
        },
      ],
    },
  ],
  {
    initialIndex: 0,
    initialEntries: [`/${Routes.startScreen}`],
  }
);

export function RouterView() {
  return <RouterProvider router={router} />;
}
