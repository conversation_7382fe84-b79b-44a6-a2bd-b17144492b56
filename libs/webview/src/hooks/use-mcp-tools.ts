import { useAppStore } from '@src/store/app.store';
import { useEffect, useMemo } from 'react';
import { ideApi } from '..';

export function useMcpTools() {
  const { state } = useAppStore();

  useEffect(() => {
    ideApi.postMessage({ type: 'getAgentToolsState' });
  }, []);

  const agentTools = state.agentTools?.tools ?? {};

  const agentToolsWithIcons = useMemo(() => {
    for (const tool of Object.values(agentTools)) {
      if (tool.name === 'Full Text Search') {
        tool.icon =
          'iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMESURBVHgB7ZlNdhJBEMf/NRhAzQdZusMTmBuY7HTQFzxByMZt8nKBJBcwyTab4AkkL0rcJZ5AbhBcZSkCz4DItF35eBkmSM8wkhmwfhuYpmped81Udf0bQBAEQRAEQRCE/xEyGdRfPnpN6G6AaAZjhII6h0rszx7/PBpkZ8EEdd+O2+IZAj0hOBsmO2MASNE0xhSHqGmyMQZAwXrngM4xZiiFhn54+xAEQRAEQfgLogVgQrSAaAHRAoIgCIIgTCQj1wJ+e/Ien3wmU+9cFEjhuQIW9CSzV/dCRX+v6mkfzpZbRfwDzAGwU0fcVyMMuiubOW4t+THVAc/D+n0ARRmDaVVPfztsIO5FC/jpyZlGLrWjxdcHH4tnsjqyBw07uYkQjFwL+O3JG3Z6U9uuIyAKtNXITe1gSIwpcB/U7XSBn6Z7TJGqQVl7loXTmY+tUx5r2lMLXZVYJ1Ird26iEm90nSkhIDEJQOoM14WO0cWu6ji0NP+5Ve1n//1FOmtZ6oRcPppaN/nw6XypVkMAzAciI+bq6fcsBIMWz/BvbAN+S27JWO2LAgISeQBgqeXeASoOWvwNbKN0ivS6YhEBiTwAeq/Puq+7SOz59eX60HMv4BkCEnkN0Pmv3Nez5XagOYX1jz4FIiYOAai6Lxqv0ovwSTP/eMF9rdvuCgLywGQwci1goQLntg44jrOoP07hA6fTWevNYvqGgIz8fwHT+bzu/r547Ncu8uksDFzaKCp4fAM3QpFrAWeqXfTu551f6mRQEPg3tnGPcfM0jDCKXAvMl1DTs1j1DPMCz+q55EHTvs1zrg8sfjod5ys8zZPmEEMQi1aY+WGndvVk1hACi6gw/an1PpAPYsJcub2uld02QuAoVWzm0itBfGLVB8yVW1v6pVxVnq2xH6wWtd2drjFoEGKTAl6uJfKyImR1u3xZBzgwesIVPhLrJtMlVn79pDTjNx1iG4AghAnCRLTCV9sfeXcSToddk+/EaIF+QdApYzwcmSgxdBMErhVcJCnkriIIwuTzB0BKV/Rp/KExAAAAAElFTkSuQmCC';
      } else if (tool.name === 'File Search') {
        tool.icon =
          'iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAScSURBVHgB7Zs9UBtHFMf/b/mQgi0MM6aIK6VJG2ecPrgLksG0nhSRG6eEidML+iS2y9CAq5TBDiiZSWHShwl1GsuNKewZy5IsTgjtevcQ47sD63bvA52t+80wsHu73O3/3r7dfbsHpKSkDDOEAIiFy7kmb17jHLMy+bnMyTGBiyDKISIExD7EyNrkn60/ECPGAjRuZGfRFeUoG9sPIdhqnCIYCVCfy9wlols4Z+IUgekWbMxl7wyi8QoiXq7PTcwjBrQsQL75edn4sjNPCDSI4TcGtosR7Lcw0Zh5/LKBkDQK2X/fdy0OSxjVKkW440xy0P7hGH0/87j1/F1uC3HTswREKYJvF7DfPuhTZ97hqLfx50fU3cFXAEF0zZnmwNagGn9ClCL4CkACrrc/SizWcVmXqETwHwVIXHEmW9LhISFEIYK/BXj6/6DN30tYEbTnAUkmjAjJE0DOLxCAoCIkTgAB/I+AEPgPMCRxArSP2GpQK5AKGC/QEifAzN+t59YR+1aQ2ME5oDcVPmeUCPLXjzpl+60ddPgoRoEwpAJgyEkFwJCTCoAhJxUAQ86HL4Bj3aCCtTDkgxdAgP2iGm6H6QWtwZBErgVM6IXIA8cpUx+AIWfoBUikDxCLU1P1zkFJ7kl8LUNkV+UGZt7OB/bk31UZ+nk0WbE2EAG+m6PegEOuYn2FGJGBzUWwo3W5JTXlU7QqH381rBCJ6gKNYuYeqPu7RuMVeWkT643CeBkhSIwA0tLKcixfhiECtNIojt1DQBIhQL2QLQmIFWeeDIrWZONWidH1yUqb1A8D/1IIeuitLwRbtrtOABLhA+qFzFP0HJ1COrsq53R9+i+relb5V99k84yJJ+SoI6l1xz/5bHqzVoMBA7cA9fbhbgj6NV6hrqkycuPW2dgp1j4owZDBdwEmbrozaKNf409QZaTpP3BXtY/tmd0eA0aO9XlnuouRB7p1GcOO638BX8CQgQsgPf9VZ3q68mZPsypyW9aOJysPQ9K1AAZP1ZmwT6Jq0ly84LIeOZRqW88JCXCCcD0053wWmvBOZ8mdQ89giL8Anq3qFwuXIz0jLH3AP840gZYOFrN5v3p2GUElT91NGKJxTE403TmtSAXgY+0N73jeORRP+omgrqkyzjw1eQqyMPI/JEXksoCJrvvYXFimN1GTT3Hbk60a+LReHF9vFt71c+Uf1OKn0+H/4bTHf4QA+E6FXxeyK1KlG44aa7ltyzj46H+fzH35MEsIASMqXdy2HhrV8S3gmWzI2POtNwsTVxAxlyrtZbX4QQi4EBvNYvY7kzq+AljM2nU5QkKue9T9NR4RrBV5g9vCMzSexfFqEadmjaYiaB2Xt8dmjp9O3QzYso/OjmD/QsQHKI8XSeKmIOSpN1tUwsgH3lMhse54dlOt/Hrl1r31dbuD9hcj9WLmrtx4CP3BRBzfAoURQXsiNLnd/lk5QIREHb0Ncp6vH8fDH3lHEtUd7vvVNZoJKu+vvtoIsgfnhBM1ETFniSC7jG9wxHgqrEz3UuVg/lgIbMk+usuVWWsSdA9P69l6IihfoZwkhRxVUlJSPn7eAitD2scmGxshAAAAAElFTkSuQmCC';
      } else if (!tool.icon && tool.author?.toLowerCase().startsWith('zencoder')) {
        tool.icon =
          '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';
      }
    }

    return agentTools;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(agentTools)]);

  return {
    isLoading: state.agentTools?.isLoading ?? true,
    tools: agentToolsWithIcons,
    error: state.agentTools?.error,
  };
}
