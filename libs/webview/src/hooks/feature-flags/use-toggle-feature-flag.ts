import { z } from 'zod';
import { useFeatureFlagValue } from './use-feature-flag-value';

const toggleFeatureFlagSchema = z.enum(['on', 'off']);

export type ToggleFeatureFlag = typeof toggleFeatureFlagSchema._type;

export const useToggleFeatureFlag = (featureFlagKey: string, defaultValue: ToggleFeatureFlag) => {
  const featureFlagValue = useFeatureFlagValue<ToggleFeatureFlag>({
    featureFlagKey: featureFlagKey,
    schema: toggleFeatureFlagSchema,
    defaultValue: defaultValue,
  });

  return featureFlagValue;
};
