import { v4 as uuidv4 } from 'uuid';
import { ideApi } from '.';

// Store both the promise and its resolve/reject functions
interface PromiseControl<T = unknown> {
  promise: Promise<T>;
  resolve: (value: T) => void;
  reject: (reason?: Error) => void;
}

const promisesMap = new Map<string, PromiseControl>();

export function sendRequestToIde<R = unknown, D = unknown>(
  data: D,
  timeoutMs: number = 5000 // Default timeout of 5 seconds
): Promise<R> {
  let resolveFunction!: (value: R) => void;
  let rejectFunction!: (reason?: unknown) => void;
  let timeoutId: NodeJS.Timeout;

  const requestKey = uuidv4();

  const promise = new Promise<R>((resolve, reject) => {
    ideApi.postMessage({
      ...data,
      requestKey,
    });

    resolveFunction = resolve;
    rejectFunction = reject;

    // Set timeout to reject promise if no response is received
    timeoutId = setTimeout(() => {
      const error = new Error(`Request to IDE timed out after ${timeoutMs}ms`);
      reject(error);
      promisesMap.delete(requestKey);
    }, timeoutMs);
  });

  const control: PromiseControl<R> = {
    promise,
    resolve: (value: R) => {
      clearTimeout(timeoutId);
      resolveFunction(value);
    },
    reject: (reason?: Error) => {
      clearTimeout(timeoutId);
      rejectFunction(reason);
    },
  };

  (promisesMap as Map<string, PromiseControl<R>>).set(requestKey, control);

  return promise;
}

window.addEventListener('message', (message) => {
  if (message.data && typeof message.data === 'object' && 'requestKey' in message.data) {
    const data = message.data as {
      requestKey: string;
    } & (
      | {
          result: unknown;
          error: never;
        }
      | {
          error: string;
          result: never;
        }
    );

    const promiseControl = promisesMap.get(data.requestKey);
    if (promiseControl) {
      if (data.error) {
        promiseControl.reject(new Error(data.error));
      } else {
        promiseControl.resolve(data.result);
      }

      promisesMap.delete(data.requestKey);
    }
  }
});
