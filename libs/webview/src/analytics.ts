import { RudderAnalytics } from '@rudderstack/analytics-js';
import * as Sentry from '@sentry/react';
import React from 'react';
import {
  createRoutesFromChildren,
  matchRoutes,
  useLocation,
  useNavigationType,
} from 'react-router-dom';
import { WebviewAnalyticsOptions, WebviewSentryOptions } from './models/settings.model';

export const analytics = new RudderAnalytics();

export let pluginFeatureFlags: {
  enableCoffeeMode?: boolean;
} = {};

const dataset = document.getElementById('webview-settings')?.dataset;

if (dataset) {
  const sentryOptions = dataset.sentry_options
    ? (JSON.parse(dataset.sentry_options) as WebviewSentryOptions)
    : null;
  const analyticsOptions = dataset.analytics_options
    ? (JSON.parse(dataset.analytics_options) as WebviewAnalyticsOptions)
    : null;

  if (sentryOptions && !Sentry.isInitialized()) {
    Sentry.init({
      ...sentryOptions,
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.reactRouterV6BrowserTracingIntegration({
          useEffect: React.useEffect,
          useLocation,
          useNavigationType,
          createRoutesFromChildren,
          matchRoutes,
        }),
      ],
    });
  }

  if (analyticsOptions?.isEnabled && analyticsOptions.writeKey && analyticsOptions.dataPlaneURL) {
    // do it first to avoid cleaning up user id for current user
    analytics.identify(analyticsOptions.userId ?? '');

    analytics.load(analyticsOptions.writeKey, analyticsOptions.dataPlaneURL);
    analytics.setAnonymousId(analyticsOptions.anonymousId);
  } else {
    analytics.track = wrapToLogger('Track event', analytics.track);
    analytics.identify = wrapToLogger('Identify', analytics.identify);
  }

  if (dataset.feature_flags) {
    pluginFeatureFlags = JSON.parse(dataset.feature_flags);
  }
}

function wrapToLogger<F extends (...args: any[]) => unknown>(logName: string, fn: F) {
  return ((...args) => {
    console.log(`Analytics - ${logName}: `, ...args);
    return fn(...args);
  }) as F;
}
