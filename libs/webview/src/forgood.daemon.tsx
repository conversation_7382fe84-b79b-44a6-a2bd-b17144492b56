import * as Sentry from '@sentry/react';
import { useCallback, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { analytics } from './analytics';
import { forceWindowEvent } from './hooks/use-window-event';
import { ideApi } from './index';
import { Routes } from './models/router';
import { Settings } from './models/settings.model';
import { useAppStore } from './store/app.store';

declare global {
  interface Window {
    __zencoderDaemonInitialised?: boolean;
  }
}

export function ForgoodDaemon() {
  const { dispatch, state: storeState } = useAppStore();

  const navigate = useNavigate();
  const location = useLocation();

  const processWindowMessage = useCallback(
    (event: MessageEvent) => {
      const message = event.data; // The json data that the extension sent
      if (message.isForcedWindowEvent) {
        // Let's filter out forceWindowEvent executions
        return;
      }

      switch (message.type) {
        case 'setSettings': {
          const settings = message.settings as Settings;

          Sentry.setUser(settings.userId ? { id: settings.userId } : null);
          analytics.identify(settings.userId ?? '');

          dispatch({ type: 'set-settings', settings });
          break;
        }
        case 'showSignInPage': {
          navigate(Routes.signIn);
          break;
        }
        case 'showRepoIndexSettingsPage': {
          navigate(Routes.repoIndexSettings);
          break;
        }
        case 'showModelsSettingsPage': {
          navigate(Routes.modelsSettings);
          break;
        }
        case 'showIntegrationsPage': {
          navigate(Routes.integrations);
          break;
        }
        case 'showCustomInstructionsPage': {
          navigate(Routes.customInstructions);
          break;
        }
        case 'showMemoriesSettingsPage': {
          navigate(Routes.memoriesSettings);
          break;
        }
        case 'showChatSessionPage': {
          navigate(`/${Routes.chat}/${message.chatId}`);
          break;
        }
        case 'showNewChatPage': {
          navigate(`/${Routes.newChat}`);
          break;
        }
        case 'showCustomAgentsPage': {
          navigate(Routes.customAgents);
          break;
        }
        case 'showZenLoaderPage': {
          navigate(Routes.zenLoader);
          break;
        }
        case 'showAgentToolsPage': {
          navigate(Routes.agentTools);
          break;
        }
        case 'resetState': {
          dispatch({ type: 'reset-state' });
          break;
        }
        case 'showUnitTestsProgress': {
          dispatch({
            type: 'show-unit-tests-progress',
            actionId: message.actionId,
            symbolName: message.symbolName,
            progressDetails: message.progressDetails,
          });
          navigate(Routes.unitTestsViewer);
          break;
        }
        case 'selectBulletPoints': {
          dispatch({
            type: 'init-generate-unit-tests',
            actionId: message.actionId,
            symbolName: message.symbolName,
            bulletPoints: message.bulletPoints,
          });
          navigate(Routes.unitTestsViewer);
          break;
        }
        case 'showCommandContext': {
          dispatch({
            type: 'set-command-context',
            symbolName: message.symbolName,
            context: message.context,
          });
          navigate(Routes.contextViewer);
          break;
        }
        case 'setKeybinding': {
          dispatch({
            type: 'set-keybinding',
            keybinding: message.keybinding,
          });
          break;
        }
        case 'setActiveChat': {
          dispatch({
            type: 'set-active-chat',
            chat: message.chat,
            isStreaming: message.isStreaming,
            hasClientOperations: message.hasClientOperations,
            warning: message.warning,
            overrideCurrent: message.navigateTo,
          });
          if (message.navigateTo) {
            navigate(`/${Routes.chat}/${message.chat?.id}`);
          }
          break;
        }
        case 'setChatSessions': {
          dispatch({
            type: 'set-chat-sessions',
            chatSessions: message.chatSessions,
          });
          break;
        }
        case 'setClearChatsHistoryDialogShown': {
          dispatch({
            type: 'set-clear-chats-dialog-shown',
            isShown: message.isShown,
          });

          break;
        }
        case 'setCustomAgents': {
          dispatch({
            type: 'set-custom-agents',
            customAgents: message.customAgents,
          });
          break;
        }
        case 'setRepoIndexData': {
          dispatch({
            type: 'set-repo-index-data',
            repoIndexData: message.repoIndexData,
          });
          break;
        }
        case 'setModelsSettings': {
          dispatch({
            type: 'set-models-settings',
            settings: message.settings,
          });
          break;
        }
        case 'setIntegrations': {
          dispatch({
            type: 'set-integrations',
            integrations: message.integrations,
          });
          break;
        }
        case 'jiraToWebview': {
          if (message.payload.type === 'searchIssueResult') {
            dispatch({
              type: 'set-jira-issues-search-result',
              searchResult: {
                query: message.payload.query,
                issues: message.payload.issues,
              },
            });
          }
          break;
        }
        case 'setCustomInstructions': {
          dispatch({
            type: 'set-custom-instructions',
            userInstructions: message.userInstructions,
          });
          break;
        }
        case 'setMemoriesData': {
          dispatch({
            type: 'set-chat-memories',
            areEnabled: message.areEnabled,
            items: message.items,
          });
          break;
        }
        case 'showChatHistoryPage': {
          navigate(Routes.chatHistory);
          break;
        }
        case 'showUiKitDemo': {
          navigate(Routes.uiKitDemo);
          break;
        }
        case 'insertIntoChat': {
          if (
            !location.pathname.startsWith(`/${Routes.chat}/`) &&
            !location.pathname.startsWith(`/${Routes.newChat}`)
          ) {
            navigate(`/${Routes.newChat}`);
            // Let's wait for ChatHistory to render and be ready to accept insertions
            setTimeout(() => {
              forceWindowEvent('insertIntoChat', message);
            }, 1);
          }
          break;
        }
        case 'setActiveFile': {
          dispatch({
            type: 'set-active-file',
            activeFile: message.activeFile,
          });
          break;
        }
        case 'setIsLatestVersionInstalled': {
          dispatch({
            type: 'set-latest-version-installed',
            value: message.value,
          });
          break;
        }
        case 'setBannerState': {
          dispatch({
            type: 'set-banner-state',
            state: message.state,
          });
          break;
        }
        case 'fileSelectedForMention': {
          dispatch({
            type: 'set-file-select-for-mention',
            requestId: message.requestId,
            file: message.file,
          });
          break;
        }
        case 'setShellToolConfig': {
          dispatch({
            type: 'set-shell-tool-config',
            config: message.config,
          });
          break;
        }
        case 'setFeatureFlagsVariants': {
          dispatch({
            type: 'set-feature-flags-variants',
            variants: message.variants,
          });
          break;
        }
        case 'setAgentToolsState': {
          dispatch({
            type: 'set-agent-tools',
            payload: message.payload,
          });
          break;
        }
      }
    },
    [dispatch, navigate, location]
  );

  useEffect(() => {
    if (!storeState.featureFlagsVariants) {
      ideApi.postMessage({
        type: 'getFeatureFlagsVariants',
      });
    }
  }, [storeState.featureFlagsVariants]);

  useEffect(() => {
    window.addEventListener('message', processWindowMessage);

    return () => {
      window.removeEventListener('message', processWindowMessage);
    };
  }, [processWindowMessage]);

  useEffect(() => {
    if (window.__zencoderDaemonInitialised) {
      // Bugfix - somehow this window is being rendered twice
      return;
    }
    window.__zencoderDaemonInitialised = true;

    const state = ideApi.getState();
    if (state) {
      dispatch({ type: 'set-state', state });
    }

    ideApi.postMessage({ type: 'startup' });
  }, [dispatch]);

  return <></>;
}
