// Mock the entire index module to avoid SVG import issues
import * as indexModule from '..';
import { sendRequestToIde } from '../ideRequests';

jest.mock(
  '..',
  () => ({
    ideApi: {
      postMessage: jest.fn(),
      getState: jest.fn(),
      setState: jest.fn(),
    },
  }),
  { virtual: true }
);

// Mock the uuid module
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid-value'),
}));

describe('ideRequests', () => {
  // Get the mocked ideApi
  const mockPostMessage = indexModule.ideApi.postMessage;

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset timers for each test
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  // Test Scenario: Successfully send request to IDE
  it('should send a request to the IDE with the correct data', async () => {
    const testData = { type: 'testRequest', payload: 'testPayload' };
    const promise = sendRequestToIde(testData);

    // Verify postMessage was called with the correct data
    expect(mockPostMessage).toHaveBeenCalledWith({
      ...testData,
      requestKey: 'mock-uuid-value',
    });

    // Simulate a successful response
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          requestKey: 'mock-uuid-value',
          result: 'success',
        },
      })
    );

    // Verify the promise resolves with the correct value
    await expect(promise).resolves.toBe('success');
  });

  // Test Scenario: Request times out
  it('should reject the promise when the request times out', async () => {
    const testData = { type: 'testRequest' };
    const promise = sendRequestToIde(testData);

    // Fast-forward time to trigger the timeout
    jest.advanceTimersByTime(5000);

    // Verify the promise is rejected with a timeout error
    await expect(promise).rejects.toThrow('Request to IDE timed out after 5000ms');
  });

  // Test Scenario: Receive successful response
  it('should resolve the promise when a successful response is received', async () => {
    const testData = { type: 'testRequest' };
    const promise = sendRequestToIde(testData);

    // Simulate a successful response
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          requestKey: 'mock-uuid-value',
          result: { result: 'success' },
        },
      })
    );

    // Verify the promise resolves with the correct value
    await expect(promise).resolves.toEqual({ result: 'success' });
  });

  // Test Scenario: Receive error response
  it('should reject the promise when an error response is received', async () => {
    const testData = { type: 'testRequest' };
    const promise = sendRequestToIde(testData);

    // Simulate an error response
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          requestKey: 'mock-uuid-value',
          error: 'Something went wrong',
        },
      })
    );

    // Verify the promise is rejected with the error message
    await expect(promise).rejects.toThrow('Something went wrong');
  });

  // Test Scenario: Multiple concurrent requests
  it('should handle multiple concurrent requests correctly', async () => {
    // Create two separate requests with different data
    const testData1 = { type: 'testRequest1' };
    const testData2 = { type: 'testRequest2' };

    // Mock uuid to return different values for each call
    jest
      .requireMock('uuid')
      .v4.mockReturnValueOnce('request-id-1')
      .mockReturnValueOnce('request-id-2');

    const promise1 = sendRequestToIde(testData1);
    const promise2 = sendRequestToIde(testData2);

    // Verify both requests were sent with correct IDs
    expect(mockPostMessage).toHaveBeenCalledWith({
      ...testData1,
      requestKey: 'request-id-1',
    });
    expect(mockPostMessage).toHaveBeenCalledWith({
      ...testData2,
      requestKey: 'request-id-2',
    });

    // Resolve the second request first
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          requestKey: 'request-id-2',
          result: 'response2',
        },
      })
    );

    // Then resolve the first request
    window.dispatchEvent(
      new MessageEvent('message', {
        data: {
          requestKey: 'request-id-1',
          result: 'response1',
        },
      })
    );

    // Verify both promises resolve with the correct values
    await expect(promise1).resolves.toBe('response1');
    await expect(promise2).resolves.toBe('response2');
  });
});
