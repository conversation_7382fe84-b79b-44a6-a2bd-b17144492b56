import { SettingsLayout } from '@src/components/chat-settings/settings.layout';
import { BaseLoaderView } from '@src/components/loaders/base-loader.view';
import { SecondaryButton } from '@src/components/ui-kit/button';
import { Tab, TabGroup, TabList } from '@src/components/ui-kit/tabs';
import LibraryIcon from '@src/icons/library.svg';
import { ideApi } from '@src/index';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { LibraryAgentsView } from './library-agents.view';
import { useZencoderAgents } from './use-zencoder-agents';
import { UsersAgentsView } from './users-agents.view';
import { AgentCategory } from './utils';

export default function CustomAgentsView() {
  const { state } = useAppStore();
  const { customAgents } = state;
  const zencoderAgents = useZencoderAgents();

  useEffect(() => {
    ideApi.postMessage({
      type: 'getCustomAgents',
    });
  }, []);
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<AgentCategory>(AgentCategory.ALL);

  if (!customAgents) {
    return <BaseLoaderView>Loading...</BaseLoaderView>;
  }

  return (
    <SettingsLayout>
      <div className={'flex flex-col text-foreground gap-y-2 py-4'}>
        <div className={'flex flex-row justify-between items-center select-none px-4'}>
          <h1 className={'flex flex-nowrap overflow-hidden m-0'}>Agents</h1>
          <SecondaryButton onClick={() => navigate(`/${Routes.editCustomAgent}/new`)}>
            Create agent
          </SecondaryButton>
        </div>
        <div className={'px-4'}>
          <p className={'text-secondary-foreground'}>
            Use prebuilt or create own agents to easily call them for a selected code or the whole
            file
          </p>
        </div>
        <TabGroup className={'px-5'}>
          <TabList>
            <Tab onClick={() => setSelectedCategory(AgentCategory.ALL)}>All</Tab>
            <Tab
              onClick={() => setSelectedCategory(AgentCategory.ZENCODER)}
              className={'flex flex-row gap-x-1 items-center'}
            >
              Zencoder
              <div className="rounded-full flex items-center justify-center px-1 h-3.5 bg-current font-semibold text-xs">
                <span className={'text-editor-background'}>{zencoderAgents.length}</span>
              </div>
            </Tab>
            <Tab
              onClick={() => setSelectedCategory(AgentCategory.CUSTOM)}
              className={'flex flex-row gap-x-1 items-center'}
            >
              Custom
              <div className="rounded-full flex items-center justify-center px-1 h-3.5 bg-current font-semibold text-xs">
                <span className={'text-editor-background'}>{customAgents.length}</span>
              </div>
            </Tab>
            <Tab alignRight onClick={() => setSelectedCategory(AgentCategory.LIBRARY)}>
              <LibraryIcon className="mr-1 w-4 h-4" />
              Library
            </Tab>
          </TabList>
          <div className="mt-3">
            {selectedCategory === AgentCategory.LIBRARY ? (
              <LibraryAgentsView />
            ) : (
              <UsersAgentsView selectedCategory={selectedCategory} />
            )}
          </div>
        </TabGroup>
      </div>
    </SettingsLayout>
  );
}
