import { fireEvent, render, screen } from '@testing-library/react';

import SlashCommandInput from '../slash-command-input';

// Mock the form-elements module
jest.mock('@src/components/ui-kit/form-elements');

describe('SlashCommandInput', () => {
  // Test initial rendering
  it('renders correctly with empty value', () => {
    render(<SlashCommandInput data-testid="slash-input" />);
    const inputElement = screen.getByTestId('slash-input');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveValue('');
  });

  it('renders correctly with initial value', () => {
    render(<SlashCommandInput data-testid="slash-input" value="test" />);
    const inputElement = screen.getByTestId('slash-input');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveValue('/test');
  });

  // Test user interactions
  it('handles typing valid characters', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');
    fireEvent.change(inputElement, { target: { value: '/hello' } });

    // Check that onChange was called with the correct transformed value (without slash)
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: 'hello',
        }),
      })
    );
  });

  it('handles typing invalid characters', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');

    // Type an invalid character (uppercase not allowed per regex)
    fireEvent.change(inputElement, { target: { value: '/Hello' } });

    // The onChange should not be called for invalid input
    expect(handleChange).not.toHaveBeenCalled();
  });

  it('handles clearing the input', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" value="test" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');
    expect(inputElement).toHaveValue('/test');

    // Clear the input
    fireEvent.change(inputElement, { target: { value: '' } });

    // Check that onChange was called with empty string
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: '',
        }),
      })
    );
  });

  it('handles typing just a slash', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');
    fireEvent.change(inputElement, { target: { value: '/' } });

    // Just typing a slash should not trigger onChange
    expect(handleChange).not.toHaveBeenCalled();
    expect(inputElement).toHaveValue('/');
  });

  it('handles special characters correctly', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');

    // Test with underscore (allowed per regex)
    fireEvent.change(inputElement, { target: { value: '/hello_world' } });

    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: 'hello_world',
        }),
      })
    );
  });

  it('handles numbers correctly', () => {
    const handleChange = jest.fn();
    render(<SlashCommandInput data-testid="slash-input" onChange={handleChange} />);

    const inputElement = screen.getByTestId('slash-input');

    // Test with numbers (allowed per regex)
    fireEvent.change(inputElement, { target: { value: '/test123' } });

    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: 'test123',
        }),
      })
    );
  });
});
