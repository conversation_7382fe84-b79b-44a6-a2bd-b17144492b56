import * as Sentry from '@sentry/react';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import { useAppStore } from '@src/store/app.store';
import { act, renderHook } from '@testing-library/react';
import { _resetLibraryAgentsState, useLibraryAgents } from '../use-library-agents';

// Mock dependencies
jest.mock('@sentry/react', () => ({
  captureException: jest.fn(),
}));

jest.mock('@src/store/app.store', () => ({
  useAppStore: jest.fn(),
}));

jest.mock('@src/hooks/use-mcp-tools', () => ({
  useMcpTools: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();
// @ts-expect-error
global.DOMParser = jest.fn(() => ({
  parseFromString: jest.fn(() => ({
    querySelector: jest.fn(() => null),
    querySelectorAll: jest.fn(() => []),
  })),
}));

describe('useLibraryAgents', () => {
  // Setup common mocks
  const mockSettings = {
    urlSettings: {
      cloudStorageEndpoint: 'https://example.com/storage',
    },
  };

  const mockTools = {
    ExecuteShellCommand: { id: 'shell', name: 'ExecuteShellCommand' },
    VectorSearchTool: { id: 'semantic_search', name: 'VectorSearchTool' },
    fetch_webpage: { id: 'webpage_content', name: 'fetch_webpage' },
    web_search: { id: 'web_search', name: 'web_search' },
    RequirementsTool: { id: 'requirements', name: 'RequirementsTool' },
    file_search: { id: 'file_search', name: 'File Search' },
    fulltext_search: { id: 'full_text_search', name: 'Full Text Search' },
    str_replace_editor: { id: 'file_edit', name: 'str_replace_editor' },
  };

  beforeEach(() => {
    _resetLibraryAgentsState();
    jest.clearAllMocks();

    // Default mocks
    (useAppStore as jest.Mock).mockReturnValue({
      state: { settings: mockSettings },
    });

    (useMcpTools as jest.Mock).mockReturnValue({
      tools: mockTools,
      isLoading: false,
    });

    // Reset fetch mock
    (global.fetch as jest.Mock).mockReset();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('returns empty agents initially', () => {
    // Mock fetch to delay response
    (global.fetch as jest.Mock).mockImplementation(() => new Promise(() => {}));

    const { result } = renderHook(() => useLibraryAgents());

    expect(result.current.agents).toEqual([]);
    expect(result.current.failedToLoad).toBe(false);
  });

  it('fetches and processes agents', async () => {
    // Mock XML response
    const mockXmlResponse = `
      <ListBucketResult>
        <Contents>
          <Key>zen-agents/agents/agent1.json</Key>
        </Contents>
        <Contents>
          <Key>zen-agents/agents/agent2.json</Key>
        </Contents>
      </ListBucketResult>
    `;

    // Mock agent config responses
    const mockAgent1 = {
      name: 'Test Agent 1',
      alias: 'test_agent_1',
      instructions: 'Test instructions 1',
      tools: ['shell', 'semantic_search'],
    };

    const mockAgent2 = {
      name: 'Test Agent 2',
      instructions: 'Test instructions 2',
      tools: ['file_search', 'full_text_search'],
    };

    // Setup fetch mock for different calls
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('?prefix=')) {
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve(mockXmlResponse),
        });
      } else if (url.includes('agent1.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAgent1),
        });
      } else if (url.includes('agent2.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAgent2),
        });
      }
      return Promise.reject(new Error('Unexpected URL'));
    });

    // Mock DOMParser to return XML elements
    (DOMParser as jest.Mock).mockImplementation(() => ({
      parseFromString: jest.fn(() => ({
        querySelector: jest.fn(() => null), // No parser error
        querySelectorAll: jest.fn(() => [
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent1.json' };
              }
              return null;
            },
          },
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent2.json' };
              }
              return null;
            },
          },
        ]),
      })),
    }));

    const { result, rerender } = renderHook(() => useLibraryAgents());

    // Initially empty
    expect(result.current.agents).toEqual([]);

    // Wait for promises to resolve
    await act(async () => {
      await Promise.resolve();
    });

    rerender();

    // Should have processed the agents
    expect(result.current.agents.length).toBe(2);
    expect(result.current.agents[0]?.name).toBe('Test Agent 1');
    expect(result.current.agents[0]?.commandName).toBe('test_agent_1');
    expect(result.current.agents[0]?.tools?.length).toBe(2);

    expect(result.current.agents[1]?.name).toBe('Test Agent 2');
    expect(result.current.agents[1]?.commandName).toBe('test_agent_2');
    expect(result.current.agents[1]?.tools?.length).toBe(2);

    expect(result.current.failedToLoad).toBe(false);
  });

  it('handles fetch errors correctly', async () => {
    // Mock fetch to reject
    (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

    const { result, rerender } = renderHook(() => useLibraryAgents());

    // Initially empty
    expect(result.current.agents).toEqual([]);
    expect(result.current.failedToLoad).toBe(false);

    // Wait for promises to resolve
    await act(async () => {
      await Promise.resolve();
    });

    rerender();

    // Should have set failedToLoad to true
    expect(result.current.failedToLoad).toBe(true);
    expect(Sentry.captureException).toHaveBeenCalled();
  });

  it('filters duplicate aliases', async () => {
    // Mock XML response
    const mockXmlResponse = `
      <ListBucketResult>
        <Contents>
          <Key>zen-agents/agents/agent1.json</Key>
        </Contents>
        <Contents>
          <Key>zen-agents/agents/agent2.json</Key>
        </Contents>
      </ListBucketResult>
    `;

    // Mock agent config responses with duplicate aliases
    const mockAgent1 = {
      name: 'Test Agent 1',
      alias: 'duplicate_alias',
      instructions: 'Test instructions 1',
      tools: ['shell'],
    };

    const mockAgent2 = {
      name: 'Test Agent 2',
      alias: 'duplicate_alias',
      instructions: 'Test instructions 2',
      tools: ['file_search'],
    };

    // Setup fetch mock
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('?prefix=')) {
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve(mockXmlResponse),
        });
      } else if (url.includes('agent1.json') || url.includes('agent2.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(url.includes('agent1.json') ? mockAgent1 : mockAgent2),
        });
      }
      return Promise.reject(new Error('Unexpected URL'));
    });

    // Mock DOMParser
    (DOMParser as jest.Mock).mockImplementation(() => ({
      parseFromString: jest.fn(() => ({
        querySelector: jest.fn(() => null),
        querySelectorAll: jest.fn(() => [
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent1.json' };
              }
              return null;
            },
          },
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent2.json' };
              }
              return null;
            },
          },
        ]),
      })),
    }));

    // Mock console.warn
    const originalWarn = console.warn;
    console.warn = jest.fn();

    const { result, rerender } = renderHook(() => useLibraryAgents());

    // Wait for promises to resolve
    await act(async () => {
      await Promise.resolve();
    });

    rerender();

    // Should only include the first agent with the duplicate alias
    expect(result.current.agents.length).toBe(1);
    expect(result.current.agents[0]?.name).toBe('Test Agent 1');
    expect(result.current.agents[0]?.commandName).toBe('duplicate_alias');

    // Should have logged a warning
    expect(console.warn).toHaveBeenCalledWith('Duplicate alias found:', 'duplicate_alias');

    // Restore console.warn
    console.warn = originalWarn;
  });

  it('maps tools correctly', async () => {
    // Mock XML response
    const mockXmlResponse = `
      <ListBucketResult>
        <Contents>
          <Key>zen-agents/agents/agent1.json</Key>
        </Contents>
      </ListBucketResult>
    `;

    // Mock agent with all tool types
    const mockAgent = {
      name: 'Test Agent',
      instructions: 'Test instructions',
      tools: [
        'shell',
        'semantic_search',
        'webpage_content',
        'web_search',
        'requirements',
        'file_search',
        'full_text_search',
        'file_edit',
        'non_existent_tool', // Should be filtered out
      ],
    };

    // Setup fetch mock
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('?prefix=')) {
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve(mockXmlResponse),
        });
      } else if (url.includes('agent1.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAgent),
        });
      }
      return Promise.reject(new Error('Unexpected URL'));
    });

    // Mock DOMParser
    (DOMParser as jest.Mock).mockImplementation(() => ({
      parseFromString: jest.fn(() => ({
        querySelector: jest.fn(() => null),
        querySelectorAll: jest.fn(() => [
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent1.json' };
              }
              return null;
            },
          },
        ]),
      })),
    }));

    const { result, rerender } = renderHook(() => useLibraryAgents());

    // Wait for promises to resolve
    await act(async () => {
      await Promise.resolve();
    });

    rerender();

    // Should have mapped all valid tools
    expect(result.current.agents.length).toBe(1);
    expect(result.current.agents[0]?.tools?.length).toBe(8); // All valid tools

    // Verify each tool was mapped correctly
    const toolNames = result.current.agents[0]?.tools?.map((t) => t.name);
    expect(toolNames).toContain('ExecuteShellCommand');
    expect(toolNames).toContain('VectorSearchTool');
    expect(toolNames).toContain('fetch_webpage');
    expect(toolNames).toContain('web_search');
    expect(toolNames).toContain('RequirementsTool');
    expect(toolNames).toContain('File Search');
    expect(toolNames).toContain('Full Text Search');
    expect(toolNames).toContain('str_replace_editor');
  });

  it('refreshes data periodically', async () => {
    // Mock XML response
    const mockXmlResponse = `
      <ListBucketResult>
        <Contents>
          <Key>zen-agents/agents/agent1.json</Key>
        </Contents>
      </ListBucketResult>
    `;

    // Mock agent config
    const mockAgent = {
      name: 'Test Agent',
      instructions: 'Test instructions',
      tools: ['shell'],
    };

    // Setup fetch mock
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('?prefix=')) {
        return Promise.resolve({
          ok: true,
          text: () => Promise.resolve(mockXmlResponse),
        });
      } else if (url.includes('agent1.json')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve(mockAgent),
        });
      }
      return Promise.reject(new Error('Unexpected URL'));
    });

    // Mock DOMParser
    (DOMParser as jest.Mock).mockImplementation(() => ({
      parseFromString: jest.fn(() => ({
        querySelector: jest.fn(() => null),
        querySelectorAll: jest.fn(() => [
          {
            querySelector: (selector: string) => {
              if (selector === 'Key') {
                return { textContent: 'zen-agents/agents/agent1.json' };
              }
              return null;
            },
          },
        ]),
      })),
    }));

    renderHook(() => useLibraryAgents());

    // Initial fetch
    expect(global.fetch).toHaveBeenCalledTimes(1);

    // Fast-forward 5 minutes
    act(() => {
      jest.advanceTimersByTime(10 * 60 * 1000);
    });

    // Should have fetched again
    expect(global.fetch).toHaveBeenCalledTimes(3); // Initial fetch + 2 more calls for the XML and JSON
  });

  it('handles missing endpoint', () => {
    // Mock settings without cloudStorageEndpoint
    (useAppStore as jest.Mock).mockReturnValue({
      state: { settings: { urlSettings: {} } },
    });

    const { result } = renderHook(() => useLibraryAgents());

    // Should not attempt to fetch
    expect(global.fetch).not.toHaveBeenCalled();
    expect(result.current.agents).toEqual([]);
    expect(result.current.failedToLoad).toBe(false);
  });
});
