import SlashCommandInput from '@src/components/chat-settings/custom-agents-view/slash-command-input';
import { PrimaryButton, SecondaryButton } from '@src/components/ui-kit/button';
import { DialogTitle, ModalDialog } from '@src/components/ui-kit/dialog';
import { Field, Input, Label, ListBox, Textarea } from '@src/components/ui-kit/form-elements';
import { Icon } from '@src/components/ui-kit/icon';
import TagSelect from '@src/components/ui-kit/tag-select';
import { Tooltip, useTooltipId } from '@src/components/ui-kit/tooltip';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import ArrowIcon from '@src/icons/chevron-down.svg';
import McpIcon from '@src/icons/mcp.svg';
import QuestionMarkIcon from '@src/icons/question-mark.svg';
import { Agent } from '@src/models/custom-agents.model';
import { McpServerInfo } from '@src/models/mcp-servers.model';
import { Routes } from '@src/models/router';
import { getNotInstalledMcpServers } from '@src/utils/get-not-installed-tools';
import isEqual from 'lodash.isequal';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useBlocker, useNavigate } from 'react-router-dom';

type Props = {
  initialAgent?: Agent;
  editingAgent: Agent;
  installedTools: McpServerInfo[];
  onChange: (agent: Agent) => void;
  showDetails: boolean;
  canEdit: boolean;
  canShare: boolean;
  showInstallTools?: boolean;
  shouldNavigateAway?: boolean;
};

enum AgentShareStatus {
  Personal = 'personal',
  Org = 'org',
}

const shareOptions: Record<
  AgentShareStatus,
  { title: string; description: string; id: AgentShareStatus }
> = {
  [AgentShareStatus.Personal]: {
    title: 'Personal',
    description: 'Visible only to you',
    id: AgentShareStatus.Personal,
  },
  [AgentShareStatus.Org]: {
    title: 'Org',
    description: 'Shared with all users in company',
    id: AgentShareStatus.Org,
  },
};

export default function CustomAgentEditFormView(props: Readonly<Props>) {
  const {
    initialAgent,
    editingAgent,
    installedTools,
    showDetails,
    canEdit,
    canShare,
    showInstallTools = true,
    shouldNavigateAway = false,
    onChange,
  } = props;
  const [isCloseEditingDialogVisible, setIsCloseEditingDialogVisible] = useState(false);
  const { tools, isLoading: isToolsLoading } = useMcpTools();
  const chatCommandTooltipId = useTooltipId();
  const navigate = useNavigate();

  const hasUnsavedChanges = useMemo(() => {
    if (!initialAgent) {
      return !!(editingAgent.name || editingAgent.commandName || editingAgent.command);
    }
    return !isEqual(initialAgent, editingAgent);
  }, [editingAgent, initialAgent]);

  const shouldBlock = useMemo(() => {
    return hasUnsavedChanges && !shouldNavigateAway;
  }, [hasUnsavedChanges, shouldNavigateAway]);

  const blocker = useBlocker(({ currentLocation, nextLocation }) => {
    if (currentLocation.pathname === nextLocation.pathname) {
      return false;
    }
    return shouldBlock;
  });

  useEffect(() => {
    if (blocker.state === 'blocked') {
      setIsCloseEditingDialogVisible(true);
    }
  }, [blocker]);

  const handleCloseEditingDialogClick = useCallback(
    (confirm: boolean) => {
      setIsCloseEditingDialogVisible(false);

      if (confirm) {
        if (blocker.state === 'blocked') {
          blocker.proceed();
        }
      } else {
        if (blocker.state === 'blocked') {
          blocker.reset();
        }
      }
    },
    [blocker]
  );

  const selectedTags = useMemo(() => {
    if (!editingAgent.tools) {
      return [];
    }

    return editingAgent.tools.map((selectedTool) => {
      const actualTool = tools[selectedTool.id];

      return {
        ...selectedTool,
        icon: actualTool?.icon || selectedTool.icon,
        name: actualTool?.name || selectedTool.name,
      };
    });
  }, [editingAgent.tools, tools]);

  const toolsToInstall = useMemo(
    () =>
      !showInstallTools || isToolsLoading
        ? []
        : getNotInstalledMcpServers(editingAgent.tools ?? [], tools),
    [editingAgent.tools, isToolsLoading, showInstallTools, tools]
  );

  const onAgentChange = (agent: Partial<Agent>) => {
    onChange({
      ...editingAgent,
      ...agent,
    });
  };

  return (
    <div className={'flex flex-col gap-y-1 py-4 w-full'}>
      <Field className={'mb-3'}>
        <Label className="block mb-2 font-bold">Name*</Label>
        <Input
          value={editingAgent.name}
          disabled={!canEdit}
          autoFocus={true}
          placeholder={'Agent name'}
          className={'w-full'}
          maxLength={64}
          onChange={(e) => onAgentChange({ name: e.target.value })}
        />
      </Field>
      {canShare && (
        <Field className={'mb-3'}>
          <Label className="block font-bold">Share</Label>
          <p className="text-secondary-foreground mb-2">Set who can see and use this agent</p>
          <ListBox
            disabled={!canEdit}
            options={Object.values(shareOptions)}
            onChange={(e) => {
              onAgentChange({
                isSharedWithOrganization: e.id === AgentShareStatus.Org,
              });
            }}
            value={
              editingAgent.isSharedWithOrganization
                ? shareOptions[AgentShareStatus.Org]
                : shareOptions[AgentShareStatus.Personal]
            }
          />
        </Field>
      )}
      <h2>Configuration</h2>
      <Field className={'mb-3'}>
        <div className={'flex flex-row justify-between gap-x-2 items-center mt-2'}>
          <Label className="block font-bold">Alias*</Label>
          <QuestionMarkIcon
            data-tooltip-id={chatCommandTooltipId}
            className="h-4 w-4 flex-none block text-disabled-foreground"
          />
          <Tooltip id={chatCommandTooltipId}>
            The command must be unique across Agents and use only lowercase Latin letters, digits,
            or underscores (_).
          </Tooltip>
        </div>
        <p className={'text-secondary-foreground mb-2'}>To call agent in chat</p>
        <SlashCommandInput
          value={editingAgent.commandName}
          placeholder={'/example_agent'}
          className={'w-full'}
          disabled={!canEdit}
          maxLength={64}
          onChange={(e) => onAgentChange({ commandName: e.target.value })}
        />
      </Field>
      {showDetails && (
        <Field className={'mb-3'}>
          <Label className="block mb-2 font-bold">Instructions*</Label>
          <Textarea
            value={editingAgent.command}
            className={'w-full'}
            disabled={!canEdit}
            placeholder={'Detailed instruction for an agent to follow'}
            maxLength={20000}
            rows={7}
            onChange={(e) => onAgentChange({ command: e.target.value })}
          />
        </Field>
      )}
      <Field className={'mb-3'}>
        <Label className={'block mb-2 font-bold'}>Tools</Label>
        <TagSelect
          title={'Tools'}
          showHeader={true}
          placeholder={'Add tool...'}
          managementButtonTitle={'Manage tools'}
          onManageClick={() => navigate(`/${Routes.agentTools}`)}
          tags={installedTools}
          selectedTags={selectedTags}
          readonly={!canEdit}
          onTagsEdit={(value) => {
            onAgentChange({ tools: value });
          }}
          constantTags={[]}
          defaultTagIcon={McpIcon}
          //compare function for sorting of the tools
          compare={(first, second) => {
            if (
              first.author?.toLowerCase().startsWith('zencoder') &&
              !second.author?.toLowerCase().startsWith('zencoder')
            ) {
              return 1;
            } else if (
              second.author?.toLowerCase().startsWith('zencoder') &&
              !first.author?.toLowerCase().startsWith('zencoder')
            ) {
              return -1;
            } else {
              return first.name.localeCompare(second.name);
            }
          }}
        />
      </Field>
      {showInstallTools && toolsToInstall && !!toolsToInstall.length && (
        <Field className={'mb-3'}>
          <Label className={'mb-2 font-bold flex flex-row gap-x-1 items-center'}>
            Need to install
            <Icon type="warning" size={20} className="text-warning-foreground" />
          </Label>
          <div>
            {toolsToInstall.map((tool) => (
              <div
                onClick={() => navigate(`/${Routes.agentToolEdit}/${tool.id}`)}
                key={tool.id}
                className={
                  'group cursor-pointer flex flex-row gap-2 items-center hover:bg-secondary-button-background rounded-lg p-3'
                }
              >
                <div className={'flex items-center justify-center w-10 h-10'}>
                  <img src={`data:image/png;base64, ${tool.icon}`} alt="" />
                </div>
                <div className={'w-full'}>
                  <div className={'flex flex-row justify-between items-center'}>
                    <span>{tool.name}</span>
                    <ArrowIcon className={'-rotate-90 group-hover:visible invisible'} />
                  </div>
                  <div>
                    <span className={'text-sm text-disabled-foreground'}>{tool.author}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Field>
      )}
      <ModalDialog
        open={isCloseEditingDialogVisible}
        onClose={() => handleCloseEditingDialogClick(false)}
      >
        <DialogTitle>Unsaved changes</DialogTitle>
        You’ve made changes that haven’t been saved. Are you sure you want to leave this page?
        <div className={'mt-4 flex justify-end gap-x-2'}>
          <SecondaryButton onClick={() => handleCloseEditingDialogClick(false)}>
            Stay
          </SecondaryButton>
          <PrimaryButton
            onClick={() => handleCloseEditingDialogClick(true)}
            className={
              'bg-error-button-background data-[hover]:bg-error-button-active-background data-[active]:bg-error-button-active-background'
            }
          >
            Leave
          </PrimaryButton>
        </div>
      </ModalDialog>
    </div>
  );
}
