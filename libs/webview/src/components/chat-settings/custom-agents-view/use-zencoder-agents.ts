import { useMcpTools } from '@src/hooks/use-mcp-tools';
import { Agent } from '@src/models/custom-agents.model';
import { useMemo } from 'react';

enum ZencoderAgentId {
  CodingAgent = 'coding_agent',
  UnitTestsAgent = 'unit_tests_agent',
  E2ETestAgent = 'e2e_tests_agent',
}

const toolsForAgent = {
  [ZencoderAgentId.CodingAgent]: [
    'file_search',
    'fulltext_search',
    'web_search',
    'RequirementsTool',
    'fetch_webpage',
    'str_replace_editor',
    'ExecuteShellCommand',
  ],
  [ZencoderAgentId.UnitTestsAgent]: [
    'file_search',
    'fulltext_search',
    'RequirementsTool',
    'str_replace_editor',
    'ExecuteShellCommand',
  ],
  [ZencoderAgentId.E2ETestAgent]: [
    'file_search',
    'fulltext_search',
    'str_replace_editor',
    'ExecuteShellCommand',
  ],
};

export function useZencoderAgents(): Agent[] {
  const { tools, isLoading } = useMcpTools();

  const zencoderTools = useMemo(() => {
    if (isLoading) return [];
    return Object.entries(tools)
      .map(([key, value]) => ({ ...value, id: key }))
      .filter((agent) => agent.author?.toLowerCase().startsWith('zencoder'));
  }, [isLoading, tools]);

  const customTools = useMemo(() => {
    if (isLoading) return [];
    return Object.entries(tools)
      .map(([key, value]) => ({ ...value, id: key }))
      .filter(
        (agent) =>
          !agent.author?.toLowerCase().startsWith('zencoder') && agent.status.status === 'installed'
      );
  }, [isLoading, tools]);

  return [
    {
      id: ZencoderAgentId.CodingAgent,
      commandName: 'code',
      name: 'Coding agent',
      command:
        'Performs development tasks, such as bug fixing, refactoring, or feature development',
      author: 'Zencoder',
      codeLens: false,
      rag: true,
      tools: [
        ...zencoderTools.filter((tool) =>
          toolsForAgent[ZencoderAgentId.CodingAgent].some((toolId) => tool.id === toolId)
        ),
        ...customTools,
      ],
    },
    {
      id: ZencoderAgentId.UnitTestsAgent,
      commandName: 'unittests',
      name: 'Unit tests',
      command: 'Generate unit tests for all popular programming languages',
      author: 'Zencoder',
      codeLens: false,
      rag: true,
      tools: [
        ...zencoderTools.filter((tool) =>
          toolsForAgent[ZencoderAgentId.UnitTestsAgent].some((toolId) => tool.id === toolId)
        ),
        ...customTools,
      ],
    },
    {
      id: ZencoderAgentId.E2ETestAgent,
      commandName: 'e2e-test',
      name: 'E2E Test Agent',
      command: 'Generate and maintain end-to-end tests for web apps with visual agentic navigation',
      author: 'Zencoder',
      codeLens: false,
      rag: true,
      tools: [
        ...zencoderTools.filter((tool) =>
          toolsForAgent[ZencoderAgentId.E2ETestAgent].some((toolId) => tool.id === toolId)
        ),
        ...customTools,
      ],
    },
  ];
}
