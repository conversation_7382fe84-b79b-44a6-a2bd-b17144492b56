import { Agent } from '@src/models/custom-agents.model';

export enum AgentCategory {
  ALL = 'all',
  ZENCODER = 'zencoder',
  CUSTOM = 'custom',
  LIBRARY = 'library',
}

export function sortAgents(agents?: Agent[]) {
  return agents?.sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase())) ?? [];
}

export function getAgentAnalyticsProps(agent: Agent) {
  return {
    agent_id: agent.id,
    agent_use_rag: agent.rag,
    agent_instruction_length: agent.command.length,
    agent_has_command: !!agent.commandName,
  };
}
