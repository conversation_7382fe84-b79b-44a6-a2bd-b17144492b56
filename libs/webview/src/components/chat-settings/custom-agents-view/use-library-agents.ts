import * as Sentry from '@sentry/react';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import { Agent } from '@src/models/custom-agents.model';
import { useAppStore } from '@src/store/app.store';
import { useEffect, useMemo, useRef, useState } from 'react';
import { normalizeCommandName } from './helpers/normalize-command-name';

interface CloudStorageFile {
  key: string;
}

const ZENCODER_TOOLS_IDS = [
  'file_search',
  'full_text_search',
  'web_search',
  'webpage_content',
  'semantic_search',
  'file_edit',
  'shell',
  'requirements',
] as const;

type ConfigZencoderTool = (typeof ZENCODER_TOOLS_IDS)[number];

const ZENCODER_TOOLS_MAP: Record<ConfigZencoderTool, string> = {
  shell: 'ExecuteShellCommand',
  semantic_search: 'VectorSearchTool',
  webpage_content: 'fetch_webpage',
  web_search: 'web_search',
  requirements: 'RequirementsTool',
  file_search: 'file_search',
  full_text_search: 'fulltext_search',
  file_edit: 'str_replace_editor',
};

//str_replace_editor

interface AgentConfig {
  name: string;
  alias?: string;
  instructions: string;
  tools: ConfigZencoderTool[];
}

let fetchDataPromise: Promise<AgentConfig[]> | null = null;
let lastRequestedAt: number | null = null;

// Reset function for testing. Don't use this.
// eslint-disable-next-line @typescript-eslint/naming-convention
export function _resetLibraryAgentsState() {
  fetchDataPromise = null;
  lastRequestedAt = null;
}

export function useLibraryAgents(): { agents: Agent[]; failedToLoad: boolean } {
  const {
    state: { settings },
  } = useAppStore();
  const { tools, isLoading: isToolsLoading } = useMcpTools();
  const [agentsConfigs, setAgentsConfigs] = useState<AgentConfig[]>([]);
  const [failedToLoad, setFailedToLoad] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!settings?.urlSettings?.cloudStorageEndpoint) {
      return;
    }

    if (!fetchDataPromise || !lastRequestedAt || lastRequestedAt + 5 * 60 * 1000 < Date.now()) {
      lastRequestedAt = Date.now();
      fetchDataPromise = fetchData(settings.urlSettings.cloudStorageEndpoint);
    }

    fetchDataPromise.then(setAgentsConfigs).catch((err) => {
      console.error(err);
      fetchDataPromise = null;
      setFailedToLoad(true);
      Sentry.captureException(err ?? new Error('Failed to load agents from library'));
    });

    intervalRef.current = setInterval(
      () => {
        if (!settings?.urlSettings?.cloudStorageEndpoint) {
          return;
        }

        lastRequestedAt = Date.now();
        fetchDataPromise = fetchData(settings.urlSettings.cloudStorageEndpoint);
        fetchDataPromise.then(setAgentsConfigs).catch((err) => {
          console.error(err);
          fetchDataPromise = null;
          setFailedToLoad(true);
          Sentry.captureException(err ?? new Error('Failed to load agents from library'));
        });
      },
      5 * 60 * 1000
    );

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [settings?.urlSettings?.cloudStorageEndpoint]);

  const agents = useMemo(() => {
    if (isToolsLoading) {
      return [];
    }
    const addedAliases = new Set<string>();

    return agentsConfigs.reduce<Agent[]>((acc, config) => {
      const alias = config.alias || normalizeCommandName(config.name);
      if (addedAliases.has(alias)) {
        console.warn('Duplicate alias found:', alias);
        return acc; // Skip adding this agent if an alias is already present.
      }

      addedAliases.add(alias);

      acc.push({
        id: `library-${alias}`,
        name: config.name,
        commandName: alias,
        command: config.instructions,
        tools: config.tools
          .map((configToolId) => {
            if (ZENCODER_TOOLS_IDS.includes(configToolId)) {
              return tools[ZENCODER_TOOLS_MAP[configToolId]];
            } else {
              return tools[configToolId];
            }
          })
          .filter((t) => !!t),
      });

      return acc;
    }, []);
  }, [agentsConfigs, isToolsLoading, tools]);

  return {
    agents,
    failedToLoad,
  };
}

const AGENTS_DIRECTORY = 'zen-agents/agents/';

const fetchData = async (url: string) => {
  // Step 1: Fetch the XML listing from cloud storage
  const filesResponse = await fetch(`${url}?prefix=${encodeURIComponent(AGENTS_DIRECTORY)}`);

  if (!filesResponse.ok) {
    throw new Error(`Failed to fetch agents for library. Status: ${filesResponse.status}`);
  }

  const xmlText = await filesResponse.text();

  // Step 2: Parse XML to extract file keys
  const parser = new DOMParser();
  const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

  // Check for parsing errors
  const parserError = xmlDoc.querySelector('parsererror');
  if (parserError) {
    throw new Error('Failed to parse XML response from cloud storage');
  }

  const contentsElements = xmlDoc.querySelectorAll('Contents');
  const files: CloudStorageFile[] = [];

  contentsElements.forEach((content) => {
    const keyElement = content.querySelector('Key');
    if (keyElement) {
      const key = keyElement.textContent || '';

      // Filter for JSON files in the agents directory
      if (key.startsWith(AGENTS_DIRECTORY) && key.endsWith('.json')) {
        files.push({
          key,
        });
      }
    }
  });

  if (files.length === 0) {
    throw new Error(`No agent files found in cloud storage.`);
  }

  // Step 3: Fetch and parse each JSON file
  const configs = await Promise.all(
    files.map(async (file): Promise<AgentConfig | null> => {
      try {
        const response = await fetch(`${url}/${file.key}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch agent config: ${url}/${file.key}`);
        }
        return await response.json();
      } catch (e) {
        console.error(e);
        return null;
      }
    })
  );

  const validConfigs = configs.filter((c) => !!c);

  if (validConfigs.length === 0) {
    throw new Error(`No agent with valid config found in cloud storage.`);
  }

  return validConfigs;
};
