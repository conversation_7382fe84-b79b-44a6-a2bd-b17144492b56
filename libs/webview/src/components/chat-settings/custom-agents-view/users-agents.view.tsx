import { useAppStore } from '@src/store/app.store';
import { useMemo } from 'react';
import AgentCard from './custom-agent-card.view';
import { useZencoderAgents } from './use-zencoder-agents';
import { AgentCategory, sortAgents } from './utils';

interface Props {
  selectedCategory: Exclude<AgentCategory, AgentCategory.LIBRARY>;
}

export function UsersAgentsView({ selectedCategory }: Props) {
  const { state } = useAppStore();
  const { customAgents } = state;
  const zencoderAgents = useZencoderAgents();

  const sortedAgents = useMemo(() => {
    switch (selectedCategory) {
      case AgentCategory.ALL:
        return [...sortAgents(zencoderAgents), ...sortAgents(customAgents)];
      case AgentCategory.ZENCODER:
        return sortAgents(zencoderAgents);
      case AgentCategory.CUSTOM:
        return sortAgents(customAgents);
    }
  }, [customAgents, selectedCategory, zencoderAgents]);

  return (
    <div className="grid grid-cols-1 gap-4">
      {sortedAgents.map((agent) => (
        <AgentCard key={agent.id} agent={agent} />
      ))}
    </div>
  );
}
