import { SettingsLayout } from '@src/components/chat-settings/settings.layout';
import { DEFAULT_SLASH_COMMAND_NAMES } from '@src/components/chat-view/chat-input-field-view/message-editor/message-editor';
import { BaseLoaderView } from '@src/components/loaders/base-loader.view';
import { PrimaryButton } from '@src/components/ui-kit/button';
import { sendRequestToIde } from '@src/ideRequests';
import { ideApi } from '@src/index';
import { Agent } from '@src/models/custom-agents.model';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import CustomAgentEditFormView from './custom-agent-edit-form.view';
import { getUniqueAgentCommandName } from './helpers/get-unique-agent-command';
import { useLibraryAgents } from './use-library-agents';
import { useZencoderAgents } from './use-zencoder-agents';

interface RoutingParams extends Record<string, string | undefined> {
  agentId: string;
}

export default function ViewLibraryAgentView() {
  const { agentId } = useParams<RoutingParams>();
  const { state } = useAppStore();
  const { customAgents } = state;
  const { agents, failedToLoad } = useLibraryAgents();
  const navigate = useNavigate();
  const zencoderAgents = useZencoderAgents();

  const [viewingAgent, setViewingAgent] = useState<Agent | undefined>();

  const uniqueAlias = useMemo(() => {
    if (!viewingAgent) return '';

    return getUniqueAgentCommandName({
      commandName: viewingAgent.commandName,
      agents: [...(customAgents ?? []), ...zencoderAgents],
      defaultCommandNames: DEFAULT_SLASH_COMMAND_NAMES,
    });
  }, [customAgents, viewingAgent, zencoderAgents]);

  useEffect(() => {
    if (!agentId || !agents.length) return;

    const agent = agents.find((a) => a.id === agentId);
    if (agent) {
      setViewingAgent(agent);
    }
  }, [agentId, agents]);

  useEffect(() => {
    ideApi.postMessage({
      type: 'getCustomAgents',
    });
  }, []);

  const onAddToMyAgents = async () => {
    if (!viewingAgent || !customAgents) {
      return;
    }

    // Create a new agent based on the library agent
    const newAgent: Agent = {
      ...viewingAgent,
      id: '', // Empty ID so it gets assigned a new one
      commandName: uniqueAlias,
    };

    const savedAgent = await sendRequestToIde<Agent>({
      type: 'saveCustomAgent',
      agent: newAgent,
    });

    // Navigate back to the custom agents list
    navigate(`/${Routes.editCustomAgent}/${savedAgent.id}`);
  };

  if (!viewingAgent) {
    if (!agents.length && failedToLoad) {
      return <p className="mx-4">Failed to load agents. Please try again later.</p>;
    }

    return <BaseLoaderView>Loading...</BaseLoaderView>;
  }

  return (
    <SettingsLayout
      backPage={{
        path: `/${Routes.customAgents}`,
        label: 'Agents',
      }}
    >
      <div className={'py-4'}>
        <div className={'flex flex-col text-foreground gap-y-2'}>
          <div className={'flex flex-row justify-between items-center select-none px-4'}>
            <h1 className={'flex flex-nowrap overflow-hidden m-0'}>{viewingAgent.name}</h1>
            <PrimaryButton onClick={onAddToMyAgents}>Add</PrimaryButton>
          </div>
          <div className={'px-4'}>
            <CustomAgentEditFormView
              editingAgent={{ ...viewingAgent, commandName: uniqueAlias }}
              installedTools={[]}
              shouldNavigateAway={true}
              onChange={() => {}} // No changes allowed
              showDetails={true}
              canEdit={false} // Read-only
              canShare={false}
              showInstallTools={false}
            />
          </div>
        </div>
      </div>
    </SettingsLayout>
  );
}
