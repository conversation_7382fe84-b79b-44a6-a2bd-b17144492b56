import { Icon } from '@src/components/ui-kit/icon';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import { Agent } from '@src/models/custom-agents.model';
import { Routes } from '@src/models/router';
import { getNotInstalledMcpServers } from '@src/utils/get-not-installed-tools';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { twJoin } from 'tailwind-merge';

type Props = {
  agent: Agent;
  showInstallTools?: boolean;
};

export default function AgentCard({ agent, showInstallTools = true }: Readonly<Props>) {
  const navigate = useNavigate();
  const { tools, isLoading: isToolsLoading } = useMcpTools();
  const toolsToInstall = useMemo(
    () =>
      !showInstallTools || isToolsLoading
        ? []
        : getNotInstalledMcpServers(agent.tools ?? [], tools),
    [agent.tools, isToolsLoading, showInstallTools, tools]
  );

  return (
    <div
      className={twJoin(
        'relative',
        'flex',
        'flex-col',
        'justify-between',
        'cursor-pointer',
        'p-2',
        'rounded-md',
        'hover:bg-hover-background',
        'select-none'
      )}
      onClick={() => {
        // If it's a library agent, go to view-library-agent route
        if (agent.id.includes('library-')) {
          navigate(`/${Routes.viewLibraryAgent}/${agent.id}`);
        } else {
          navigate(`/${Routes.editCustomAgent}/${agent.id}`);
        }
      }}
    >
      <div className="flex items-center justify-between gap-2">
        <div className={'m-0 flex items-center min-w-0 flex-1'}>
          {showInstallTools && toolsToInstall.length > 0 && (
            <Icon type="warning" size={20} className="mr-1 text-warning-foreground flex-shrink-0" />
          )}
          <div className="flex flex-wrap leading-6 min-w-0 w-full">
            <span className="font-semibold mr-1 truncate">{agent.name}</span>
            <span className="font-normal text-link-foreground whitespace-nowrap">
              /{agent.commandName}
            </span>
          </div>
        </div>
        {!!agent.isSharedWithOrganization && (
          <span className="bg-secondary-button-background flex items-center h-5 px-1.5 rounded-full text-sm flex-shrink-0">
            Org
          </span>
        )}
      </div>
      {agent.author?.toLowerCase().startsWith('zencoder') && (
        <p className={'text-secondary-foreground'}>by {agent.author}</p>
      )}
      <p className={'text-secondary-foreground whitespace-break-spaces line-clamp-2'}>
        {agent.command}
      </p>
    </div>
  );
}
