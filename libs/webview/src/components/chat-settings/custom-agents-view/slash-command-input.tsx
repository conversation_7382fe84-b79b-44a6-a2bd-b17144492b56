import { Input } from '@src/components/ui-kit/form-elements';
import React, { ComponentProps, forwardRef, useEffect, useState } from 'react';

const SlashCommandInput = forwardRef<HTMLInputElement, ComponentProps<'input'>>((props, ref) => {
  const { value, onChange, ...rest } = props;
  const [showingValue, setShowingValue] = useState('');
  useEffect(() => {
    if (value) {
      setShowingValue(`/${value}`);
    } else {
      setShowingValue('');
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const raw = e.target.value;
    if (raw === '/') {
      setShowingValue('/');
      return;
    }
    if (!raw.length) {
      setShowingValue('');
      onChange?.({
        ...e,
        target: {
          ...e.target,
          value: '',
        },
      });
      return;
    }
    if (/^\/?[a-z0-9_]*$/.test(raw)) {
      onChange?.({
        ...e,
        target: {
          ...e.target,
          value: e.target.value.replace(/^\//, ''),
        },
      });
      return;
    }
  };

  return <Input {...rest} value={showingValue} onChange={handleChange} ref={ref} />;
});

export default SlashCommandInput;
