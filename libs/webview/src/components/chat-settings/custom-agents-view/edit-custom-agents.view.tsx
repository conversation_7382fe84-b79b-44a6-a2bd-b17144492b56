import CustomAgentEditFormView from '@src/components/chat-settings/custom-agents-view/custom-agent-edit-form.view';
import { useZencoderAgents } from '@src/components/chat-settings/custom-agents-view/use-zencoder-agents';
import { SettingsLayout } from '@src/components/chat-settings/settings.layout';
import { DEFAULT_SLASH_COMMAND_NAMES } from '@src/components/chat-view/chat-input-field-view/message-editor/message-editor';
import { BaseLoaderView } from '@src/components/loaders/base-loader.view';
import { LinkButton, PrimaryButton, SecondaryButton } from '@src/components/ui-kit/button';
import { DialogTitle, ModalDialog } from '@src/components/ui-kit/dialog';
import { Menu, MenuButton, MenuItem, MenuItems } from '@src/components/ui-kit/menu';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import DeleteIcon from '@src/icons/delete-icon.svg';
import ThreeDots from '@src/icons/three_dots.svg';
import { ideApi } from '@src/index';
import { ChatId } from '@src/models/chat.model';
import { Agent } from '@src/models/custom-agents.model';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import isEqual from 'lodash.isequal';
import React, { useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getUniqueAgentCommandName } from './helpers/get-unique-agent-command';
import { normalizeCommandName } from './helpers/normalize-command-name';

interface RoutingParams extends Record<string, string | undefined> {
  chatId: ChatId;
}

export default function EditCustomAgentsView() {
  const { agentId } = useParams<RoutingParams>();
  const { state } = useAppStore();
  const { customAgents, settings } = state;
  const [editingAgent, setEditingAgent] = React.useState<Agent>();
  const [initialEditingAgent, setInitialEditingAgent] = React.useState<Agent>();
  const [isDeleteAgentDialogVisible, setIsDeleteAgentDialogVisible] = React.useState(false);
  const [shouldNavigateAway, setShouldNavigateAway] = useState(false);
  const [navigationPath, setNavigationPath] = useState('');

  const handleDeleteAgentDialogClick = (confirm: boolean) => {
    setIsDeleteAgentDialogVisible(false);
    if (confirm && editingAgent) {
      onDelete(editingAgent);
    }
  };
  const isNewAgent = agentId === 'new';
  const rawZencoderAgents = useZencoderAgents();
  // Memoize zencoderAgents to prevent unnecessary re-renders
  const zencoderAgents = useMemo(
    () => rawZencoderAgents,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const isZencoderAgent = zencoderAgents.some((a) => a.id === agentId);
  const navigate = useNavigate();
  const { tools, isLoading: isToolsLoading } = useMcpTools();

  const installedTools = useMemo(
    () =>
      Object.entries(tools)
        .map(([key, value]) => {
          return {
            ...value,
            id: key,
          };
        })
        .filter((tool) => tool.status.status === 'installed'),
    [tools]
  );

  useEffect(() => {
    if (editingAgent) {
      return;
    }
    if (isNewAgent && !isToolsLoading) {
      setEditingAgent({
        tools: installedTools.filter((tool) => tool.author?.toLowerCase().startsWith('zencoder')),
        name: '',
        commandName: '',
        command: '',
        id: '',
      });
    } else {
      const agent = [...zencoderAgents, ...(customAgents ?? [])].find((a) => a.id === agentId);

      if (agent) {
        setEditingAgent(agent);
        setInitialEditingAgent(agent);
      }
    }
  }, [
    agentId,
    customAgents,
    editingAgent,
    installedTools,
    isNewAgent,
    isToolsLoading,
    zencoderAgents,
  ]);

  const isValid =
    !!editingAgent?.name.trim() &&
    !!editingAgent?.command.trim() &&
    !!editingAgent?.commandName?.trim();

  const isChanged = !isEqual(initialEditingAgent, editingAgent);

  const onSave = (edit: boolean) => {
    if (!editingAgent) {
      return;
    }

    let commandName = editingAgent.commandName && normalizeCommandName(editingAgent.commandName);

    if (commandName) {
      commandName = getUniqueAgentCommandName({
        commandName,
        agentId: editingAgent?.id,
        agents: customAgents ?? [],
        defaultCommandNames: DEFAULT_SLASH_COMMAND_NAMES,
      });
    }

    const agent: Agent = {
      ...editingAgent,
      command: editingAgent.command.trim(),
      name: editingAgent.name.trim(),
      commandName,
    };

    if (edit) {
      ideApi.postMessage({
        type: 'updateCustomAgent',
        agent,
      });
    } else {
      ideApi.postMessage({
        type: 'saveCustomAgent',
        agent,
      });
    }

    setNavigationPath(`/${Routes.customAgents}`);
    setShouldNavigateAway(true);
  };

  const onDelete = (agent: Agent) => {
    ideApi.postMessage({
      type: 'deleteCustomAgent',
      id: agent.id,
    });

    setNavigationPath(`/${Routes.customAgents}`);
    setShouldNavigateAway(true);
  };

  useEffect(() => {
    ideApi.postMessage({
      type: 'getCustomAgents',
    });
  }, []);

  useEffect(() => {
    if (shouldNavigateAway) {
      navigate(navigationPath);
      setShouldNavigateAway(false);
    }
  }, [shouldNavigateAway, navigate, navigationPath]);

  const canEdit = useMemo(() => {
    if (isNewAgent) {
      return true;
    }

    if (!editingAgent || !settings) {
      return false;
    }

    return editingAgent.author === settings.userId;
  }, [editingAgent, isNewAgent, settings]);

  if (!customAgents || !editingAgent) {
    return <BaseLoaderView>Loading...</BaseLoaderView>;
  }

  return (
    <SettingsLayout
      backPage={{
        path: `/${Routes.customAgents}`,
        label: 'AI agents',
      }}
    >
      <div className={'py-4'}>
        <div className={'flex flex-col text-foreground gap-y-2'}>
          <div className={'flex flex-row justify-between items-center select-none px-4'}>
            <h1 className={'flex flex-nowrap overflow-hidden m-0'}>
              {isNewAgent ? 'New agent' : 'Edit agent'}
            </h1>
            {!isZencoderAgent && (
              <div className={'flex flex-row flex-nowrap gap-x-1'}>
                <PrimaryButton
                  onClick={() => onSave(!isNewAgent)}
                  disabled={!canEdit || !isValid || !isChanged}
                >
                  {isNewAgent ? 'Create' : 'Save'}
                </PrimaryButton>
                {!isNewAgent && (
                  <Menu>
                    <MenuButton as={'div'} onClick={(e) => e.stopPropagation()} disabled={!canEdit}>
                      <LinkButton size={'sm'}>
                        <ThreeDots />
                      </LinkButton>
                    </MenuButton>
                    <MenuItems unmount anchor={'bottom end'} className={'pb-1 flex flex-col'}>
                      <MenuItem>
                        <LinkButton
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsDeleteAgentDialogVisible(true);
                          }}
                          className={
                            'flex items-center pl-1 pr-2.5 justify-start text-nowrap w-full font-normal'
                          }
                        >
                          <DeleteIcon />
                          Delete
                        </LinkButton>
                      </MenuItem>
                    </MenuItems>
                  </Menu>
                )}
              </div>
            )}
          </div>
          <div className={'px-4'}>
            <CustomAgentEditFormView
              initialAgent={initialEditingAgent}
              editingAgent={editingAgent}
              installedTools={installedTools}
              onChange={setEditingAgent}
              showDetails={!isZencoderAgent}
              canEdit={canEdit}
              shouldNavigateAway={shouldNavigateAway}
              canShare={
                !isZencoderAgent &&
                !!(
                  settings?.zencoderPermissions?.includes('zencoder.agents.share') ||
                  // TODO: Remove fallback to roles after permissions rolled out to prod
                  settings?.zencoderRoles?.some((role) => role === 'Admin' || role === 'Manager')
                )
              }
            />
          </div>
        </div>
      </div>
      <ModalDialog
        open={isDeleteAgentDialogVisible}
        onClose={() => handleDeleteAgentDialogClick(false)}
      >
        <DialogTitle>Delete agent</DialogTitle>
        Are you sure you want to delete this agent permanently?
        <div className={'mt-4 flex justify-end gap-x-2'}>
          <SecondaryButton onClick={() => handleDeleteAgentDialogClick(false)}>
            Cancel
          </SecondaryButton>
          <PrimaryButton
            onClick={() => handleDeleteAgentDialogClick(true)}
            className={
              'bg-error-button-background data-[hover]:bg-error-button-active-background data-[active]:bg-error-button-active-background'
            }
          >
            Delete
          </PrimaryButton>
        </div>
      </ModalDialog>
    </SettingsLayout>
  );
}
