import { BaseLoaderView } from '@src/components/loaders/base-loader.view';
import { Input } from '@src/components/ui-kit/form-elements';
import SearchIcon from '@src/icons/search.svg';
import { useMemo, useState } from 'react';
import AgentCard from './custom-agent-card.view';
import { useLibraryAgents } from './use-library-agents';
import { sortAgents } from './utils';

export function LibraryAgentsView() {
  const { agents, failedToLoad } = useLibraryAgents();
  const [query, setQuery] = useState('');

  const filteredAgents = useMemo(() => {
    if (query !== '') {
      return sortAgents(
        agents.filter((agent) => agent.name.toLowerCase().includes(query.toLowerCase()))
      );
    }

    return sortAgents(agents);
  }, [agents, query]);

  if (!agents.length) {
    if (failedToLoad) {
      return <p className="mx-4">Failed to load agents. Please try again later.</p>;
    }

    return <BaseLoaderView>Loading...</BaseLoaderView>;
  }

  return (
    <>
      <div className="px-4 mb-1">
        <div className="relative">
          <SearchIcon className="w-4 h-4 absolute top-1/2 left-2 -translate-y-1/2 text-input-placeholder-foreground" />
          <Input
            className="w-full pl-7"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search"
          />
        </div>
        {!!query && agents.length > 0 && filteredAgents.length === 0 && (
          <p className="mt-3">No agents found</p>
        )}
      </div>
      <div className="grid grid-cols-1 gap-4">
        {filteredAgents.map((agent) => (
          <AgentCard key={agent.id} agent={agent} showInstallTools={false} />
        ))}
      </div>
    </>
  );
}
