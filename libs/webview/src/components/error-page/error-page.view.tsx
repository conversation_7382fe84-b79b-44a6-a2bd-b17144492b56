import * as Sentry from '@sentry/react';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useEffect, useMemo } from 'react';
import { useRouteError } from 'react-router-dom';
import { BaseLoaderView } from '../loaders/base-loader.view';

interface ErrorType {
  statusText?: string;
  message?: string;
}

export function ErrorPage() {
  const error = useRouteError();
  const analytics = useAnalytics();
  const errorText = useMemo(() => {
    return typeof error === 'string'
      ? error
      : (error as ErrorType)?.message || (error as ErrorType)?.statusText;
  }, [error]);

  useEffect(() => {
    const errorToReport =
      error instanceof Error
        ? error
        : new Error(errorText || 'WebView: global routing error is shown');
    Sentry.captureException(errorToReport, {
      tags: {
        webview_routing_error: true,
      },
    });
    analytics.track('Global routing error', { error_text: errorText });
  }, [errorText]);

  return (
    <BaseLoaderView>
      <p>Sorry, an unexpected error has occurred.</p>
      <p>
        <i>{errorText}</i>
      </p>
    </BaseLoaderView>
  );
}
