import { PlaceholderElement } from '@src/components/chat-view/chat-input-field-view/message-editor/placeholder-element.view';
import { useCodingAgentDefaultState } from '@src/hooks/use-coding-agent-default-state';
import { ChatSettings } from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { trimMessage } from '@src/utils/chat.helpers';
import { useCallback, useEffect, useState } from 'react';
import { ideApi } from '../..';
import {
  ChatInputField,
  UserMessage,
} from '../chat-view/chat-input-field-view/chat-input-field.view';
import { BaseLoaderView } from '../loaders/base-loader.view';
import { PrimaryButton, SecondaryButton } from '../ui-kit/button';
import { DialogTitle, ModalDialog } from '../ui-kit/dialog';
import { Chats } from './chats.view';
import { EmptyStateView } from './empty-state.view';

export function ChatHistoryView() {
  const { state, dispatch } = useAppStore();
  const { chatSessions, repoIndexData, settings, clearChatsDialogShown } = state;
  const { areChatSessionsPersisted } = settings ?? {};
  const [isStreaming, setIsStreaming] = useState(false);
  const [isCodingAgentEnabledByDefault, setIsCodingAgentEnabledByDefault] =
    useCodingAgentDefaultState();
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    isCurrentFileUsageEnabled: true,
    isRagSearchEnabled: repoIndexData?.isIndexingEnabled,
    isAgent: isCodingAgentEnabledByDefault,
    isCustomAgent: false,
    isUnitTestsAgent: false,
  });

  useEffect(() => {
    ideApi.postMessage({
      type: 'chatSessionsRequired',
    });
  }, []);

  useEffect(() => {
    setChatSettings((prevSettings) => ({
      ...prevSettings,
      isRagSearchEnabled: repoIndexData?.isIndexingEnabled,
    }));
  }, [repoIndexData?.isIndexingEnabled]);

  const handleSendMessage = useCallback(
    (message: UserMessage) => {
      setIsCodingAgentEnabledByDefault(message.chatSettings.isAgent);
      ideApi.postMessage({
        type: 'startNewChat',
        message: trimMessage(message),
      });
      setIsStreaming(true);
    },
    [setIsCodingAgentEnabledByDefault]
  );

  const handleClearChatsDialogClick = useCallback(
    (confirm: boolean) => {
      if (confirm) {
        ideApi.postMessage({
          type: 'clearChatsHistory',
        });
      }

      dispatch({
        type: 'set-clear-chats-dialog-shown',
        isShown: false,
      });
    },
    [dispatch]
  );

  if (!chatSessions) {
    return <BaseLoaderView>Loading chat sessions...</BaseLoaderView>;
  }

  const isEmpty = !chatSessions.length;

  return (
    <div
      className="flex flex-col text-foreground h-full bg-background select-none overflow-hidden"
      role="application"
      aria-label="Chat history view"
    >
      {isEmpty ? (
        <EmptyStateView areChatSessionsPersisted={!!areChatSessionsPersisted} />
      ) : (
        <Chats areChatSessionsPersisted={!!areChatSessionsPersisted} chatSessions={chatSessions} />
      )}
      <ChatInputField
        className={'p-1 pt-0 sticky bottom-0'}
        onCommit={handleSendMessage}
        onSetChatSettings={setChatSettings}
        placeholder={<PlaceholderElement />}
        chatSettings={chatSettings}
        storageKey={'chat_history_draft'}
        isChatInProgress={isStreaming}
        aria-label="New chat input field"
        buttonCaption="Send"
        isFirstMessage
      />
      <ModalDialog
        open={!!clearChatsDialogShown}
        onClose={() => handleClearChatsDialogClick(false)}
      >
        <DialogTitle>Clear chats history</DialogTitle>
        Are you sure you want to delete all chats?
        <div className={'mt-4 flex justify-end gap-x-2'}>
          <SecondaryButton onClick={() => handleClearChatsDialogClick(false)}>No</SecondaryButton>
          <PrimaryButton onClick={() => handleClearChatsDialogClick(true)}>Yes</PrimaryButton>
        </div>
      </ModalDialog>
    </div>
  );
}
