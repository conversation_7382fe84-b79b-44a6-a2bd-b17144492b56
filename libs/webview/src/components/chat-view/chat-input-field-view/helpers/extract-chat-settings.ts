import { ChatSettings, MessageContent } from '@src/models/chat.model';
import { Agent } from '@src/models/custom-agents.model';

export function extractChatSettings(
  content: MessageContent,
  customAgents: Agent[]
): Partial<ChatSettings> {
  if (!content.length) {
    return {};
  }
  const firstPart = content[0]!;
  if (firstPart.type === 'command' && firstPart.commandName === 'code') {
    return {
      isAgent: true,
      isUnitTestsAgent: false,
      isCustomAgent: false,
    };
  }

  if (firstPart.type === 'command' && firstPart.commandName === 'unittests') {
    return {
      isAgent: false,
      isUnitTestsAgent: true,
      isCustomAgent: false,
    };
  }

  if (firstPart.type === 'command' && firstPart.commandName === 'e2e-test') {
    return {
      isAgent: false,
      isE2EAgent: true,
      isUnitTestsAgent: false,
      isCustomAgent: false,
    };
  }

  if (firstPart.type === 'command' && firstPart.customAgentId) {
    return {
      isAgent: false,
      isUnitTestsAgent: false,
      isCustomAgent: true,
      customAgent: customAgents.find((agent) => agent.id === firstPart.customAgentId),
    };
  }

  return {};
}
