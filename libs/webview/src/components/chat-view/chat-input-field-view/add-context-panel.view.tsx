import MessageContextView from '@src/components/chat-view/chat-input-field-view/message-context.view';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useWindowEvent } from '@src/hooks/use-window-event';
import { ideApi } from '@src/index';
import { ChatAttachedFile, ChatMessageContext, ChatSettings } from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { useCallback, useEffect, useMemo } from 'react';
import { twJoin } from 'tailwind-merge';

interface Props {
  context: ChatMessageContext;
  settings: ChatSettings;
  onChangeContext: (context: ChatMessageContext) => void;
  onChangeSettings: (settings: ChatSettings) => void;
}

export interface FilesSelectedEvent {
  files: ChatAttachedFile[];
}

export const menuIconClassName = twJoin('me-2', '!hidden', '2xs:!inline-block');

export function AddContextPanelView({
  context,
  settings,
  onChangeContext,
  onChangeSettings,
}: Props) {
  const { state } = useAppStore();
  const { activeFile } = state;
  const analytics = useAnalytics();

  useEffect(() => {
    ideApi.postMessage({
      type: 'requireRepoIndexData',
    });
  }, []);

  const attachedFilesFsPaths = useMemo(() => {
    return new Set(context.attachedFiles?.map((f) => f.fsPath) ?? []);
  }, [context.attachedFiles]);

  // TODO: refactor to use a single "protocol" for sending and receiving messages
  const onFilesSelectedFromComputer = useCallback(
    (data: FilesSelectedEvent) => {
      if (containsSameFiles(attachedFilesFsPaths, data.files)) {
        ideApi.postMessage({
          type: 'showWarningMessage',
          message: 'Adding the same file twice is not allowed',
        });
        return;
      }

      analytics.track('File added to context', {
        from_computer: true,
      });
      onChangeContext({
        ...context,
        attachedFiles: [...(context.attachedFiles ?? []), ...data.files],
      });
    },
    [attachedFilesFsPaths, analytics, onChangeContext, context]
  );

  const onChangeAttachedFiles = useCallback(
    (newAttachedFiles: ChatMessageContext['attachedFiles']) => {
      onChangeContext({
        ...context,
        attachedFiles: newAttachedFiles,
      });
      analytics.track('File added to context', {
        from_computer: false,
      });
    },
    [onChangeContext, context, analytics]
  );

  useWindowEvent('filesSelected', onFilesSelectedFromComputer);

  const onFileClick = (allowImages: boolean) => {
    ideApi.postMessage({
      type: 'selectFile',
      allowImages,
    });
  };

  const onRagSearchToggle = () => {
    onChangeSettings({
      ...settings,
      isRagSearchEnabled: !settings.isRagSearchEnabled,
    });
    onChangeContext({
      ...context,
      codebaseEnabled: !settings.isRagSearchEnabled,
    });
  };

  const onCurrentFileToggle = () => {
    onChangeSettings({
      ...settings,
      isCurrentFileUsageEnabled: !settings.isCurrentFileUsageEnabled,
    });
    onChangeContext({
      ...context,
      currentFile: activeFile,
    });
  };

  return (
    // [YY] menu animation sometimes makes react app to crash.
    // E.g., when attaching files from menu
    // TODO: return animation and fix bugs
    // <MenuItems
    //   transition={true}
    //   className={twJoin(
    //     'transition-max-height duration-300 ease-out overflow-hidden',
    //     // [YY] NB - Don't forget to update max height when adding new items
    //     'max-h-60',
    //     'data-[closed]:max-h-0',
    //     'data-[closed]:duration-0',
    //     'data-[enter]:max-h-0',
    //     'data-[enter]:duration-0'
    //   )}
    // >
    <MessageContextView
      position={'top start'}
      isRagEnabled={!!settings.isRagSearchEnabled}
      isCurrentFileEnabled={!!settings.isCurrentFileUsageEnabled}
      attachedFiles={context.attachedFiles}
      onChangeAttachedFiles={onChangeAttachedFiles}
      onFileClick={onFileClick}
      onToggleRag={onRagSearchToggle}
      onCurrentFileToggle={onCurrentFileToggle}
    />
  );
}

function containsSameFiles(
  currentFilesFsPaths: Set<string>,
  newFiles: ChatAttachedFile[]
): boolean {
  return newFiles.some(({ fsPath }) => currentFilesFsPaths.has(fsPath));
}
