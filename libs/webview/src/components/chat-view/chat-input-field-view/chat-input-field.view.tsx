import { extractChatSettings } from '@src/components/chat-view/chat-input-field-view/helpers/extract-chat-settings';
import { Field, Label } from '@src/components/ui-kit/form-elements';
import { Kbd } from '@src/components/ui-kit/kbd';
import { Switch } from '@src/components/ui-kit/switch';
import { Tooltip, useTooltipId } from '@src/components/ui-kit/tooltip';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useLocalStorage } from '@src/hooks/use-local-storage';
import { useWindowEvent } from '@src/hooks/use-window-event';
import ClipIcon from '@src/icons/clip.svg';
import SendIcon from '@src/icons/send.svg';
import StopIcon from '@src/icons/stop.svg';
import { ideApi } from '@src/index';
import {
  ChatMessage,
  ChatMessageContext,
  ChatSearchFile,
  ChatSettings,
  isAgenticChat,
} from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { AttachmentOptionsProvider } from '@src/store/attachment-options.context';
import { FocusPosition, JSONContent } from '@tiptap/core';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { twJoin, twMerge } from 'tailwind-merge';
import { LinkButton } from '../../ui-kit/button';
import { Menu, MenuButton } from '../../ui-kit/menu';
import { AddContextPanelView } from './add-context-panel.view';
import { AttachmentsPanelView } from './attachments-panel.view';
import { convertToMessageContent } from './helpers/convert-to-message-content';
import { findInChatInput } from './helpers/find-in-chat-input';
import { MessageEditor } from './message-editor/message-editor';
import {
  getCustomAgentTooltipId,
  ZENCODER_AGENT_TOOLTIP_ID,
} from './message-editor/slash-command.extension';
import { MessageEditorRef } from './message-editor/types';
import {
  containsImageAttachments,
  UnsupportedAttachmentsDialog,
} from './unsupported-attachments.dialog';

export interface UserMessage extends Pick<ChatMessage, 'content' | 'rawContent' | 'context'> {
  chatSettings: ChatSettings;
}

export interface EditedChatMessage extends ChatMessage {
  chatSettingsUpdate: Partial<ChatSettings>;
}

interface ChatInputFieldProps {
  className?: string;
  onCommit: (message: UserMessage) => void;
  onSetChatSettings: (settings: ChatSettings) => void;
  onStop?: () => void;
  initialValue?: string;
  placeholder: string | React.ReactNode;
  isChatInProgress: boolean;
  autoFocus?: boolean;
  chatSettings: ChatSettings;
  buttonCaption: string;
  isFirstMessage?: boolean;
  isMessageEditing?: boolean;
  showCodingAgentSwitch?: boolean;
  agentSwitchTooltip?: string;
  /**
   * A key to store unsaved data in localStorage
   */
  storageKey: string;
}

interface InsertIntoChatEvent {
  code: string;
  language?: string;
}

export function ChatInputField({
  className,
  onCommit,
  onSetChatSettings,
  onStop,
  placeholder,
  isChatInProgress,
  chatSettings,
  storageKey,
  autoFocus,
  agentSwitchTooltip = 'Uses advanced AI workflows to perform common development tasks, such as bug fixing, refactoring, or new feature development. Recommended for complex queries.',
  isFirstMessage,
  showCodingAgentSwitch = true,
}: ChatInputFieldProps) {
  const { state } = useAppStore();
  const { activeFile, customAgents, settings } = state;
  const { isMacOs } = settings ?? {};
  const codingAgentToolTipId = useTooltipId();
  const [isEditorEmpty, setIsEditorEmpty] = useState(true);
  const [context, setContext] = useState<ChatMessageContext>({});
  const [isUnsupportedAttachmentsDialogOpen, setUnsupportedAttachmentsDialogOpen] = useState(false);
  const [draftValue, setDraftValue] = useLocalStorage<JSONContent | undefined>(
    storageKey,
    undefined
  );
  const canToggleCodingAgent = !isChatInProgress && isFirstMessage;
  const toggleAgentShortcut = isMacOs ? '⌘.' : 'Ctrl+.';

  const editorRef = useRef<MessageEditorRef>();

  useEffect(() => {
    editorRef.current?.replace(
      (node) => {
        const notExistingInContext = !context?.attachedFiles?.some(
          ({ fsPath }) => fsPath === node.attrs.fsPath
        );
        return node.type.name === 'mention' && node.attrs.type === 'file' && notExistingInContext;
      },
      (node) => node.attrs.label
    );
  }, [context?.attachedFiles]);

  useEffect(() => {
    setContext((prevState) => ({
      ...prevState,
      currentFile: chatSettings.isCurrentFileUsageEnabled ? activeFile : undefined,
      codebaseEnabled: chatSettings.isRagSearchEnabled ?? false,
    }));
  }, [activeFile, chatSettings.isCurrentFileUsageEnabled, chatSettings.isRagSearchEnabled]);
  const analytics = useAnalytics();
  const onInsertIntoChat = useCallback(
    (msg: InsertIntoChatEvent) => {
      if (!editorRef.current) {
        return;
      }
      editorRef.current.insertCodeSnippet(msg.code, msg.language);
      analytics.track('Inserted code snippet into chat');
    },
    [analytics, editorRef]
  );

  useWindowEvent('insertIntoChat', onInsertIntoChat);

  useEffect(() => {
    checkIsEmpty();
  }, []);

  const focusEditor = useCallback(
    (position?: FocusPosition) => {
      editorRef.current?.focus?.(position);
    },
    [editorRef]
  );

  useEffect(() => {
    if (autoFocus) {
      focusEditor('end');
    }
  }, [autoFocus, focusEditor]);

  useEffect(() => {
    ideApi.postMessage({
      type: 'getCustomAgents',
    });
  }, []);

  const handleCommit = useCallback(
    (autoApply = false) => {
      if (isChatInProgress) {
        return;
      }
      if (!editorRef.current || editorRef.current.isEmpty()) {
        return;
      }
      const content = convertToMessageContent(editorRef.current.getValue());

      const updatedChatSettings = {
        ...chatSettings,
        ...extractChatSettings(content, customAgents ?? []),
        autoApply,
      };

      if (autoApply && canToggleCodingAgent) {
        // Always enable Coding Agent mode in Coffee mode
        updatedChatSettings.isAgent = true;
      }

      if (
        !isAgenticChat(updatedChatSettings) &&
        containsImageAttachments(context.attachedFiles ?? [])
      ) {
        setUnsupportedAttachmentsDialogOpen(true);
        return;
      }

      onCommit({
        content,
        rawContent: editorRef.current.getValue(),
        context,
        chatSettings: updatedChatSettings,
      });
      setDraftValue(undefined);
      editorRef.current.clearContent();
      setContext({
        currentFile: context.currentFile,
        codebaseEnabled: context.codebaseEnabled,
      });
    },
    [
      isChatInProgress,
      chatSettings,
      customAgents,
      canToggleCodingAgent,
      onCommit,
      context,
      setDraftValue,
    ]
  );

  const handleStop = useCallback(() => {
    onStop?.();
  }, [onStop]);

  const checkIsEmpty = () => {
    setIsEditorEmpty(!editorRef.current || editorRef.current.isEmpty());
  };

  const onEditorChange = useCallback(() => {
    if (editorRef.current) {
      const value = editorRef.current.getValue();

      // Let's not save empty lines
      if (editorRef.current.isEmpty()) {
        setDraftValue(undefined);
      } else {
        setDraftValue(value);
      }

      const command = findInChatInput('command', value.content)[0];
      const mentions = findInChatInput('mention', value.content);
      const customAgent = !!command
        ? customAgents?.find((agent) => agent.commandName === command.attrs?.label)
        : undefined;

      onSetChatSettings({
        ...chatSettings,
        isCustomAgent: !!customAgent,
        customAgent: customAgent,
        isUnitTestsAgent: command?.attrs?.label === 'unittests',
        isE2EAgent: command?.attrs?.label === 'e2e-test',
      });
      if (!!mentions.length) {
        const mentionFiles: ChatSearchFile[] = mentions
          .filter((mention) => mention.attrs?.type === 'file')
          .map(({ attrs }) => {
            return {
              fsPath: attrs!.fsPath,
              path: attrs!.path,
              language: attrs!.language,
            };
          });
        setContext((prevState) => {
          const merged = [...mentionFiles, ...(prevState.attachedFiles ?? [])];
          const uniqueByPath = Array.from(
            new Map(merged.map((item) => [item.fsPath, item])).values()
          );
          return {
            ...prevState,
            attachedFiles: uniqueByPath,
          };
        });
      }
    }
    checkIsEmpty();
  }, [chatSettings, customAgents, onSetChatSettings, setContext, setDraftValue]);

  const onIsAgentChecked = useCallback(
    (isChecked: boolean) => onSetChatSettings({ ...chatSettings, isAgent: isChecked }),
    [onSetChatSettings, chatSettings]
  );

  const handleToggleCodingAgent = useCallback(() => {
    if (!canToggleCodingAgent) {
      return;
    }
    onIsAgentChecked(!chatSettings.isAgent);
  }, [canToggleCodingAgent, onIsAgentChecked, chatSettings.isAgent]);

  const menuIsOpen = useRef(false);

  return (
    <AttachmentOptionsProvider allowImages={isAgenticChat(chatSettings)}>
      {chatSettings.customAgent && (
        <Tooltip place="top" id={getCustomAgentTooltipId(chatSettings.customAgent.id)}>
          {chatSettings.customAgent.isSharedWithOrganization ? 'Org agent' : 'Personal agent'}
        </Tooltip>
      )}
      <Tooltip place="top" id={ZENCODER_AGENT_TOOLTIP_ID}>
        Zencoder agent
      </Tooltip>
      <Menu>
        {({ open }) => {
          // onClose basically, headless ui doesn't have this event
          if (!open && menuIsOpen.current) {
            focusEditor();
          }

          menuIsOpen.current = open;

          return (
            <div className={twMerge('bg-background', className)}>
              <div
                className={twJoin(
                  'border',
                  'bg-editor-background',
                  'rounded-lg',
                  'overflow-hidden',
                  'focus-within:ring',
                  'active:ring'
                )}
                onClick={() => focusEditor()}
              >
                <MessageEditor
                  ref={editorRef}
                  className={'min-h-20'}
                  slashCommandsAvailable={true}
                  initialContent={draftValue}
                  onChange={onEditorChange}
                  onCommit={handleCommit}
                  onToggleAgent={handleToggleCodingAgent}
                  placeholder={placeholder}
                  isFirstMessage={isFirstMessage}
                />
                <div className="p-1 pt-2">
                  <AttachmentsPanelView
                    context={context}
                    settings={chatSettings}
                    onChangeContext={setContext}
                    onChangeSettings={onSetChatSettings}
                  />
                  <AddContextPanelView
                    context={context}
                    settings={chatSettings}
                    onChangeContext={setContext}
                    onChangeSettings={onSetChatSettings}
                  />
                  <div className={'flex items-center gap-2 select-none'}>
                    <MenuButton
                      aria-label="Add context or settings"
                      as={LinkButton}
                      className="p-0.5 flex items-center gap-0.5"
                      disabled={isChatInProgress}
                    >
                      <ClipIcon className="w-4 h-4 block flex-none" />
                      <span
                        className={twJoin('text-secondary-foreground text-xs', 'hidden 2xs:block')}
                      >
                        Context
                      </span>
                    </MenuButton>
                    {showCodingAgentSwitch && (
                      <>
                        <Field
                          disabled={!canToggleCodingAgent}
                          className="flex items-center gap-0.5 cursor-pointer"
                          data-tooltip-id={codingAgentToolTipId}
                        >
                          <Switch checked={!!chatSettings.isAgent} onChange={onIsAgentChecked} />
                          <Label className="text-xs text-secondary-foreground ">
                            Coding&nbsp;agent
                          </Label>
                        </Field>
                        {!!agentSwitchTooltip && (
                          <Tooltip id={codingAgentToolTipId}>
                            <span className="font-semibold">Coding Agent:</span>&nbsp;
                            {agentSwitchTooltip}{' '}
                            {canToggleCodingAgent && <Kbd caption={toggleAgentShortcut} />}
                          </Tooltip>
                        )}
                      </>
                    )}
                    <div className={'flex-1'} />
                    {isChatInProgress ? (
                      <LinkButton
                        aria-label="Stop chat streaming"
                        className="p-0.5"
                        onClick={handleStop}
                      >
                        <StopIcon className="w-4 h-4 block" />
                      </LinkButton>
                    ) : (
                      <LinkButton
                        aria-label="Chat send button"
                        onClick={() => handleCommit(false)}
                        disabled={isEditorEmpty}
                        className="p-0.5"
                      >
                        <SendIcon className="w-4 h-4 block" />
                      </LinkButton>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        }}
      </Menu>
      <UnsupportedAttachmentsDialog
        isOpen={isUnsupportedAttachmentsDialogOpen}
        close={() => setUnsupportedAttachmentsDialogOpen(false)}
      />
    </AttachmentOptionsProvider>
  );
}
