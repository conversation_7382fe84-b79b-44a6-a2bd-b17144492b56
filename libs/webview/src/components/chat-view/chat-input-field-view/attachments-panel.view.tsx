import ActiveFileTag from '@src/components/chat-view/chat-message-view/message-components/active-file-tag.view';
import RagSearchTag from '@src/components/chat-view/chat-message-view/message-components/rag-search-tag.view';
import { ChatMessageContext, ChatSettings } from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { getFileName } from '@src/utils/get-file-name';
import { useCallback, useMemo } from 'react';
import { AttachedFileTag } from '../chat-message-view/message-components/attached-file-tag.view';

interface Props {
  context: ChatMessageContext;
  settings: ChatSettings;
  onChangeContext: (context: ChatMessageContext) => void;
  onChangeSettings: (settings: ChatSettings) => void;
}

export function AttachmentsPanelView({
  context,
  settings,
  onChangeContext,
  onChangeSettings,
}: Props) {
  const { state } = useAppStore();
  const { activeFile } = state;
  const activeFileName = activeFile?.fsPath && getFileName(activeFile.fsPath);

  const onRemove = useCallback(
    (fsPath: string) => {
      const attachedFiles = [...(context.attachedFiles ?? [])];
      const index = attachedFiles.findIndex((f) => f.fsPath === fsPath);
      if (index !== -1) {
        attachedFiles.splice(index, 1);

        onChangeContext({
          ...context,
          attachedFiles,
        });
      }
    },
    [context, onChangeContext]
  );

  const nodes = useMemo(() => {
    const result = [];

    if (settings.isCurrentFileUsageEnabled && activeFileName) {
      result.push(
        <ActiveFileTag
          key={'activeFile'}
          activeFileName={activeFileName}
          onRemoveClick={() => {
            onChangeSettings({
              ...settings,
              isCurrentFileUsageEnabled: false,
            });
            onChangeContext({
              ...context,
              currentFile: undefined,
            });
          }}
        />
      );
    }

    if (settings.isRagSearchEnabled) {
      result.push(
        <RagSearchTag
          key={'rag'}
          onRemoveClick={() => {
            onChangeSettings({
              ...settings,
              isRagSearchEnabled: false,
            });
            onChangeContext({
              ...context,
              codebaseEnabled: false,
            });
          }}
        />
      );
    }

    (context.attachedFiles ?? []).forEach((file) => {
      result.push(
        <AttachedFileTag
          key={file.fsPath}
          file={file}
          onRemoveClick={() => onRemove(file.fsPath)}
          openFileOnClick={true}
        />
      );
    });

    return result;
  }, [activeFileName, context, onChangeContext, onChangeSettings, onRemove, settings]);

  if (!nodes.length) {
    return null;
  }

  return <div className="pb-2 flex items-center flex-wrap gap-1">{nodes}</div>;
}
