import { SecondaryButton } from '@src/components/ui-kit/button';
import { DialogTitle, ModalDialog } from '@src/components/ui-kit/dialog';
import { ChatAttachedFile, ChatSearchFile } from '@src/models/chat.model';

const LANGUAGE_IMAGE = 'image-0eadcea7-d0df-4585-a904-c4d230fa26b9';

type Props = {
  isOpen: boolean;
  close: () => void;
};

export const UnsupportedAttachmentsDialog = ({ isOpen, close }: Props) => (
  <ModalDialog open={isOpen} onClose={close}>
    <DialogTitle>Only agents support images</DialogTitle>
    Image attachments are only available in agent chats. To continue, please remove any attached
    images first.
    <div className={'mt-4 flex justify-end gap-x-2'}>
      <SecondaryButton onClick={close}>Close</SecondaryButton>
    </div>
  </ModalDialog>
);

export const containsImageAttachments = (files: (ChatAttachedFile | ChatSearchFile)[]) =>
  files.some((f) => (f as ChatAttachedFile).language === LANGUAGE_IMAGE);
