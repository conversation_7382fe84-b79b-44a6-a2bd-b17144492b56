import {
  AddFileMentionListItem,
  FileMentionListItem,
  FilesScreen,
  JiraScreen,
  MentionType,
  NavigationMentionListItem,
} from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import { useFilesSearch } from '@src/components/chat-view/chat-input-field-view/use-files-search';
import { useAttachmentOptions } from '@src/hooks/use-attachment-options';
import FileIcon from '@src/icons/integrations/file-icon.svg';
import JiraIcon from '@src/icons/integrations/jira-logo.svg';
import { useAppStore } from '@src/store/app.store';
import { useEffect, useMemo, useState } from 'react';

type NavigationListItem = FileMentionListItem | AddFileMentionListItem | NavigationMentionListItem;

function getDefaultNavigationItems(
  isRepoIndexingEnabled: boolean,
  isImageAttachmentAllowed: boolean
): NavigationListItem[] {
  if (!isRepoIndexingEnabled) {
    return [
      {
        type: 'add_file',
        isSuggestionAvailable: true,
      },
      {
        type: 'navigation',
        screen: JiraScreen.Issues,
        icon: <JiraIcon className={'text-secondary-foreground'} />,
        text: 'Jira',
        isSuggestionAvailable: false,
      },
    ];
  } else {
    return [
      {
        type: 'navigation',
        screen: FilesScreen.Search,
        icon: <FileIcon className={'text-secondary-foreground'} />,
        text: isImageAttachmentAllowed ? 'Files and images' : 'Files',
        isSuggestionAvailable: false,
      },
      {
        type: 'navigation',
        screen: JiraScreen.Issues,
        icon: <JiraIcon className={'text-secondary-foreground'} />,
        text: 'Jira',
        isSuggestionAvailable: false,
      },
    ];
  }
}

export default function useNavigationItems(
  query: string,
  needToUpdate: boolean
): NavigationListItem[] {
  const { state } = useAppStore();
  const { repoIndexData } = state;
  const { allowImages } = useAttachmentOptions();
  const defaultNavigationItems = useMemo(() => {
    return getDefaultNavigationItems(!!repoIndexData?.isSearchEnabled, allowImages);
  }, [repoIndexData?.isSearchEnabled, allowImages]);
  const [items, setItems] = useState<NavigationListItem[]>(defaultNavigationItems);
  const filesSearch = useFilesSearch(!!repoIndexData?.isSearchEnabled);
  useEffect(() => {
    filesSearch.requestSuggestedFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (!repoIndexData?.isSearchEnabled || !query.trim()) {
      setItems(defaultNavigationItems);
    } else if (needToUpdate) {
      filesSearch.onChangeQuery(query);
      const fileItems: FileMentionListItem[] =
        filesSearch.result?.files.map((file) => ({
          type: MentionType.File,
          item: {
            id: file.path,
            path: file.path,
            fsPath: file.fsPath,
            type: MentionType.File,
          },
          isSuggestionAvailable: true,
        })) ?? [];

      setItems(() => {
        const result: NavigationListItem[] = [];
        const defaultItems = getDefaultNavigationItems(repoIndexData.isSearchEnabled, allowImages);
        result.push(
          ...defaultItems.filter(
            (item) =>
              item.type === 'navigation' && item.text.toLowerCase().includes(query.toLowerCase())
          )
        );
        result.push(...fileItems);
        return result;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    allowImages,
    defaultNavigationItems,
    query,
    filesSearch.result?.query,
    repoIndexData?.isSearchEnabled,
  ]);
  return items;
}
