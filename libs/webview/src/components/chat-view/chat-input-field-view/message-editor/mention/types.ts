import React from 'react';

export enum MentionType {
  Jira = 'jira',
  File = 'file',
}

export enum GeneralScreen {
  Navigation = 'general.navigation',
}

export enum JiraScreen {
  SignIn = 'jira.signIn',
  Issues = 'jira.issues',
}

export enum FilesScreen {
  Search = 'files.search',
}

export type MentionScreen = GeneralScreen | JiraScreen | FilesScreen;

export type MentionItem = JiraMention | FileMention;

export type JiraMention = {
  type: MentionType.Jira;
  id: string;
  key: string;
  summary: string;
  description: string;
};

export type FileMention = {
  type: MentionType.File;
  id: string;
  path: string;
  fsPath: string;
  language?: string;
};

export type MentionListItem =
  | SuggestionAvailableMentionListItems
  | SuggestionUnavailableMentionListItems;

export type JiraIssueMentionListItem = {
  type: 'jira_issue';
  item: JiraMention;
  isSuggestionAvailable: true;
};

export type FileMentionListItem = {
  type: 'file';
  item: FileMention;
  isSuggestionAvailable: true;
};

export type ConnectJiraMentionListItem = {
  type: 'connect_jira';
  isSuggestionAvailable: false;
};

export type NavigationMentionListItem = {
  type: 'navigation';
  screen: MentionScreen;
  icon: React.ReactNode;
  text: string;
  isSuggestionAvailable: false;
};

export type AddFileMentionListItem = {
  type: 'add_file';
  isSuggestionAvailable: true;
};

export type SuggestionAvailableMentionListItems = JiraIssueMentionListItem | FileMentionListItem;

export type SuggestionUnavailableMentionListItems =
  | NavigationMentionListItem
  | AddFileMentionListItem
  | ConnectJiraMentionListItem;
