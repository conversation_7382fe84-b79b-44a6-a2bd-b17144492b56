import FilesSearchView from '@src/components/chat-view/chat-input-field-view/message-editor/mention/files/files.search.view';
import useNavigationItems from '@src/components/chat-view/chat-input-field-view/message-editor/mention/general/use-navigation-items';
import JiraIssuesView from '@src/components/chat-view/chat-input-field-view/message-editor/mention/jira/jira.issues.view';
import JiraSignInView from '@src/components/chat-view/chat-input-field-view/message-editor/mention/jira/jira.sign-in.view';
import useJiraIssues from '@src/components/chat-view/chat-input-field-view/message-editor/mention/jira/use-jira-issues';
import {
  AddFileMentionListItem,
  FileMentionListItem,
  FilesScreen,
  GeneralScreen,
  JiraIssueMentionListItem,
  JiraScreen,
  MentionListItem,
  MentionScreen,
  MentionType,
  SuggestionAvailableMentionListItems,
} from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import { useFilesSearch } from '@src/components/chat-view/chat-input-field-view/use-files-search';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useAttachmentOptions } from '@src/hooks/use-attachment-options';
import { ideApi } from '@src/index';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { Editor } from '@tiptap/react';
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import NavigationView from './general/general.navigation.view';

export type MentionListProps = {
  editor: Editor;
  query: string;
  suggestionsAvailable: boolean;
  setSuggestionsAvailable: (isAvailable: boolean) => void;
  setScreen: (screen: MentionScreen | null) => void;
  onSelectSuggestion: (item: SuggestionAvailableMentionListItems) => void;
  onClose: () => void;
  screen: MentionScreen;
};

const MentionList = forwardRef(
  (
    {
      screen,
      query,
      suggestionsAvailable,
      setSuggestionsAvailable,
      setScreen,
      onSelectSuggestion,
    }: MentionListProps,
    ref
  ) => {
    const requestId = useRef(uuidv4());
    const { allowImages } = useAttachmentOptions();

    const onAddFileClick = useCallback(() => {
      ideApi.postMessage({
        type: 'selectFileForMention',
        requestId: requestId.current,
        allowImages: allowImages,
      });
    }, [allowImages]);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const navigate = useNavigate();
    const {
      issues: jiraIssues,
      debouncedQuery,
      resetJiraIssues,
    } = useJiraIssues({
      query: query.split(':')[1] ?? '',
      enabled: screen === JiraScreen.Issues,
    });
    const { state } = useAppStore();
    const { repoIndexData, fileSelectForMention } = state;
    const navigationItems = useNavigationItems(query, screen === GeneralScreen.Navigation);
    const filesSearch = useFilesSearch(!!repoIndexData?.isSearchEnabled);
    const itemRefs = useRef<Array<HTMLDivElement | null>>([]);
    useEffect(() => {
      const file = fileSelectForMention?.[requestId.current];
      if (!!file) {
        onSelectSuggestion({
          type: 'file',
          isSuggestionAvailable: true,
          item: {
            type: MentionType.File,
            id: `file:${file.path}`,
            path: file.path,
            fsPath: file.fsPath,
            language: file.language,
          },
        });
      }
    }, [fileSelectForMention, onSelectSuggestion]);
    const items = useMemo<MentionListItem[]>(() => {
      switch (screen) {
        case GeneralScreen.Navigation:
          return navigationItems;
        case JiraScreen.SignIn:
          return [{ type: 'connect_jira', isSuggestionAvailable: false }];
        case JiraScreen.Issues: {
          const issues: JiraIssueMentionListItem[] =
            jiraIssues?.issues.map((value) => {
              return {
                type: 'jira_issue',
                item: {
                  type: MentionType.Jira,
                  ...value,
                },
                isSuggestionAvailable: true,
              };
            }) ?? [];
          return issues;
        }
        case FilesScreen.Search:
          const hasFileItemsWithQuery =
            !!filesSearch.query?.length && !!filesSearch.result?.files.length;
          const hasFileItemsWithoutQuery =
            !filesSearch.query?.length && !!filesSearch.suggestedFiles?.length;
          if (hasFileItemsWithQuery) {
            const files: FileMentionListItem[] = filesSearch.result!.files.map((file) => {
              return {
                type: MentionType.File,
                item: {
                  type: MentionType.File,
                  id: `file:${file.path}`,
                  path: file.path,
                  fsPath: file.fsPath,
                },
                isSuggestionAvailable: true,
              };
            });
            return [
              {
                type: 'add_file',
                isSuggestionAvailable: true,
              } as AddFileMentionListItem,
              ...files,
            ];
          } else if (hasFileItemsWithoutQuery) {
            const files: FileMentionListItem[] = filesSearch.suggestedFiles!.map((file) => {
              return {
                type: MentionType.File,
                item: {
                  type: MentionType.File,
                  id: `file:${file.path}`,
                  path: file.path,
                  fsPath: file.fsPath,
                },
                isSuggestionAvailable: true,
              };
            });
            return [
              {
                type: 'add_file',
                isSuggestionAvailable: true,
              } as AddFileMentionListItem,
              ...files,
            ];
          } else {
            return [];
          }
        default:
          return [];
      }
    }, [
      screen,
      navigationItems,
      filesSearch.query,
      filesSearch.result,
      filesSearch.suggestedFiles,
      jiraIssues?.issues,
    ]);

    const totalItems = items.length;

    const upHandler = () => {
      setSelectedIndex((prevIndex) => {
        const newIndex = prevIndex - 1 >= 0 ? prevIndex - 1 : totalItems - 1;
        itemRefs.current[newIndex]?.scrollIntoView({
          behavior: 'instant' as ScrollBehavior,
          block: 'nearest',
        });
        return newIndex;
      });
    };

    const downHandler = () => {
      setSelectedIndex((prevIndex) => {
        const newIndex = prevIndex + 1 < totalItems ? prevIndex + 1 : 0;
        itemRefs.current[newIndex]?.scrollIntoView({
          behavior: 'instant' as ScrollBehavior,
          block: 'nearest',
        });
        return newIndex;
      });
    };

    useEffect(() => {
      setSuggestionsAvailable(!!items[selectedIndex]?.isSuggestionAvailable);
    }, [items, selectedIndex, setSuggestionsAvailable]);

    const enterHandler = () => {
      const selectedItem = items[selectedIndex];
      if (!selectedItem) {
        return;
      }
      switch (selectedItem.type) {
        case 'navigation':
          setScreen(selectedItem.screen);
          return;
        case 'jira_issue':
          onSelectSuggestion({
            type: 'jira_issue',
            item: {
              id: selectedItem.item.id,
              key: selectedItem.item.key,
              summary: selectedItem.item.summary,
              description: selectedItem.item.description,
              type: MentionType.Jira,
            },
            isSuggestionAvailable: true,
          });
          return;
        case 'connect_jira':
          navigate(`/${Routes.integrations}`);
          return;
        case 'file':
          onSelectSuggestion({
            type: 'file',
            item: {
              type: MentionType.File,
              id: `file:${selectedItem.item.path}`,
              path: selectedItem.item.path,
              fsPath: selectedItem.item.fsPath,
            },
            isSuggestionAvailable: true,
          });
          return;
        case 'add_file':
          onAddFileClick();
          return;
        default:
          selectedItem satisfies never;
          return;
      }
    };

    useEffect(() => {
      if (itemRefs.current[selectedIndex]) {
        itemRefs.current[selectedIndex]?.scrollIntoView({
          behavior: 'auto',
          block: 'nearest',
        });
      }
    }, [selectedIndex]);

    useEffect(() => {
      setSelectedIndex(0);
    }, [screen]);

    useEffect(() => {
      ideApi.postMessage({
        type: 'requireIntegrations',
      });
    }, []);

    useEffect(() => {
      if (screen === GeneralScreen.Navigation) {
        resetJiraIssues();
      }
    }, [screen, resetJiraIssues]);

    useImperativeHandle(ref, () => ({
      onKeyDown: ({ event }: { event: KeyboardEvent }) => {
        if (event.key === 'ArrowUp') {
          upHandler();
          return true;
        }

        if (event.key === 'ArrowDown') {
          downHandler();
          return true;
        }

        if (event.key === 'Enter' || event.key === 'Tab') {
          event.preventDefault();
          event.stopPropagation();
          enterHandler();
          return true;
        }

        if (event.key === ' ') {
          if (items.length === 1 && suggestionsAvailable) {
            enterHandler();
            return true;
          }
        }

        return false;
      },
    }));

    useEffect(() => {
      if (screen === JiraScreen.Issues) {
        const issuesLength = items.length;
        if (issuesLength > 0) {
          itemRefs.current = itemRefs.current.slice(0, issuesLength);
        }
      }
    }, [items.length, screen]);

    const navigationScreen = useMemo(() => {
      switch (screen) {
        case GeneralScreen.Navigation:
          return (
            <NavigationView
              items={navigationItems}
              setScreen={setScreen}
              selectedIndex={selectedIndex}
              setSelectedIndex={setSelectedIndex}
              onSelectSuggestion={onSelectSuggestion}
              itemRefs={itemRefs}
              query={query}
              onFileClick={onAddFileClick}
            />
          );
        default:
          return null;
      }
    }, [
      onAddFileClick,
      navigationItems,
      onSelectSuggestion,
      query,
      screen,
      selectedIndex,
      setScreen,
    ]);

    const jiraScreen = useMemo(() => {
      switch (screen) {
        case JiraScreen.SignIn:
          return (
            <JiraSignInView
              setScreen={setScreen}
              onClose={() => {
                setScreen(null);
              }}
            />
          );
        case JiraScreen.Issues:
          return (
            <JiraIssuesView
              query={query.split(':')[1]?.trim() ?? ''}
              items={jiraIssues}
              debouncedQuery={debouncedQuery}
              itemRefs={itemRefs}
              setScreen={setScreen}
              selectedIndex={selectedIndex}
              setSelectedIndex={setSelectedIndex}
              setSuggestionsAvailable={setSuggestionsAvailable}
              onSelectSuggestion={onSelectSuggestion}
            />
          );
        default:
          return null;
      }
    }, [
      debouncedQuery,
      jiraIssues,
      onSelectSuggestion,
      query,
      screen,
      selectedIndex,
      setScreen,
      setSuggestionsAvailable,
    ]);

    const filesScreen = useMemo(() => {
      switch (screen) {
        case FilesScreen.Search:
          return (
            <FilesSearchView
              query={query.split(':')[1]?.trim() ?? ''}
              filesSearch={filesSearch}
              itemRefs={itemRefs}
              setSelectedIndex={setSelectedIndex}
              onSelectSuggestion={onSelectSuggestion}
              selectedIndex={selectedIndex}
              onAddFileClick={onAddFileClick}
            />
          );
        default:
          return null;
      }
    }, [onAddFileClick, filesSearch, onSelectSuggestion, query, screen, selectedIndex]);

    const analytics = useAnalytics();
    const mentionMenuOpenedSent = useRef(false);
    useEffect(() => {
      if (!mentionMenuOpenedSent.current) {
        analytics.track('Mention menu opened');
        mentionMenuOpenedSent.current = true;
      }
    }, [analytics]);

    return (
      <div
        className={
          'flex flex-col rounded-lg border overflow-x-hidden overflow-y-auto max-h-[330px] p-1 bg-editor-background w-[15rem]'
        }
      >
        {navigationScreen}
        {jiraScreen}
        {filesScreen}
      </div>
    );
  }
);

export default MentionList;
