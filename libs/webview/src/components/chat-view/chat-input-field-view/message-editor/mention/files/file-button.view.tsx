import { FileMentionListItem } from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import SearchFileTextView from '@src/components/chat-view/chat-input-field-view/search-file-text.view';
import FileIcon from '@src/icons/integrations/file-icon.svg';
import React, { MutableRefObject } from 'react';
import { twJoin } from 'tailwind-merge';

type AddFileButtonProps = {
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
};

export function AddFileButton({
  itemRefs,
  index,
  selectedIndex,
  setSelectedIndex,
  onSelect,
}: AddFileButtonProps) {
  return (
    <div
      role={'button'}
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center p-1.5 border-none min-w-[200px] gap-x-1',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
      onClick={onSelect}
    >
      <FileIcon className={'text-secondary-foreground'} />
      Select from computer...
    </div>
  );
}

type FileButtonProps = {
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  item: FileMentionListItem;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
  query: string;
};

export function FileButton({
  itemRefs,
  item,
  index,
  selectedIndex,
  setSelectedIndex,
  onSelect,
  query,
}: FileButtonProps) {
  return (
    <div
      role={'button'}
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center p-1.5 border-none min-w-[200px]',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
    >
      <SearchFileTextView
        filePath={item.item.fsPath}
        searchString={query}
        fileNameClassName={'text-base'}
        pathClassName={'text-base'}
      />
    </div>
  );
}
