import { analytics } from '@src/analytics';
import { MentionNodeView } from '@src/components/chat-view/chat-input-field-view/message-editor/mention/mention-node.view';
import {
  FilesScreen,
  GeneralScreen,
  JiraScreen,
  MentionScreen,
  MentionType,
  SuggestionAvailableMentionListItems,
} from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import { getFilePathParts } from '@src/utils/get-file-path-parts';
import { mergeAttributes } from '@tiptap/core';
import Mention from '@tiptap/extension-mention';
import { PluginKey } from '@tiptap/pm/state';
import { Editor, Range, ReactNodeViewRenderer, ReactRenderer } from '@tiptap/react';
import { SuggestionOptions } from '@tiptap/suggestion';
import tippy, { Instance } from 'tippy.js';
import MentionList, { MentionListProps } from './mention-list.view';

export const MENTION_TRIGGER_SYMBOL = '@';
export const MENTION_EXTENSION_NAME = 'mention';

export const MentionPluginKey = new PluginKey('mention');

export const suggestion: Omit<SuggestionOptions, 'editor'> = {
  char: MENTION_TRIGGER_SYMBOL,
  pluginKey: MentionPluginKey,
  allowSpaces: true,
  render: () => {
    let component: ReactRenderer<typeof MentionList, MentionListProps>;
    let popup: Instance[];

    let screen: MentionScreen = GeneralScreen.Navigation;
    let suggestionsAvailable = false;

    const getScreenFromQuery = (query: string): MentionScreen => {
      if (query.startsWith('jira:')) {
        return JiraScreen.Issues;
      } else if (query.startsWith('file:')) {
        return FilesScreen.Search;
      } else {
        return GeneralScreen.Navigation;
      }
    };

    const getPrefixForScreen = (screenType: MentionScreen): string | null => {
      if (screenType === JiraScreen.Issues) {
        return 'jira:';
      } else if (screenType === FilesScreen.Search) {
        return 'file:';
      }
      return null;
    };

    const updateQueryWithPrefix = (
      editor: Editor,
      range: Range,
      currentQuery: string,
      newScreen: MentionScreen
    ) => {
      const prefix = getPrefixForScreen(newScreen);
      if (!prefix) return;

      if (!currentQuery.startsWith(prefix)) {
        let newQuery: string;

        const lowerQuery = currentQuery.toLowerCase();
        const prefixWithoutColon = prefix.slice(0, -1).toLowerCase();

        const isQueryPrefixOfCategory =
          lowerQuery.length <= prefixWithoutColon.length &&
          prefixWithoutColon.substring(0, lowerQuery.length) === lowerQuery;

        if (isQueryPrefixOfCategory) {
          newQuery = prefix;
        } else if (currentQuery.includes(':')) {
          newQuery = prefix + currentQuery.split(':').slice(1).join(':');
        } else {
          newQuery = prefix + currentQuery;
        }

        editor
          .chain()
          .focus()
          .command(({ tr }) => {
            const start = range.from + 1;
            const end = range.from + currentQuery.length + 1;
            tr.insertText(newQuery, start, end);
            return true;
          })
          .run();
      }
    };

    const onExit = () => {
      popup?.[0]?.destroy();
      component?.destroy();
      suggestionsAvailable = false;
      screen = GeneralScreen.Navigation; // Reset to default on exit
    };

    return {
      onStart: (props) => {
        screen = getScreenFromQuery(props.query);

        component = new ReactRenderer<typeof MentionList, MentionListProps>(MentionList, {
          props: {
            ...props,
            screen,
            suggestionsAvailable,
            setSuggestionsAvailable: (isAvailable: boolean) => {
              suggestionsAvailable = isAvailable;
              component?.updateProps({
                ...props,
                range: props.range,
                screen,
                suggestionsAvailable,
              });
            },
            onSelectSuggestion: (listItem: SuggestionAvailableMentionListItems) => {
              if (suggestionsAvailable) {
                switch (listItem.type) {
                  case 'jira_issue':
                    props.command({
                      id: listItem.item.key,
                      label: listItem.item.key,
                      summary: listItem.item.summary,
                      description: listItem.item.description,
                      type: listItem.item.type,
                    });
                    break;
                  case 'file':
                    const { fileName } = getFilePathParts(listItem.item.path);
                    props.command({
                      id: listItem.item.id,
                      label: fileName,
                      path: listItem.item.path,
                      fsPath: listItem.item.fsPath,
                      type: listItem.item.type,
                      language: listItem.item.language,
                    });
                    break;
                  default:
                    listItem satisfies never;
                    break;
                }
              }
            },
            onClose: onExit,
            setScreen: (newScreen: MentionScreen | null) => {
              screen = newScreen || GeneralScreen.Navigation;

              if (newScreen) {
                updateQueryWithPrefix(props.editor, props.range, props.query, newScreen);
              }

              component?.updateProps({
                ...props,
                range: props.range,
                screen,
                suggestionsAvailable,
              });
            },
          },
          editor: props.editor,
        });

        if (!props.clientRect) {
          return;
        }

        popup = tippy('body', {
          getReferenceClientRect: props.clientRect as () => DOMRect,
          appendTo: () => document.body,
          content: component.element,
          showOnCreate: true,
          interactive: true,
          trigger: 'manual',
          placement: 'top-start',
          maxWidth: `${window.innerWidth - 100}px`,
        });
      },
      onUpdate(props) {
        screen = getScreenFromQuery(props.query);

        component?.updateProps({
          ...props,
          screen,
          suggestionsAvailable,
          setSuggestionsAvailable: (isAvailable: boolean) => {
            suggestionsAvailable = isAvailable;
            component?.updateProps({
              ...props,
              screen,
              suggestionsAvailable,
            });
          },
          onSelectSuggestion: (listItem: SuggestionAvailableMentionListItems) => {
            if (suggestionsAvailable) {
              switch (listItem.type) {
                case 'jira_issue':
                  props.command({
                    id: listItem.item.key,
                    label: listItem.item.key,
                    summary: listItem.item.summary,
                    description: listItem.item.description,
                    type: listItem.item.type,
                  });
                  break;
                case 'file':
                  const { fileName } = getFilePathParts(listItem.item.path);
                  props.command({
                    id: listItem.item.path,
                    label: fileName,
                    path: listItem.item.path,
                    fsPath: listItem.item.fsPath,
                    type: listItem.item.type,
                    language: listItem.item.language,
                  });
                  break;
                default:
                  listItem satisfies never;
                  break;
              }
            }
          },
          setScreen: (newScreen: MentionScreen | null) => {
            screen = newScreen || GeneralScreen.Navigation;

            if (newScreen) {
              updateQueryWithPrefix(props.editor, props.range, props.query, newScreen);
            }

            component?.updateProps({
              ...props,
              range: props.range,
              screen,
              suggestionsAvailable,
            });
          },
        });
        if (!props.clientRect) return;
        popup[0]?.setProps({
          getReferenceClientRect: props.clientRect as () => DOMRect,
        });
      },
      onKeyDown(props) {
        if (props.event.key === 'Escape') {
          popup[0]?.hide();
          screen = GeneralScreen.Navigation;
          suggestionsAvailable = false;
          return true;
        }
        if (!component.ref) return false;
        // @ts-expect-error
        return component.ref?.onKeyDown(props);
      },
      onExit() {
        popup[0]?.destroy();
        screen = GeneralScreen.Navigation; // Reset to default on complete exit
        suggestionsAvailable = false;
        component.destroy();
      },
    };
  },
  command: ({ editor, range, props }) => {
    if (props.type === MentionType.Jira) {
      analytics.track('Mention added', { type: 'jira' });
    } else if (props.type === MentionType.File) {
      analytics.track('Mention added', { type: 'file' });
    }
    editor
      .chain()
      .focus()
      .deleteRange(range)
      .insertContent([
        { type: MENTION_EXTENSION_NAME, attrs: { ...props } },
        { type: 'text', text: ' ' },
      ])
      .run();
    window.getSelection()?.collapseToEnd();
  },
};

export const MentionExtension = Mention.configure({
  suggestion,
}).extend({
  addNodeView() {
    return ReactNodeViewRenderer(MentionNodeView);
  },
  addAttributes() {
    return {
      label: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-label'),
        renderHTML: (attributes) => (attributes.label ? { 'data-label': attributes.label } : {}),
      },
      summary: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-summary'),
        renderHTML: (attributes) =>
          attributes.summary ? { 'data-summary': attributes.summary } : {},
      },
      description: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-description'),
        renderHTML: (attributes) => {
          return attributes.description ? { 'data-description': attributes.description } : {};
        },
      },
      type: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-type'),
        renderHTML: (attributes) => (attributes.type ? { 'data-type': attributes.type } : {}),
      },
      path: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-path'),
        renderHTML: (attributes) => (attributes.path ? { 'data-path': attributes.path } : {}),
      },
      fsPath: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-fspath'),
        renderHTML: (attributes) => (attributes.fsPath ? { 'data-fspath': attributes.fsPath } : {}),
      },
      language: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-language'),
        renderHTML: (attributes) =>
          attributes.language ? { 'data-language': attributes.language } : {},
      },
    };
  },

  parseHTML() {
    return [{ tag: `span[data-type="${this.name}"]` }];
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      mergeAttributes({ 'data-type': this.name, class: 'text-link-foreground' }, HTMLAttributes),
      ['div', { class: 'inline-flex flex-row gap-1 items-baseline' }, node.attrs.label],
    ];
  },

  renderText({ node }) {
    return node.attrs.label;
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () =>
        this.editor.commands.command(({ tr, state }) => {
          let isMention = false;
          const { selection } = state;
          if (!selection.empty) return false;
          state.doc.nodesBetween(selection.anchor - 1, selection.anchor, (node, pos) => {
            if (node.type.name === this.name) {
              isMention = true;
              tr.insertText(MENTION_TRIGGER_SYMBOL, pos, pos + node.nodeSize);
              return false;
            }
          });
          return isMention;
        }),
    };
  },
});
