import {
  AddFileMentionListItem,
  FileMentionListItem,
  MentionScreen,
  MentionType,
  NavigationMentionListItem,
  SuggestionAvailableMentionListItems,
} from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import SearchFileTextView from '@src/components/chat-view/chat-input-field-view/search-file-text.view';
import { useAttachmentOptions } from '@src/hooks/use-attachment-options';
import ArrowIcon from '@src/icons/chevron-down.svg';
import FileIcon from '@src/icons/integrations/file-icon.svg';
import React, { Dispatch, MutableRefObject, SetStateAction } from 'react';
import { twJoin } from 'tailwind-merge';

export type NavigationListItem =
  | NavigationMentionListItem
  | FileMentionListItem
  | AddFileMentionListItem;

type NavigationViewProps = {
  query: string;
  items: NavigationListItem[];
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  onSelectSuggestion: (item: SuggestionAvailableMentionListItems) => void;
  selectedIndex: number;
  setSelectedIndex: Dispatch<SetStateAction<number>>;
  setScreen: (screen: MentionScreen) => void;
  onFileClick: () => void;
};

export default function NavigationView({
  items,
  itemRefs,
  onSelectSuggestion,
  selectedIndex,
  setSelectedIndex,
  setScreen,
  onFileClick,
  query,
}: NavigationViewProps) {
  if (!items.length) {
    return (
      <div className={'w-full'}>
        <div
          className={
            'text-xs text-disabled-foreground font-bold mt-2 mb-1.5 px-1.5 flex flex-row gap-x-4'
          }
        >
          <span className={'text-nowrap text-ellipsis overflow-x-hidden w-full inline-block'}>
            {!!query.length && <>Search by "{query}"</>}
          </span>
          <span>Esc</span>
        </div>
        <div className={'flex flex-col items-center justify-center p-4 text-disabled-foreground'}>
          <span className={'text-center'}>
            Can’t find items matched
            <br /> your criteria
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={'overflow-hidden w-full'}>
      {!!query.length && (
        <div
          className={
            'text-xs text-disabled-foreground font-bold mt-2 mb-1.5 px-1.5 flex flex-row gap-x-4'
          }
        >
          <span className={'text-nowrap text-ellipsis overflow-x-hidden w-full inline-block'}>
            Search by "{query}"
          </span>
          <span>Esc</span>
        </div>
      )}
      <div className={'overflow-y-auto max-h-[calc(330px_-_2rem)] w-full space-y-1'}>
        <NavigationList
          items={items}
          onSelectSuggestion={onSelectSuggestion}
          selectedIndex={selectedIndex}
          setSelectedIndex={setSelectedIndex}
          itemRefs={itemRefs}
          setScreen={setScreen}
          onFileClick={onFileClick}
          query={query}
        />
      </div>
    </div>
  );
}

type NavigationListProps = {
  items: NavigationListItem[];
} & Pick<
  NavigationViewProps,
  | 'itemRefs'
  | 'selectedIndex'
  | 'setSelectedIndex'
  | 'onSelectSuggestion'
  | 'setScreen'
  | 'onFileClick'
  | 'query'
>;

function NavigationList({
  items,
  itemRefs,
  setScreen,
  selectedIndex,
  setSelectedIndex,
  onSelectSuggestion,
  onFileClick,
  query,
}: NavigationListProps) {
  const { allowImages } = useAttachmentOptions();
  return (
    <>
      {items.map((item, index) => {
        switch (item.type) {
          case 'navigation':
            return (
              <NavigationMenuButton
                key={index}
                item={item}
                itemRefs={itemRefs}
                setSelectedIndex={setSelectedIndex}
                index={index}
                selectedIndex={selectedIndex}
                onSelect={() => {
                  setScreen(item.screen);
                }}
              />
            );
          case 'add_file':
            return (
              <AddFileMenuButton
                key={index}
                label={allowImages ? 'File or image from computer...' : 'Add file from computer...'}
                selectedIndex={selectedIndex}
                setSelectedIndex={setSelectedIndex}
                itemRefs={itemRefs}
                index={index}
                onSelect={onFileClick}
              />
            );
          case 'file':
            return (
              <NavigationFileButton
                key={item.item.id}
                item={item}
                itemRefs={itemRefs}
                setSelectedIndex={setSelectedIndex}
                index={index}
                selectedIndex={selectedIndex}
                onSelect={() => {
                  onSelectSuggestion({
                    type: MentionType.File,
                    item: {
                      type: MentionType.File,
                      id: `file:${item.item.path}`,
                      path: item.item.path,
                      fsPath: item.item.fsPath,
                    },
                    isSuggestionAvailable: true,
                  });
                }}
                query={query}
              />
            );
          default:
            item satisfies never;
            return null;
        }
      })}
    </>
  );
}

type NavigationMenuButtonProps = {
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  item: NavigationMentionListItem;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
};

function NavigationMenuButton({
  item,
  index,
  selectedIndex,
  itemRefs,
  onSelect,
  setSelectedIndex,
}: NavigationMenuButtonProps) {
  return (
    <div
      role={'button'}
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center p-1.5 border-none min-w-[200px]',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
    >
      <div className={'flex flex-row justify-between w-full'}>
        <div className={'flex flex-row gap-1 items-center'}>
          {item.icon}
          <span>{item.text}</span>
        </div>
        <div>
          <ArrowIcon className={'-rotate-90 text-primary-foreground'} />
        </div>
      </div>
    </div>
  );
}

type AddFileMenuButtonProps = {
  label: string;
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
};

function AddFileMenuButton({
  label,
  itemRefs,
  index,
  selectedIndex,
  setSelectedIndex,
  onSelect,
}: AddFileMenuButtonProps) {
  return (
    <div
      role={'button'}
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center p-1.5 border-none min-w-[200px] gap-x-1',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
      onClick={onSelect}
    >
      <FileIcon className={'text-secondary-foreground'} />
      {label}
    </div>
  );
}

type NavigationFileButtonProps = {
  itemRefs: MutableRefObject<(HTMLDivElement | null)[]>;
  item: FileMentionListItem;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
  query: string;
};

function NavigationFileButton({
  itemRefs,
  item,
  index,
  selectedIndex,
  setSelectedIndex,
  onSelect,
  query,
}: NavigationFileButtonProps) {
  return (
    <div
      role={'button'}
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center p-1.5 border-none min-w-[200px]',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
      onClick={() => {}}
    >
      <SearchFileTextView filePath={item.item.path} searchString={query} />
    </div>
  );
}
