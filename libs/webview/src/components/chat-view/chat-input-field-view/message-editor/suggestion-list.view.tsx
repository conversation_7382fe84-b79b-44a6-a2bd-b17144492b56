import { useAppStore } from '@src/store/app.store';
import { Editor } from '@tiptap/react';
import {
  forwardRef,
  MutableRefObject,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { twJoin } from 'tailwind-merge';
import { SlashCommandItem } from './types';

export type SuggestionListProps = {
  items: SlashCommandItem[];
  command: (item: SlashCommandItem) => void;
  editor: Editor;
  enterSubmenu?: (editor: Editor, providerId: string) => void;
  onClose: () => void;
};

const SuggestionList = forwardRef((props: SuggestionListProps, ref) => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const totalItems = props.items.length;

  const itemRefs = useRef<Array<HTMLButtonElement | null>>([]);

  const {
    state: { customAgents },
  } = useAppStore();

  const upHandler = () => {
    setSelectedIndex((prevIndex) => {
      const newIndex = prevIndex - 1 >= 0 ? prevIndex - 1 : 0;
      itemRefs.current[newIndex]?.scrollIntoView({
        behavior: 'instant' as ScrollBehavior,
        block: 'nearest',
      });
      return newIndex;
    });
  };

  const downHandler = () => {
    setSelectedIndex((prevIndex) => {
      const newIndex = prevIndex + 1 < totalItems ? prevIndex + 1 : prevIndex;
      itemRefs.current[newIndex]?.scrollIntoView({
        behavior: 'instant' as ScrollBehavior,
        block: 'nearest',
      });
      return newIndex;
    });
  };

  const enterHandler = () => {
    const item = props.items[selectedIndex];
    if (!item) {
      return;
    }
    props.command(item);
  };

  useEffect(() => setSelectedIndex(0), [props.items]);

  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }: { event: KeyboardEvent }) => {
      if (event.key === 'ArrowUp') {
        upHandler();
        return true;
      }

      if (event.key === 'ArrowDown') {
        downHandler();
        return true;
      }

      if (event.key === 'Enter' || event.key === 'Tab') {
        enterHandler();
        event.stopPropagation();
        event.preventDefault();
        return true;
      }

      if (event.key === 'Escape') {
        event.preventDefault();
        event.stopPropagation();
        return true;
      }

      if (event.key === ' ') {
        if (props.items.length === 1) {
          enterHandler();
          return true;
        }
      }

      return false;
    },
  }));

  useEffect(() => {
    itemRefs.current = itemRefs.current.slice(0, props.items.length);
  }, [props.items]);

  const { customAgentsCommands, commands } = useMemo(() => {
    return props.items.reduce<{
      commands: SlashCommandItem[];
      customAgentsCommands: SlashCommandItem[];
    }>(
      (acc, cur) => {
        if (cur.commandType === 'customAgent') {
          acc.customAgentsCommands.push(cur);
        } else {
          acc.commands.push(cur);
        }

        return acc;
      },
      { commands: [], customAgentsCommands: [] }
    );
  }, [props.items]);

  if (!props.items.length) {
    return null;
  }

  return (
    <div className="flex flex-col rounded-lg overflow-x-hidden overflow-y-auto max-h-[330px] p-1 bg-editor-background">
      {
        <>
          {commands.map((item, index) => (
            <CommandItem
              key={item.commandName}
              title={item.label}
              index={index}
              selectedIndex={selectedIndex}
              setSelectedIndex={setSelectedIndex}
              itemRefs={itemRefs}
              onSelect={() => props.command(item)}
            />
          ))}
          {customAgentsCommands.length > 0 && (
            <div className="text-xs text-disabled-foreground font-bold mt-2 mb-1.5 px-1.5">
              Custom agents
            </div>
          )}
          {customAgentsCommands.map((item, index) => (
            <CommandItem
              key={item.customAgentId}
              title={item.label}
              description={
                customAgents?.find((a) => a.id === item.customAgentId)?.isSharedWithOrganization
                  ? 'Org'
                  : undefined
              }
              index={index + commands.length}
              selectedIndex={selectedIndex}
              setSelectedIndex={setSelectedIndex}
              itemRefs={itemRefs}
              onSelect={() => props.command(item)}
            />
          ))}
        </>
      }
    </div>
  );
});

interface CommandItemProps {
  itemRefs: MutableRefObject<(HTMLButtonElement | null)[]>;
  title: string;
  description?: string;
  index: number;
  selectedIndex: number;
  setSelectedIndex: React.Dispatch<React.SetStateAction<number>>;
  onSelect: () => void;
}

function CommandItem({
  title,
  description,
  index,
  selectedIndex,
  itemRefs,
  onSelect,
  setSelectedIndex,
}: CommandItemProps) {
  return (
    <button
      ref={(el) => (itemRefs.current[index] = el)}
      className={twJoin(
        'rounded-sm flex items-center justify-between gap-2 p-1.5 border-none min-w-[200px] text-start',
        index === selectedIndex ? 'bg-hover-background' : 'bg-transparent'
      )}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
        onSelect();
      }}
      onMouseEnter={() => setSelectedIndex(index)}
    >
      <span className="whitespace-nowrap truncate">/{title}</span>
      <span className="text-secondary-foreground">{description}</span>
    </button>
  );
}

export default SuggestionList;
