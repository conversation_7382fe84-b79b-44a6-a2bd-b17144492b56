import * as Sentry from '@sentry/react';
import { Extension, mergeAttributes, Node } from '@tiptap/core';
import { PluginKey } from '@tiptap/pm/state';
import { Editor, ReactRenderer } from '@tiptap/react';
import Suggestion, {
  findSuggestionMatch as defaultFindSuggestionMatch,
  SuggestionOptions,
} from '@tiptap/suggestion';
import tippy, { Instance } from 'tippy.js';
import SuggestionList, { SuggestionListProps } from './suggestion-list.view';
import { SlashCommandItem } from './types';

export const COMMAND_TRIGGER_SYMBOL = '/';
export const SLASH_COMMAND_EXTENSION_NAME = 'command';
export const getCustomAgentTooltipId = (agentId: string) => {
  return `custom-agent-tooltip-${agentId}`;
};
export const ZENCODER_AGENT_TOOLTIP_ID = 'zencoder-agent-tooltip-';

export type SlashCommandOptions = {
  getSuggestion: (editor: Editor) => Omit<SuggestionOptions, 'editor'>;
  enabled?: boolean;
  items: SlashCommandItem[];
};

export const SlashCommandPluginKey = new PluginKey('command');

export const SlashCommand = Node.create<SlashCommandOptions>({
  name: SLASH_COMMAND_EXTENSION_NAME,
  addOptions() {
    return {
      enabled: true,
      items: [],
      getSuggestion: (editor) => ({
        char: COMMAND_TRIGGER_SYMBOL,
        pluginKey: SlashCommandPluginKey,
        startOfLine: false,
        command: ({ range, props }) => {
          // increase range.to by one when the next node is of type "text"
          // and starts with a space character
          const nodeAfter = editor.view.state.selection.$to.nodeAfter;
          const overrideSpace = nodeAfter?.text?.startsWith(' ');
          if (overrideSpace) {
            range.to += 1;
          }

          editor
            .chain()
            .focus()
            .insertContentAt(range, [
              {
                type: this.name,
                attrs: props,
              },
              {
                type: 'text',
                text: ' ',
              },
            ])
            .run();

          window.getSelection()?.collapseToEnd();
        },
        allow: ({ state, range }) => {
          const $from = state.doc.resolve(range.from);
          const type = state.schema.nodes[this.name];
          if (!type) {
            return false;
          }
          return !!$from.parent.type.contentMatch.matchType(type);
        },
        items: ({ query }: { query: string }): SlashCommandItem[] => {
          const extension = getCommandExtension(editor);

          const items: SlashCommandItem[] = (extension?.options.items ?? []).flatMap((item) =>
            item.commandName.toLowerCase().startsWith(query.toLowerCase())
              ? {
                  ...item,
                }
              : []
          );

          return items;
        },
        allowSpaces: true,
        findSuggestionMatch: (config) => {
          const extension = getCommandExtension(editor);

          if (!extension?.options.enabled) {
            return null;
          }

          const commandsNames = extension.options.items.map((item) => item.commandName);

          if (
            commandsNames.some((name) => editor.getText().startsWith(COMMAND_TRIGGER_SYMBOL + name))
          ) {
            return null;
          }
          if (editor.isEmpty || editor.getText().startsWith(COMMAND_TRIGGER_SYMBOL)) {
            return defaultFindSuggestionMatch(config);
          }
          return null;
        },
        render: () => {
          let component: ReactRenderer<typeof SuggestionList, SuggestionListProps>;
          let popup: Instance[];

          const onExit = () => {
            popup?.[0]?.destroy();
            component?.destroy();
          };

          return {
            onStart: (props) => {
              component = new ReactRenderer<typeof SuggestionList, SuggestionListProps>(
                SuggestionList,
                {
                  props: { ...props, onClose: onExit },
                  editor: props.editor,
                }
              );

              if (!props.clientRect) {
                Sentry.captureException(
                  new Error(`No client rect found for slash command suggestion`)
                );
                return;
              }

              popup = tippy('body', {
                getReferenceClientRect: props.clientRect! as () => DOMRect,
                appendTo: () => document.body,
                content: component.element,
                showOnCreate: true,
                interactive: true,
                trigger: 'manual',
                placement: 'top-start',
                maxWidth: `${window.innerWidth - 24}px`,
              });
            },

            onUpdate(props) {
              component.updateProps({ ...props });
              if (!props.clientRect) {
                return;
              }

              popup[0]?.setProps({
                getReferenceClientRect: props.clientRect! as () => DOMRect,
              });
            },
            onKeyDown(props) {
              if (props.event.key === 'Escape') {
                popup[0]?.hide();

                return true;
              }
              if (!component.ref) {
                return false;
              }
              return (component.ref as unknown as {
                onKeyDown: (props: unknown) => boolean;
              })!.onKeyDown(props);
            },

            onExit,
          };
        },
      }),
    };
  },

  group: 'inline',

  inline: true,

  selectable: false,

  atom: true,

  addAttributes() {
    return {
      label: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-label'),
        renderHTML: (attributes) => {
          if (!attributes.label) {
            return {};
          }

          return {
            'data-label': attributes.label,
          };
        },
      },
      customAgentId: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-custom-agent-id'),
        renderHTML: (attributes) => {
          if (!attributes.customAgentId) {
            return {};
          }

          return {
            'data-custom-agent-id': attributes.customAgentId,
            'data-tooltip-id': getCustomAgentTooltipId(attributes.customAgentId),
          };
        },
      },
      isZencoderAgent: {
        default: false,
        parseHTML: (element) =>
          element.getAttribute('data-tooltip-id') === ZENCODER_AGENT_TOOLTIP_ID,
        renderHTML: (attributes) => {
          if (!attributes.isZencoderAgent) {
            return {};
          }

          return {
            'data-tooltip-id': ZENCODER_AGENT_TOOLTIP_ID,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: `span[data-type="${this.name}"]`,
      },
    ];
  },

  renderHTML({ node, HTMLAttributes }) {
    return [
      'span',
      mergeAttributes({ 'data-type': this.name, class: 'text-link-foreground' }, HTMLAttributes),
      COMMAND_TRIGGER_SYMBOL + node.attrs.label,
    ];
  },

  renderText({ node }) {
    return COMMAND_TRIGGER_SYMBOL + node.attrs.label;
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () =>
        this.editor.commands.command(({ tr, state }) => {
          let isSlashCommand = false;
          const { selection } = state;
          const { empty, anchor } = selection;

          if (!empty) {
            return false;
          }

          state.doc.nodesBetween(anchor - 1, anchor, (node, pos) => {
            if (node.type.name === this.name) {
              isSlashCommand = true;
              tr.insertText(COMMAND_TRIGGER_SYMBOL, pos, pos + node.nodeSize);

              return false;
            }
          });

          return isSlashCommand;
        }),
    };
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        ...this.options.getSuggestion(this.editor),
      }),
    ];
  },
});

function changeNodeType(
  editor: Editor,
  from: number,
  to: number,
  newTypeName: string,
  newAttributes: {
    text?: string;
    label?: string;
    customAgentId?: string;
    isZencoderAgent?: boolean;
  } = {}
) {
  const { state, view } = editor;
  const { schema, doc } = state;
  const transaction = state.tr;
  const node = doc.nodeAt(from);

  if (!node) {
    Sentry.captureException(new Error('No node found at the given position'));
    return;
  }

  const newNodeType = schema.nodes[newTypeName];

  if (!newNodeType) {
    Sentry.captureException(new Error(`Node type "${newTypeName}" not found in schema`));
    return;
  }

  const newNode =
    newTypeName === 'text'
      ? schema.text(newAttributes.text!)
      : newNodeType.create({ ...node.attrs, ...newAttributes }, node.content);

  transaction.replaceWith(from, to, newNode);
  view.dispatch(transaction);
}

export function updateSlashCommands({
  editor,
  slashCommandsEnabled,
  items,
}: {
  editor: Editor | null;
  slashCommandsEnabled?: boolean;
  items: SlashCommandItem[];
}) {
  if (!editor) return;
  editor.$doc.content.descendants((node, pos) => {
    node.content.descendants((node1, pos1) => {
      if (!slashCommandsEnabled && node1.type.name === 'command') {
        changeNodeType(editor, pos1, pos1 + 2, 'text', {
          text: COMMAND_TRIGGER_SYMBOL + node1.attrs.label,
        });
      } else if (
        slashCommandsEnabled &&
        (pos !== 0 || pos1 !== 0) &&
        node1.type.name === 'command'
      ) {
        changeNodeType(editor, pos1 + 1, pos1 + 3, 'text', {
          text: `${COMMAND_TRIGGER_SYMBOL}${node1.attrs.label} `,
        });
      } else if (slashCommandsEnabled) {
        const firstNode = editor.$doc.firstChild?.content.firstChild;
        if (firstNode?.type.name === 'text') {
          const firstText = firstNode.textContent;

          const foundCommand = findCommand(firstText, items);

          if (foundCommand) {
            changeNodeType(editor, pos, pos + foundCommand.label.length + 2, 'command', {
              label: foundCommand.label,
              customAgentId:
                foundCommand.commandType === 'customAgent' ? foundCommand.customAgentId : undefined,
              isZencoderAgent: foundCommand.isZencoderAgent,
            });
          }
        }

        if (firstNode?.type.name === 'command') {
          const firstText = firstNode.textContent;

          const foundCommand = findCommand(firstText, items);

          if (!foundCommand) {
            changeNodeType(editor, pos1, pos1 + 2, 'text', {
              text: COMMAND_TRIGGER_SYMBOL + node1.attrs.label,
            });
          }
        }
      }
    });
  });
}

function findCommand(text: string, items: SlashCommandItem[]) {
  const foundItems = items.filter((command) => text?.startsWith(`/${command.label}`));

  // find the longest matching command
  const foundCommand = foundItems.reduce<SlashCommandItem | null>((acc, cur) => {
    if ((cur.commandName?.length ?? 0) > (acc?.commandName?.length ?? 0)) {
      return {
        ...cur,
      };
    }

    return acc;
  }, null);

  return foundCommand;
}

function getCommandExtension(editor: Editor) {
  return editor.extensionManager.extensions.find(
    (e): e is Extension<SlashCommandOptions> => e.name === 'command'
  );
}
