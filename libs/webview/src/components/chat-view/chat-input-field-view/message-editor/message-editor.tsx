import { useToggleFeatureFlag } from '@src/hooks/feature-flags/use-toggle-feature-flag';
import { Agent } from '@src/models/custom-agents.model';
import { useAppStore } from '@src/store/app.store';
import { Extension, FocusPosition, JSONContent } from '@tiptap/core';
import { CharacterCount } from '@tiptap/extension-character-count';
import { Document } from '@tiptap/extension-document';
import { History } from '@tiptap/extension-history';
import { Paragraph } from '@tiptap/extension-paragraph';
import { Text } from '@tiptap/extension-text';
import { Node } from '@tiptap/pm/model';
import { Editor, EditorContent, useEditor } from '@tiptap/react';
import {
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
} from 'react';
import { twMerge } from 'tailwind-merge';
import { isMacOs, isWindows } from '../helpers/env.helpers';
import { CodeSnippetArrowNavigation } from './code-snippet-arrow-navigation.extension';
import { CodeSnippetExtension } from './code-snippet.extension';
import { CustomPlaceholder } from './custom-placeholder.extension';
import { getLowlight } from './get-lowlight';
import {
  KEYBOARD_SHORTCUTS_EXTENSION_NAME,
  KeyboardShortcutsExtension,
} from './keyboard-shortcuts.extension';
import { MentionExtension } from './mention/at-command.extension';
import './message-editor.css';
import { ShortcutsNavigation } from './shortcuts-navigation';
import {
  SLASH_COMMAND_EXTENSION_NAME,
  SlashCommand,
  SlashCommandOptions,
  updateSlashCommands,
} from './slash-command.extension';
import { KeyboardEnterOptions, MessageEditorRef, SlashCommandItem } from './types';

interface Props {
  initialContent?: string | JSONContent;
  slashCommandsAvailable?: boolean;
  onChange: () => void;
  onCommit: (autoApply?: boolean) => void;
  onToggleAgent?: () => void;
  placeholder: string | React.ReactNode;
  disabled?: boolean;
  className?: string;
  isFirstMessage?: boolean;
}

/**
 * Tiptap editor conveniently sets it's main class instance
 * on the contenteditable DOM node allowing extenal event handlers
 * to get access to the editor.
 */
type TipTapEditorMixin = {
  editor?: Editor;
};

/**
 * Intercepts all the keyboard undo related keystrokes
 * and translates them to the tiptap editor native undo() / redo()
 * commands if avalable. It appearch that only the undo
 * part is racy in the combination of Electron + webview + tiptap.
 */
function handleUndoRedo(event: KeyboardEvent) {
  if (event.code !== 'KeyZ') {
    return;
  }

  let isUndo =
    // macOS cmd+z
    (isMacOs() && event.metaKey && !event.shiftKey && !event.ctrlKey && !event.altKey) ||
    // Windows ctrl+z
    (isWindows() && event.ctrlKey && !event.metaKey && !event.shiftKey && !event.altKey);

  if (!isUndo) {
    return;
  }

  const target = event.target as EventTarget & TipTapEditorMixin;
  if (!target) {
    return;
  }
  const editor = target.editor;
  if (!editor) {
    return;
  }

  // Hide this event from all the other events handlers.
  // This allows us to take over the control and avoid the
  // potentially racy undo handling.
  // A note for future engineer debugging this issue.
  // To get a feel on how much happens in VS Code and tiptap
  // on a key event uncomment the debugger line below and run
  // the webview with dev tools open:
  // debugger
  event.preventDefault();
  event.stopPropagation();
  editor.chain().focus()?.undo().run();
  // Fun fact. Instead of the above block you can add a console.log()
  // that also fixes the race condition reliably.
}

export const MessageEditor = forwardRef(
  (
    {
      initialContent,
      slashCommandsAvailable,
      onChange,
      onCommit,
      onToggleAgent,
      placeholder,
      disabled,
      className,
      isFirstMessage,
    }: Props,
    ref: ForwardedRef<MessageEditorRef | undefined>
  ) => {
    const { state } = useAppStore();
    const { customAgents } = state;

    const enableCoffeeMode = useToggleFeatureFlag('enableCoffeeMode', 'on');

    const enableE2EAgent = useToggleFeatureFlag('enable-e2e-agent', 'on');

    const slashCommandItems = useMemo(
      () =>
        getSlashCommandItems(isFirstMessage ?? false, customAgents ?? [], enableE2EAgent === 'on'),
      [customAgents, isFirstMessage, enableE2EAgent]
    );

    const editor = useEditor(
      {
        extensions: [
          CharacterCount.configure({
            limit: 10000,
          }),
          CodeSnippetExtension.configure({
            lowlight: getLowlight(),
          }),
          Document,
          History,
          KeyboardShortcutsExtension.configure({
            ctrlToSubmit: false,
            ctrlToSubmitCodeBlock: true,
            onSubmit: onCommit,
            onToggleAgent,
            enableCoffeeMode: enableCoffeeMode === 'on',
          }),
          SlashCommand.configure({
            enabled: slashCommandsAvailable,
            items: slashCommandItems,
          }),
          MentionExtension,
          Paragraph,
          CustomPlaceholder.configure({
            placeholder,
            emptyEditorClass: 'is-editor-empty',
            emptyNodeClass: 'is-empty',
          }),
          Text,
          ShortcutsNavigation,
          CodeSnippetArrowNavigation,
        ],
        editorProps: {
          attributes: {
            class: twMerge('outline-none p-2 pb-1 max-h-[50vh] overflow-auto border-b', className),
          },
        },
        editable: !disabled,
        content: initialContent,
        onUpdate: () => {
          onChange?.();
        },
      },
      []
    );

    const getUpdateCommandsPayload = useCallback(() => {
      return {
        editor,
        slashCommandsEnabled: slashCommandsAvailable,
        items: slashCommandItems,
      };
    }, [editor, slashCommandItems, slashCommandsAvailable]);

    useEffect(() => {
      if (editor) {
        editor.setOptions({
          editable: !disabled,
          onUpdate: () => {
            onChange?.();
          },
        });

        const keyboardEnterExt = editor.extensionManager.extensions.find(
          (e): e is Extension<KeyboardEnterOptions> => e.name === KEYBOARD_SHORTCUTS_EXTENSION_NAME
        );

        const commandsExt = editor.extensionManager.extensions.find(
          (e): e is Extension<SlashCommandOptions> => e.name === SLASH_COMMAND_EXTENSION_NAME
        );

        if (keyboardEnterExt) {
          keyboardEnterExt.options.onSubmit = onCommit;
          keyboardEnterExt.options.onToggleAgent = onToggleAgent;
        }
        if (commandsExt) {
          const updateCommandPayload = getUpdateCommandsPayload();

          commandsExt.options.enabled = updateCommandPayload.slashCommandsEnabled;
          commandsExt.options.items = updateCommandPayload.items;

          updateSlashCommands(updateCommandPayload);
        }
      }
    }, [disabled, editor, onChange, onCommit, onToggleAgent, getUpdateCommandsPayload]);

    useImperativeHandle(ref, () => (editor ? getMessageEditorRef(editor) : undefined), [editor]);

    useEffect(() => {
      editor?.setOptions({
        editable: !disabled,
      });
    }, [disabled, editor]);

    useEffect(() => {
      if (!editor) return;
      // useEffect() is called two times in dev React Strict mode.
      // We need to remove the listener first before adding it again.
      // This trick also guarantees that our listener stays at the top of the
      // listeners chain and can intecept event before other listeners.
      editor.view.dom.removeEventListener('keydown', handleUndoRedo, true);
      editor.view.dom.addEventListener('keydown', handleUndoRedo, true);

      return () => {
        // Also remove the listener on cleanup.
        editor.view.dom.removeEventListener('keydown', handleUndoRedo, true);
      };
    }, [editor]);

    return <EditorContent editor={editor} aria-label="Chat input field" />;
  }
);

function getMessageEditorRef(editor: Editor): MessageEditorRef {
  return {
    isEmpty() {
      return editor.isEmpty || !editor.getText().trim();
    },
    clearContent() {
      return editor.commands.clearContent(true);
    },
    getValue() {
      return editor.getJSON();
    },
    insertCodeSnippet(code: string, language?: string) {
      const content: JSONContent = {
        type: 'codeBlock',
        attrs: {
          language,
        },
        content: [
          {
            type: 'text',
            text: code,
          },
        ],
      };

      const isCodeBlockActive = editor.isActive('codeBlock');

      if (isCodeBlockActive) {
        editor.commands.selectParentNode();
        const endPos = editor.state.selection.to;
        editor.commands.focus('start');
        editor.commands.insertContentAt(endPos, content);
        editor.commands.selectParentNode();
        const newPos = editor.state.selection.to;
        return editor.commands.focus(newPos);
      } else {
        return editor
          .chain()
          .insertContent(content)
          .focus()
          .insertContent({ type: 'paragraph' })
          .run();
      }
    },
    focus(position?: FocusPosition) {
      editor.commands.focus(position);
    },
    replace(filter, value) {
      // Array to collect mention nodes
      const mentionNodes: { node: Node; pos: number }[] = [];

      // Find all mention nodes in the document
      editor.state.doc.descendants((node, pos) => {
        if (node.type.name === 'mention') {
          mentionNodes.push({ node, pos });
        }
        return true;
      });

      // Filter the mention nodes based on the provided filter function
      const mentionsToReplace = mentionNodes.filter(({ node }) => filter(node));

      // Replace each filtered mention with plain text
      // Process in reverse order to avoid position shifts
      for (let i = mentionsToReplace.length - 1; i >= 0; i--) {
        const { pos, node } = mentionsToReplace[i]!;
        const from = pos;
        const to = pos + node.nodeSize;
        // Replace the mention node with plain text
        if (typeof value === 'function') {
          const newValue = value(node);
          if (newValue) {
            editor.commands.insertContentAt({ from, to }, newValue);
          }
        } else {
          editor.commands.insertContentAt(
            { from, to },
            value || node.attrs?.label || node.attrs?.id || ''
          );
        }
      }
    },
  };
}

export const DEFAULT_SLASH_COMMAND_NAMES: Readonly<string[]> = [
  'code',
  'explain',
  'fix',
  'unittests',
  'e2e-test',
];

function getSlashCommandItems(
  isFirstMessage: boolean,
  customAgents: Agent[],
  isE2Enabled: boolean
): SlashCommandItem[] {
  const basicCommands: SlashCommandItem[] = [
    {
      label: 'explain',
      commandName: 'explain',
    },
    {
      label: 'fix',
      commandName: 'fix',
    },
  ];

  const agentCommands: SlashCommandItem[] = [
    {
      label: 'code',
      commandName: 'code',
      isZencoderAgent: true,
    },
    {
      label: 'unittests',
      commandName: 'unittests',
      isZencoderAgent: true,
    },
    ...(isE2Enabled
      ? [
          {
            label: 'e2e-test',
            commandName: 'e2e-test',
            isZencoderAgent: true,
          },
        ]
      : []),
  ];

  const customAgentsCommands = isFirstMessage
    ? customAgents
        .filter((agent): agent is Agent & { commandName: string } => !!agent.commandName)
        .map((agent) => ({
          commandName: agent.commandName,
          label: agent.commandName,
          commandType: 'customAgent' as const,
          customAgentId: agent.id,
        }))
    : [];

  const slashCommandItems: SlashCommandItem[] = isFirstMessage
    ? [...agentCommands, ...basicCommands, ...customAgentsCommands]
    : basicCommands;

  return slashCommandItems;
}
