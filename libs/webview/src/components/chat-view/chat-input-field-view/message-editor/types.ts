import { FocusPosition, JSONContent } from '@tiptap/core';
import { Node } from '@tiptap/pm/model';

export interface KeyboardEnterOptions {
  onSubmit: (autoApply?: boolean) => void;
  onToggleAgent?: () => void;
  ctrlToSubmit: boolean;
  ctrlToSubmitCodeBlock: boolean;
  enableCoffeeMode: boolean;
}

export interface MessageEditorRef {
  clearContent: () => boolean;
  isEmpty: () => boolean;
  getValue: () => JSONContent;
  insertCodeSnippet: (code: string, language?: string) => boolean;
  focus: (position?: FocusPosition) => void;
  replace: (
    filter: (node: Node) => boolean,
    value: JSONContent | ((node: Node) => JSONContent)
  ) => void;
}

export interface SlashCommandItem {
  label: string;
  commandName: string;
  commandType?: 'customAgent';
  customAgentId?: string;
  isZencoderAgent?: boolean;
}
