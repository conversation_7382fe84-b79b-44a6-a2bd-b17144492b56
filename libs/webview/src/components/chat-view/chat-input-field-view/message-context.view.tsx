import { AnchorProps } from '@headlessui/react/dist/internal/floating';
import { menuIconClassName } from '@src/components/chat-view/chat-input-field-view/add-context-panel.view';
import { LinkButton } from '@src/components/ui-kit/button';
import { Input } from '@src/components/ui-kit/form-elements';
import { Icon } from '@src/components/ui-kit/icon';
import { LanguageIcon } from '@src/components/ui-kit/language-icon';
import { MenuItem, MenuItemButton, MenuItems } from '@src/components/ui-kit/menu';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useAttachmentOptions } from '@src/hooks/use-attachment-options';
import ArrowUpRightIcon from '@src/icons/arrow-up-right.svg';
import CheckIcon from '@src/icons/check.svg';
import CloseIcon from '@src/icons/close-icon.svg';
import FileIcon from '@src/icons/file.svg';
import { ChatMessageContext } from '@src/models/chat.model';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { getFileName } from '@src/utils/get-file-name';
import { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { twJoin } from 'tailwind-merge';
import SearchFile from './search-file.view';
import { FilesSearch, useFilesSearch } from './use-files-search';

type Props = {
  position: AnchorProps;
  isRagEnabled: boolean;
  isCurrentFileEnabled: boolean;
  attachedFiles: ChatMessageContext['attachedFiles'];
  onChangeAttachedFiles: (files: ChatMessageContext['attachedFiles']) => void;
  onFileClick: (allowImages: boolean) => void;
  onToggleRag: () => void;
  onCurrentFileToggle: () => void;
};

export default function MessageContextView({ position, ...props }: Readonly<Props>) {
  const { state } = useAppStore();
  const { repoIndexData } = state;
  const filesSearch = useFilesSearch(!!repoIndexData?.isSearchEnabled);
  return (
    <MenuItems unmount anchor={position} className={'pb-1 w-96 min-h-[242px]'}>
      <MenuItemsContent filesSearch={filesSearch} {...props} />
    </MenuItems>
  );
}

interface MenuItemsContentProps extends Omit<Props, 'position'> {
  filesSearch: FilesSearch;
}

function MenuItemsContent({
  filesSearch,
  onFileClick,
  isRagEnabled,
  isCurrentFileEnabled,
  onToggleRag,
  onCurrentFileToggle,
  attachedFiles,
  onChangeAttachedFiles,
}: MenuItemsContentProps) {
  const navigate = useNavigate();
  const { state } = useAppStore();
  const { repoIndexData, activeFile } = state;
  const isCodebaseKnowledgeDisabled =
    !repoIndexData?.isIndexingEnabled || !repoIndexData?.isIndexingAvailable;
  const activeFileName = activeFile?.fsPath && getFileName(activeFile.fsPath);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { allowImages } = useAttachmentOptions();

  useEffect(() => {
    filesSearch.requestSuggestedFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const openRagSettings = () => {
    navigate(`/${Routes.repoIndexSettings}`);
  };

  const ragSettingsLabel = !repoIndexData?.isIndexingEnabled
    ? 'Enable in settings to get better answers'
    : !repoIndexData?.isIndexingAvailable
      ? 'Enabled git is required to use it'
      : '';
  const analytics = useAnalytics();
  const filesSearchAvailableSent = useRef(false);

  useEffect(() => {
    if (repoIndexData && !repoIndexData.isSearchEnabled && !filesSearchAvailableSent.current) {
      analytics.track('Files search not available');
      filesSearchAvailableSent.current = true;
    }
  }, [analytics, repoIndexData]);

  return (
    <>
      {!!repoIndexData?.isSearchEnabled && (
        <div className="relative">
          <Input
            ref={searchInputRef}
            autoFocus={true}
            className="mb-2 w-full h-8 rounded pe-8 ps-2"
            value={filesSearch.query}
            onKeyDown={(e) => {
              if (e.key === ' ') {
                e.stopPropagation();
              }
            }}
            onChange={(e) => {
              filesSearch.onChangeQuery(e.target.value);
            }}
            placeholder="Search file by name..."
          />
          {!!filesSearch.query && (
            <LinkButton
              size={'sm'}
              className="absolute z-10 right-2 top-1/2 -translate-y-1/2"
              onClick={() => {
                filesSearch.onChangeQuery('');
              }}
            >
              <CloseIcon className="block w-4 h-4" />
            </LinkButton>
          )}
        </div>
      )}
      {!!repoIndexData?.isSearchEnabled && filesSearch.result?.files.length === 0 && (
        <p className="text-sm text-secondary-foreground px-2 py-1">No files found</p>
      )}
      {/* 7 max, for better ui */}
      {!!repoIndexData?.isSearchEnabled &&
        filesSearch.result?.files
          .slice(0, 7)
          .map((f) => (
            <SearchFile
              key={f.fsPath}
              file={f}
              searchString={filesSearch.result?.query}
              attachedFiles={attachedFiles}
              onChangeAttachedFiles={onChangeAttachedFiles}
            />
          ))}
      {!filesSearch.result && (
        <>
          <MenuItem>
            <MenuItemButton
              className={'group'}
              disabledStyle={isCodebaseKnowledgeDisabled}
              onClick={(e) => {
                e.preventDefault();
                if (!isCodebaseKnowledgeDisabled) {
                  onToggleRag();
                }
              }}
              aria-label="Codebase knowledge context switcher"
            >
              <div className="flex items-center text-sm">
                <Icon type={'book'} className={menuIconClassName} size={16} fontSize={16} />
                <div>
                  Codebase
                  {!!ragSettingsLabel && (
                    <span
                      role="button"
                      className="block text-xs cursor-pointer truncate underline"
                      onClick={openRagSettings}
                    >
                      {ragSettingsLabel}
                      <ArrowUpRightIcon className="w-4 h-4" />
                    </span>
                  )}
                </div>
                <div className="flex-1" />
                <div className="w-4 ms-4">
                  {isRagEnabled && <CheckIcon className="h-4 w-4 block" />}
                </div>
              </div>
            </MenuItemButton>
          </MenuItem>
          <MenuItem>
            <MenuItemButton
              className="group flex justify-between items-center text-sm"
              onClick={(e) => {
                e.preventDefault();
                onCurrentFileToggle();
              }}
              aria-label="Current file context switcher"
            >
              <div className="flex items-center">
                {activeFileName ? (
                  <LanguageIcon
                    fileName={activeFileName}
                    className={twJoin(menuIconClassName, 'w-4 h-4 block')}
                  />
                ) : (
                  <FileIcon className={twJoin(menuIconClassName, 'w-4 h-4 block')} />
                )}
                <span className="text-sm text-nowrap">Current file</span>
              </div>
              <div className="flex items-center flex-1 ps-2 overflow-hidden justify-end gap-x-0.5">
                {!!activeFileName && (
                  <span className="text-xs text-secondary-foreground truncate max-w-40">
                    {activeFileName}
                  </span>
                )}
                <div className="w-4">
                  {isCurrentFileEnabled && <CheckIcon className="h-4 w-4 block" />}
                </div>
              </div>
            </MenuItemButton>
          </MenuItem>
        </>
      )}
      <MenuItem>
        <MenuItemButton className={'group'} onClick={() => onFileClick(allowImages)}>
          <div className="flex items-center text-sm">
            <Icon type={'file'} className={menuIconClassName} size={16} fontSize={16} />
            {allowImages ? 'File or image from computer...' : 'Add from computer...'}
          </div>
        </MenuItemButton>
      </MenuItem>
      {!filesSearch.result &&
        !!filesSearch.suggestedFiles?.length &&
        !!repoIndexData?.isSearchEnabled && (
          <>
            <p className="text-xs font-semibold text-secondary-foreground px-2 py-1.5">
              Suggested files
            </p>
            {/* 4 max, for better ui */}
            {filesSearch.suggestedFiles.slice(0, 4).map((f) => (
              <SearchFile
                key={f.fsPath}
                file={f}
                attachedFiles={attachedFiles}
                onChangeAttachedFiles={onChangeAttachedFiles}
              />
            ))}
          </>
        )}
    </>
  );
}
