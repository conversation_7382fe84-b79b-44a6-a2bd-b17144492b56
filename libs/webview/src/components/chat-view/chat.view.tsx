import { PlaceholderElement } from '@src/components/chat-view/chat-input-field-view/message-editor/placeholder-element.view';
import ChatMessageUserEditView from '@src/components/chat-view/chat-message-view/chat-message-user-edit.view';
import ResponseFeedbackView from '@src/components/chat-view/chat-message-view/response-feedback.view';
import { ChatWarningView } from '@src/components/chat-view/chat-warning.view';
import { RepoIndexChatStatusView } from '@src/components/chat-view/repo-index-chat-status.view';
import { useChatFeedback } from '@src/hooks/use-chat-feedback';
import { useDebounce } from '@src/hooks/use-debounce';
import { ideApi } from '@src/index';
import {
  ChatId,
  ChatMessage,
  ChatModel,
  ChatSettings,
  isAgenticChat,
} from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { AttachmentOptionsProvider } from '@src/store/attachment-options.context';
import {
  getIsCurrentFileUsageEnabled,
  getIsRagEnabled,
  trimMessage,
} from '@src/utils/chat.helpers';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import Logo from '../../icons/logo-icon.svg';
import { BaseLoaderView } from '../loaders/base-loader.view';
import { ChatHeader } from './chat-header.view';
import { ChatInputField, UserMessage } from './chat-input-field-view/chat-input-field.view';
import { ChatMessageAssistantView } from './chat-message-view/chat-message-assistant.view';
import { ChatMessageUserView } from './chat-message-view/chat-message-user.view';
import { checkIfShouldShowAuthor } from './check-if-should-show-author';
import { ChatAction } from './types';
import { useTrackResponseStarted } from './use-track-response-started';

interface RoutingParams extends Record<string, string | undefined> {
  chatId: ChatId;
}

function maybeResendToAgent(
  isStreaming: boolean | undefined,
  chat: ChatModel,
  messages: ChatMessage[]
): boolean {
  // already is an agent chat
  if (isAgenticChat(chat)) {
    return false;
  }

  if (isStreaming && messages.length <= 2) {
    return false;
  }

  const firstAssistantMessage = messages.find((m) => m.role === 'assistant');
  if (!firstAssistantMessage) {
    return false;
  }

  return firstAssistantMessage.content.some((c) => {
    if (c.type !== 'text') {
      return false;
    }

    // needs a coding snippet
    if (!/```/.test(c.text)) {
      return false;
    }

    const looksLikeFile = /\w+\.\w+/;
    return looksLikeFile.test(c.text);
  });
}

function resendWithAgentEnabled(original: ChatMessage) {
  const message: UserMessage = {
    content: original.content,
    context: original.context,
    rawContent: original.rawContent,
    chatSettings: {
      isAgent: true,
      isCurrentFileUsageEnabled: !!original.context?.currentFile,
      isRagSearchEnabled: !!original.context?.codebaseEnabled,
      isUnitTestsAgent: false,
      isCustomAgent: false,
    },
  };

  ideApi.postMessage({
    type: 'startNewChat',
    message,
  });
}

export function ChatView() {
  const [autoScrollToBottom, setAutoScrollToBottom] = useState(true);
  const { chatId } = useParams<RoutingParams>();
  const { state } = useAppStore();
  const { activeChat, repoIndexData } = state;
  const [activeRepoIndexData, setActiveRepoIndexData] = useState(repoIndexData);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [currentChat, setCurrentChat] = useState(
    activeChat && activeChat.id === chatId ? activeChat : null
  );
  const [showFeedback, setShowFeedback] = useChatFeedback();

  const { restart: restartResponseTracker } = useTrackResponseStarted(currentChat);

  useEffect(() => {
    ideApi.postMessage({
      type: 'requireRepoIndexData',
    });
    ideApi.postMessage({
      type: 'getCustomAgents',
    });
  }, []);

  useEffect(() => {
    if (chatId && !currentChat) {
      ideApi.postMessage({
        type: 'chatIsRequired',
        chatId,
      });
    }
  }, [currentChat, chatId]);

  const handleSendMessage = useCallback(
    (message: UserMessage) => {
      setAutoScrollToBottom(true); // Force scroll to bottom after sending a message
      ideApi.postMessage({
        type: 'postChatMessage',
        message: trimMessage(message),
        chatId: currentChat?.id,
      });
      // Let's use not latest `repoIndexData`, but the one at the moment of message creation
      setActiveRepoIndexData(repoIndexData);
      setEditingMessageId(undefined);
    },
    [currentChat?.id, repoIndexData]
  );

  const handleStopStreaming = useCallback(() => {
    ideApi.postMessage({
      type: 'stopChatStreaming',
      chatId: currentChat?.id,
    });
  }, [currentChat?.id]);

  const scrollToBottom = useCallback(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, []);
  // [YY] to overcome a delayed rendering of markdown
  const scrollToBottomDelayed = useDebounce(scrollToBottom, 1);

  useEffect(() => {
    if (currentChat && autoScrollToBottom) {
      scrollToBottomDelayed();
    }
  }, [currentChat, autoScrollToBottom, scrollToBottomDelayed]);

  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const isAtBottom = scrollHeight - scrollTop <= clientHeight + 5;
      setAutoScrollToBottom(isAtBottom);
    }
  };

  useEffect(() => {
    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  const onExternalAction = (action: ChatAction) => {
    if (action.type === 'regenerateLastAnswer') {
      restartResponseTracker();
    }

    ideApi.postMessage({
      type: 'executeChatAction',
      action: action,
      chatId: currentChat?.id,
    });
  };

  const [editingMessageId, setEditingMessageId] = useState<string>();
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    isCurrentFileUsageEnabled: currentChat
      ? getIsCurrentFileUsageEnabled(currentChat.messages)
      : true,
    isRagSearchEnabled:
      repoIndexData?.isIndexingEnabled && currentChat
        ? getIsRagEnabled(currentChat.messages)
        : true,
    isAgent: currentChat?.isAgent ?? false,
  });

  useEffect(() => {
    if (activeChat) {
      setCurrentChat(activeChat.id === chatId ? activeChat : null);
      setChatSettings((prevState) => ({
        ...prevState,
        isCurrentFileUsageEnabled: getIsCurrentFileUsageEnabled(activeChat.messages),
        isRagSearchEnabled:
          repoIndexData?.isIndexingEnabled && getIsRagEnabled(activeChat.messages),
        isAgent: !!activeChat.isAgent,
      }));
    }
  }, [activeChat, chatId, repoIndexData?.isIndexingEnabled]);

  if (!currentChat) {
    return (
      <BaseLoaderView>
        <div>Chat info is loading</div>
      </BaseLoaderView>
    );
  }

  const { hasClientOperations, isStreaming, messages } = currentChat;
  const storageKey = `chat_draft_${chatId}`;

  let messageElements = messages.map((message, index, arr) => {
    if (message.isHidden) {
      return null;
    }
    switch (message.role) {
      case 'user':
        if (message.id === editingMessageId && chatId) {
          return (
            <AttachmentOptionsProvider allowImages={isAgenticChat(chatSettings)}>
              <ChatMessageUserEditView
                key={message.id}
                initialMessage={message}
                chatId={chatId}
                isFirstMessage={index === 0}
                onSave={() => {
                  setAutoScrollToBottom(true);
                  setEditingMessageId(undefined);
                }}
                onCancel={() => {
                  setEditingMessageId(undefined);
                }}
              />
            </AttachmentOptionsProvider>
          );
        } else {
          return (
            <ChatMessageUserView
              key={index}
              chatId={currentChat.id}
              message={message}
              className={message.isGhost ? 'opacity-50' : undefined}
              onClick={() => {
                if (message.isGhost) {
                  return;
                }
                setEditingMessageId(message.id);
              }}
            />
          );
        }
      case 'assistant':
      case 'system':
        return (
          <ChatMessageAssistantView
            key={index}
            message={message}
            onExternalAction={onExternalAction}
            isLastMessage={index === arr.length - 1}
            className={message.isGhost ? 'opacity-50' : undefined}
            shouldShowAuthor={checkIfShouldShowAuthor(arr, index)}
          />
        );
      default:
        message.role satisfies never;
        return null;
    }
  });

  // to avoid messing with the messages models, injecting the markup
  // right into the element stream
  const resendMessage = maybeResendToAgent(isStreaming, currentChat, messages) && messages[0];
  if (resendMessage && messageElements[0] && messageElements[1]) {
    const resendElement = (
      <div>
        <ResendAgenticMessage message={resendMessage} />
      </div>
    );
    messageElements = [
      messageElements[0],
      messageElements[1],
      resendElement,
      ...messageElements.slice(2),
    ];
  }

  return (
    <div className="flex h-full flex-col overflow-hidden" role="application">
      <ChatHeader showNewChatButton />
      <div
        ref={chatContainerRef}
        className="flex flex-1 flex-col overflow-y-auto overflow-x-hidden pt-2"
        aria-label="Chat messages view"
        onScroll={handleScroll}
      >
        {messageElements}
        {activeRepoIndexData && (
          <RepoIndexChatStatusView chatModel={currentChat} repoIndexData={activeRepoIndexData} />
        )}
        {showFeedback && activeChat && (
          <ResponseFeedbackView
            chat={activeChat}
            chatContainerRef={chatContainerRef}
            setShowFeedback={setShowFeedback}
          />
        )}
      </div>
      <div className={'p-1 pt-0 sticky bottom-0'}>
        {currentChat && <ChatWarningView chat={currentChat} onExternalAction={onExternalAction} />}
        <ChatInputField
          className={'bg-transparent'}
          onCommit={handleSendMessage}
          onStop={handleStopStreaming}
          onSetChatSettings={setChatSettings}
          placeholder={<PlaceholderElement />}
          chatSettings={chatSettings}
          storageKey={storageKey}
          autoFocus={true}
          isChatInProgress={(isStreaming || hasClientOperations) ?? false}
          buttonCaption="Send"
          agentSwitchTooltip="Uses advanced AI workflows to perform common development tasks, such as bug fixing, refactoring, or new feature development. This setting can only be set when creating a chat."
        />
      </div>
    </div>
  );
}

type ResendAgenticMessageProps = {
  message: ChatMessage;
};

function ResendAgenticMessage({ message }: ResendAgenticMessageProps) {
  return (
    <div>
      <div className="peer px-5 py-2 has-[+.peer]:pb-0 [&+.peer]:pt-0">
        <div className="flex items-center pt-2 font-semibold leading-4">
          <Logo className="text-primary-background me-1.5 h-5 w-5" />
          {message.context?.author || 'Zencoder'}
        </div>
      </div>
      <div className="peer px-5 py-2 has-[+.peer]:pb-0 [&+.peer]:pt-0">
        <h2 className="py-2 mt-0 mb-0 text-lg font-medium">
          Trying to modify code, fix bugs or add features?
        </h2>
        <p>
          The Coding Agent is what you need! Enable it by turning on the toggle and let it handle
          the hard work for you.
        </p>
        <button
          className="mt-3 mb-2 mr-2 text-sm font-semibold leading-4 py-1 px-3 rounded select-none data-[disabled]:opacity-100 focus:outline-none focus:ring text-primary-foreground bg-primary-background data-[hover]:bg-primary-hover data-[hover]:data-[active]:bg-primary-active data-[disabled]:bg-transparent data-[disabled]:text-disabled-foreground data-[disabled]:shadow-disabled-box ps-2"
          onClick={() => resendWithAgentEnabled(message)}
        >
          Enable and try Coding Agent
        </button>
      </div>
    </div>
  );
}
