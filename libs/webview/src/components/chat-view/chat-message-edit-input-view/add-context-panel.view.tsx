import { FilesSelectedEvent } from '@src/components/chat-view/chat-input-field-view/add-context-panel.view';
import MessageContextView from '@src/components/chat-view/chat-input-field-view/message-context.view';
import { AttachedFile } from '@src/components/chat-view/chat-message-edit-input-view/chat-input-field.view';
import { useAnalytics } from '@src/hooks/use-analytics';
import { useWindowEvent } from '@src/hooks/use-window-event';
import { ideApi } from '@src/index';
import { ChatAttachedFile, ChatMessage } from '@src/models/chat.model';
import { useAppStore } from '@src/store/app.store';
import { Dispatch, SetStateAction, useCallback, useEffect } from 'react';

type Props = {
  message: ChatMessage;
  isRagEnabled: boolean;
  onToggleRag: () => void;
  attachedFiles: AttachedFile[];
  setAttachedFiles: Dispatch<SetStateAction<AttachedFile[]>>;
  currentFileEnabled: boolean;
  setCurrentFileEnabled: Dispatch<SetStateAction<boolean>>;
};

export function AddContextPanelView({
  message,
  isRagEnabled,
  onToggleRag,
  attachedFiles,
  setAttachedFiles,
  setCurrentFileEnabled,
  currentFileEnabled,
}: Props) {
  const { state } = useAppStore();
  const { activeFile } = state;
  const analytics = useAnalytics();

  useEffect(() => {
    ideApi.postMessage({
      type: 'requireRepoIndexData',
    });
  }, []);

  const onFilesSelected = useCallback(
    (data: FilesSelectedEvent) => {
      if (containsSameFiles(attachedFiles, data.files)) {
        ideApi.postMessage({
          type: 'showWarningMessage',
          message: 'Adding the same file twice is not allowed',
        });
        return;
      }
      analytics.track('File added to context', {
        from_computer: true,
      });
      setAttachedFiles((prevState) => [
        ...prevState,
        ...data.files.map((f) => ({
          file: f,
          isActiveFile: false,
          isAttachedFile: true,
        })),
      ]);
    },
    [analytics, attachedFiles, setAttachedFiles]
  );
  useWindowEvent('filesSelectedForMessage', onFilesSelected);

  const onFileClick = (allowImages: boolean) => {
    ideApi.postMessage({
      type: 'selectFileForMessage',
      message,
      allowImages,
    });
  };

  const onCurrentFileToggle = () => {
    setCurrentFileEnabled((prevState) => !prevState);
    setAttachedFiles((prevState) => {
      let result = [...prevState];
      const existingFile = result.find((f) => f.file.fsPath === activeFile?.fsPath);
      if (!currentFileEnabled && activeFile) {
        if (existingFile) {
          existingFile.isActiveFile = true;
        } else {
          result.push({ file: activeFile, isActiveFile: true, isAttachedFile: false });
        }
        setCurrentFileEnabled(true);
        return result;
      } else if (currentFileEnabled) {
        if (existingFile?.isAttachedFile) {
          existingFile.isActiveFile = false;
        }
        setCurrentFileEnabled(false);
        return result.filter((f) => f.file.fsPath !== activeFile?.fsPath);
      } else {
        return result;
      }
    });
  };

  return (
    <MessageContextView
      position={'bottom start'}
      isRagEnabled={isRagEnabled}
      isCurrentFileEnabled={currentFileEnabled}
      attachedFiles={attachedFiles.map((f) => f.file)}
      onChangeAttachedFiles={(newFiles) => {
        if (!newFiles?.length) {
          setAttachedFiles([]);
          return;
        }

        setAttachedFiles(
          newFiles.map((f) => ({
            file: f,
            isActiveFile: false,
            isAttachedFile: true,
          }))
        );
        analytics.track('File added to context', {
          from_computer: false,
        });
      }}
      onFileClick={onFileClick}
      onToggleRag={onToggleRag}
      onCurrentFileToggle={onCurrentFileToggle}
    />
  );
}

function containsSameFiles(currentFiles: AttachedFile[], newFiles: ChatAttachedFile[]): boolean {
  const paths = newFiles.map((f) => f.fsPath);
  return currentFiles.some((f) => paths.includes(f.file.fsPath));
}
