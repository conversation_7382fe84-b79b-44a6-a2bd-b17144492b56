import { AgentThoughts } from './agent-thoughts';
import { ApplyMessage } from './apply-message';
import { LoadingPart } from './loading-dots-part.view';
import LoadingMarkerPartView from './loading-marker-part.view';
import { MessageDiff } from './message-diff';

// add message components to this registry for attaching to chat
export const components = {
  AgentThoughts,
  ApplyMessage,
  LoadingMarker: LoadingMarkerPartView,
  LoadingPart,
  MessageDiff: MessageDiffLegacy,
};

type Components = { [k in keyof typeof components]: Parameters<(typeof components)[k]>[0] };

type TypeToUnion<T extends keyof K, K> = T extends unknown ? { name: T; props: K[T] } : never;

export type UiComponent = TypeToUnion<keyof Components, Components>;

export type UnionToIntersection<U> = (U extends unknown ? (x: U) => void : never) extends (
  x: infer I
) => void
  ? I
  : never;

/**
 * @deprecated
 * required to support old typing
 */
function MessageDiffLegacy(props: Parameters<typeof MessageDiff>[0] & { id: string }) {
  return MessageDiff(props);
}
