import { DestructiveButton, SecondaryButton } from '@src/components/ui-kit/button';
import { DialogTitle, ModalDialog } from '@src/components/ui-kit/dialog';
import { Tooltip } from '@src/components/ui-kit/tooltip';
import { RollbackFilePath, RollbackInfo } from '@src/models/chat.model';
import { ReactNode, useCallback, useMemo } from 'react';

interface Props {
  title: string | ReactNode;
  revertUnavailableTitle?: string;
  data: RollbackInfo;
  isOpened: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export function RollbackDialogView({
  title,
  revertUnavailableTitle,
  data,
  isOpened,
  onConfirm,
  onCancel,
}: Props) {
  revertUnavailableTitle ??= 'Revert not available';

  const displayFileList = useCallback(
    (files: RollbackFilePath[], tooltipPrefix: string) => (
      <ul className="list-disc list-inside ps-4">
        {files.map((file, index) => {
          const tooltipId = `${tooltipPrefix}-${index}`;
          return (
            <>
              <li key={index} className="mb-1 truncate" data-tooltip-id={tooltipId}>
                {file.displayPath || file.path}
              </li>
              <Tooltip id={tooltipId}>{file.fsPath}</Tooltip>
            </>
          );
        })}
      </ul>
    ),
    []
  );

  const filesToRemove = useMemo(() => {
    return data.files.filter((f) => f.isToRemove);
  }, [data]);
  const missingFiles = useMemo(() => {
    return data.files.filter((f) => f.isMissingSnapshot);
  }, [data]);
  const filesToRollback = useMemo(() => {
    return data.files.filter((f) => !f.isToRemove && !f.isMissingSnapshot);
  }, [data]);
  const noFiles = data.files.length === 0;
  const hasApplicableChanges = filesToRollback.length + filesToRemove.length !== 0;

  return (
    <ModalDialog open={isOpened} onClose={onCancel}>
      <DialogTitle>
        {hasApplicableChanges ? <>{title}</> : <>{revertUnavailableTitle}</>}
      </DialogTitle>
      <div className="mb-4 max-h-60 overflow-y-auto">
        {noFiles && (
          <p className={'text-secondary-foreground'}>
            No existing files were changed by the agent.
          </p>
        )}
        {filesToRollback.length > 0 && (
          <>
            <p className="mb-2">
              This action will remove your own edits, along with the agent changes in the following
              files:
            </p>
            {displayFileList(filesToRollback, 'rollback-files')}
          </>
        )}
        {filesToRemove.length > 0 && (
          <>
            <p className="mt-3 mb-2">The following files will be deleted as part of this revert:</p>
            {displayFileList(filesToRemove, 'remove-files')}
          </>
        )}
        {missingFiles.length > 0 && (
          <>
            <p className="mt-3 mb-2">The following files cannot be rolled back:</p>
            {displayFileList(missingFiles, 'missing-files')}
          </>
        )}
      </div>
      <div className="flex justify-end gap-x-2">
        <SecondaryButton onClick={onCancel}>Cancel</SecondaryButton>
        {hasApplicableChanges && <DestructiveButton onClick={onConfirm}>Revert</DestructiveButton>}
      </div>
    </ModalDialog>
  );
}
