import { RollbackDialogView } from '@src/components/chat-view/chat-message-view/message-components/rollback-dialog.view';
import { getFileName } from '@src/components/chat-view/chat-message-view/utils';
import { ChatAction } from '@src/components/chat-view/types';
import { LinkButton, PrimaryButton } from '@src/components/ui-kit/button';
import { LanguageIcon } from '@src/components/ui-kit/language-icon';
import { Tooltip } from '@src/components/ui-kit/tooltip';
import CheckMultipleIcon from '@src/icons/check-multiple.svg';
import CheckIcon from '@src/icons/check.svg';
import ChevronRightIcon from '@src/icons/chevron-right.svg';
import RevertIcon from '@src/icons/revert.svg';
import UnreadDotIcon from '@src/icons/unread-dot.svg';
import { ChatMessage, ClientDiff, RollbackInfo } from '@src/models/chat.model';
import { useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';
import { twJoin, twMerge } from 'tailwind-merge';

type ClientDiffMessage = {
  id: string;
} & ClientDiff;

export type MessageDiffProps = {
  diffs: ClientDiff[];
  parentMessage: ChatMessage;
  className?: string;
  onExternalAction?: (action: ChatAction) => void;
};

const TypeIcon = styled(LanguageIcon)`
  height: 16px;
  display: inline-block;
  margin: 2px;
`;

export const MessageDiff = ({
  diffs,
  parentMessage,
  className,
  onExternalAction,
}: MessageDiffProps) => {
  const { id, context, isGhost } = parentMessage;
  const revertInfo = context?.revertAppliedDiffsInfo;
  const canApplyChanges = useMemo(
    () => !isGhost && diffs.some((d) => !d.isApplied),
    [diffs, isGhost]
  );
  const canRevertChanges = useMemo(
    () => !isGhost && !canApplyChanges && !!revertInfo,
    [revertInfo, isGhost, canApplyChanges]
  );

  const onViewPatch = useCallback(
    (params: ClientDiffMessage) => {
      if (params.isApplied) {
        onExternalAction?.({
          type: 'showAppliedDiff',
          ...params,
        });
      } else {
        onExternalAction?.({
          type: 'showDiff',
          ...params,
        });
      }
    },
    [onExternalAction]
  );

  const onApplyAllChanges = useCallback(() => {
    onExternalAction?.({
      type: 'applyAllDiffs',
      id,
      diffs: diffs.filter((d) => !d.isApplied),
    });
  }, [id, onExternalAction, diffs]);

  const onRevertChanges = useCallback(() => {
    onExternalAction?.({
      type: 'revertDiffs',
      id,
      revertInfo: revertInfo!,
    });
  }, [onExternalAction, revertInfo, id]);

  if (!diffs.length) {
    return <></>;
  }

  return (
    <div className={twMerge('my-2 select-none', className)}>
      <div className={'my-2 flex gap-x-2 items-center'}>
        <span className={'font-bold flex-1 truncate'}>Changes</span>
        {canApplyChanges && (
          <PrimaryButton
            onClick={onApplyAllChanges}
            className={'ps-2'}
            aria-label="Review changes apply button"
          >
            {diffs.length > 1 ? (
              <>
                <CheckMultipleIcon className={'mr-1'} />
                Apply all
              </>
            ) : (
              <>
                <CheckIcon className={'mr-1'} />
                Apply
              </>
            )}
          </PrimaryButton>
        )}
        {canRevertChanges && (
          <RevertDiffsView
            data={revertInfo!}
            buttonText={diffs.length > 1 ? 'Revert all' : 'Revert'}
            onRevert={onRevertChanges}
          />
        )}
      </div>
      <div
        className={twMerge('rounded-lg overflow-hidden border text-base', className)}
        aria-label="Review changes file container"
      >
        {diffs.map((diff, index) => {
          const { path, isNew, isApplied, isUnread } = diff;
          const { startPart, endPartReversed } = splitPath(path, 10);
          const tooltipId = `diff-${id}-${index}`;
          return (
            <div
              key={index}
              className={twJoin(
                'group flex flex-row items-center gap-x-1',
                'ps-1.5 pe-2 py-1.5',
                'border-t first:border-none',
                'leading-5',
                'hover:bg-hover-background cursor-pointer'
              )}
              onClick={() => onViewPatch({ id, ...diff })}
            >
              <TypeIcon data-tooltip-id={tooltipId} fileName={getFileName(path)} />
              <div
                data-tooltip-id={tooltipId}
                className={twMerge(
                  'flex overflow-hidden text-nowrap min-w-0',
                  isApplied && 'text-secondary-foreground'
                )}
              >
                <span>{startPart}</span>
                <span
                  className="truncate"
                  style={{ direction: 'rtl', unicodeBidi: 'bidi-override' }}
                >
                  {endPartReversed}
                </span>
              </div>
              <Tooltip id={tooltipId}>{path}</Tooltip>
              {isNew && <span className={'ms-1 text-disabled-foreground shrink-0'}>New</span>}
              {isUnread && (
                <UnreadDotIcon className={'text-primary-background shrink-0 w-2 h-5 px-0.5 py-2'} />
              )}
              <div className="flex-grow"></div>
              {isApplied ? (
                <CheckIcon className={'shrink-0'} aria-label="Change applied icon" />
              ) : (
                <ChevronRightIcon className={'shrink-0'} aria-label="Change not applied icon" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

function splitPath(path: string, startLength: number) {
  if (path.length <= startLength) {
    return { startPart: path, endPart: '' };
  }
  const startPart = path.slice(0, startLength);
  const endPartReversed = path.slice(startLength).split('').reverse().join('');
  return { startPart, endPartReversed };
}

function RevertDiffsView({
  data,
  buttonText,
  onRevert,
}: {
  data: RollbackInfo;
  buttonText: string;
  onRevert: () => void;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleRevertClick = () => {
    setIsModalOpen(true);
  };

  const handleConfirm = () => {
    onRevert();
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <div className={'mt-2 mb-1 flex justify-end'}>
        <LinkButton onClick={handleRevertClick}>
          <RevertIcon className="me-1" />
          {buttonText}
        </LinkButton>
      </div>

      <RollbackDialogView
        title={'Revert changes'}
        data={data}
        isOpened={isModalOpen}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </>
  );
}
