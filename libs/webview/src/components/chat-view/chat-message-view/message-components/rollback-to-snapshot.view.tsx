import { RollbackDialogView } from '@src/components/chat-view/chat-message-view/message-components/rollback-dialog.view';
import { LinkButton } from '@src/components/ui-kit/button';
import { FormattedDate } from '@src/components/ui-kit/formatted-date';
import RevertIcon from '@src/icons/revert.svg';
import { ideApi } from '@src/index';
import { ChatId, RollbackInfo } from '@src/models/chat.model';
import { useState } from 'react';

interface Props {
  rollbackInfo: RollbackInfo;
  chatId: ChatId;
}

export function RollbackToSnapshotView({ rollbackInfo, chatId }: Props) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleClick = () => {
    setIsModalOpen(true);
  };

  const handleConfirm = () => {
    // Send message to IDE to perform the rollback
    ideApi.postMessage({
      type: 'rollbackToSnapshot',
      chatId,
      rollbackInfo,
    });
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const title = (
    <>
      Revert to version from{' '}
      <span className={'lowercase'}>
        <FormattedDate value={rollbackInfo.timestamp} />
      </span>
    </>
  );

  return (
    <>
      <div className={'mt-2 mb-1 flex justify-end'}>
        <LinkButton onClick={handleClick}>
          <RevertIcon className="me-1" />
          Revert changes to this point
        </LinkButton>
      </div>

      <RollbackDialogView
        title={title}
        data={rollbackInfo}
        isOpened={isModalOpen}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </>
  );
}
