import * as Sentry from '@sentry/react';
import { ActionableCodeBlockView } from '@src/components/chat-view/actionable-code-block/actionable-code-block.view';
import { SyntaxHighlight } from '@src/components/ui-kit/syntax-highlight';
import { getLanguageNameByFilepath } from '@src/utils/language-map.helpers';
import { formatPatch, ParsedDiff, parsePatch } from 'diff';
import { Fragment, useMemo } from 'react';
import { twJoin } from 'tailwind-merge';

interface Props {
  lineBasedDiff: string;
}

interface ProcessedLine {
  text: string;
  type: 'insert' | 'remove' | '';
}

interface ProcessedDiff {
  newFileName?: string;
  rawDiffText: string;
  language?: string;
  hunks: {
    header: string;
    lines: ProcessedLine[];
  }[];
}

export function CodeHunkView({ lineBasedDiff }: Props) {
  const parsedDiffs = useMemo(() => {
    try {
      return parsePatch(lineBasedDiff);
    } catch (e) {
      Sentry.captureException(e);
      return [];
    }
  }, [lineBasedDiff]);

  const processedDiffs = useMemo(() => {
    return parsedDiffs.map(processDiff);
  }, [parsedDiffs]);

  return (
    <>
      {processedDiffs.map((diff, index) => (
        <ActionableCodeBlockView
          key={index}
          language={diff.language}
          codeWrapClassName={'py-2 px-0'}
          allowRun={false}
          allowPaste={false}
          // let's use unprocessed value if possible
          rawText={processedDiffs.length === 1 ? lineBasedDiff : diff.rawDiffText}
        >
          <table className={'border-collapse w-full'}>
            <tbody>
              {diff.hunks.map((hunk, i) => (
                <Fragment key={i}>
                  <tr>
                    <td className={'text-disabled-foreground select-none px-2 py-0'}>
                      {hunk.header}
                    </td>
                  </tr>
                  {hunk.lines.map(({ text, type }, j) => (
                    <tr
                      key={j}
                      className={twJoin(
                        type === 'insert' && 'bg-inserted-line-background',
                        type === 'remove' && 'bg-removed-line-background'
                      )}
                    >
                      <td className={'px-2 py-0'}>
                        <SyntaxHighlight language={diff.language}>{text}</SyntaxHighlight>
                      </td>
                    </tr>
                  ))}
                </Fragment>
              ))}
            </tbody>
          </table>
        </ActionableCodeBlockView>
      ))}
    </>
  );
}

function processDiff(diff: ParsedDiff): ProcessedDiff {
  const { newFileName, hunks } = diff;

  const processedHunks = hunks.length
    ? hunks.map((hunk) => ({
        header: `@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@`,
        lines: hunk.lines.map(
          (line): ProcessedLine => ({
            text: line,
            type: line.startsWith('+') ? 'insert' : line.startsWith('-') ? 'remove' : '',
          })
        ),
      }))
    : [
        {
          header: 'Empty file.',
          lines: [],
        },
      ];

  return {
    newFileName,
    language: newFileName ? getLanguageNameByFilepath(newFileName) : undefined,
    rawDiffText: formatPatch(diff),
    hunks: processedHunks,
  };
}
