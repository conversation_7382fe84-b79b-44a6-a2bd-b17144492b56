import { useAnalytics } from '@src/hooks/use-analytics';
import LightBulbIcon from '@src/icons/light-bulb.svg';
import { useCallback, useEffect, useMemo } from 'react';
import { twMerge } from 'tailwind-merge';
import { PrimaryButton, SecondaryButton } from '../../ui-kit/button';
import { ChatAction } from '../types';

interface Props {
  waitSeconds?: number;
  onExternalAction?: (action: ChatAction) => void;
  className?: string;
}

export function ChatMessageUpgradePlanView({ waitSeconds, onExternalAction, className }: Props) {
  const analytics = useAnalytics();

  useEffect(() => {
    analytics.track('Plan limit message displayed', {
      wait_seconds: waitSeconds,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formattedWait = useMemo(() => {
    if (!waitSeconds) return null;
    const futureTime = new Date();
    futureTime.setSeconds(futureTime.getSeconds() + waitSeconds);
    const time = futureTime.toLocaleTimeString(undefined, {
      hour: 'numeric',
      minute: 'numeric',
    });
    const date = futureTime.toLocaleDateString('en', {
      month: 'long',
      day: 'numeric',
    });
    return `${time} on ${date}`;
  }, [waitSeconds]);

  const handleUpgrade = useCallback(() => {
    analytics.track('Plan limit upgrade clicked', {
      source: 'chat',
    });
    onExternalAction?.({ type: 'openUpgradePlan' });
  }, [analytics, onExternalAction]);

  const handleRegenerate = useCallback(() => {
    analytics.track('Plan limit try again clicked');
    onExternalAction?.({ type: 'regenerateLastAnswer' });
  }, [analytics, onExternalAction]);

  return (
    <div
      aria-label="Plan usage limit reached"
      className={twMerge('m-1 rounded-xl p-4 border bg-secondary-background', className)}
    >
      <div className="flex items-start">
        <LightBulbIcon className={'text-warning-foreground ms-0.5 me-1.5 my-1'} />
        <div className="flex-1 flex flex-col justify-between">
          <h4 className="m-0 text-base font-semibold">You’ve hit your plan usage limits</h4>
          <p className="mt-1 text-sm">
            Upgrade now to continue, or{' '}
            {formattedWait ? <>wait until {formattedWait}.</> : <>try again later.</>}
          </p>

          <div className="mt-4 flex flex-wrap gap-2">
            <PrimaryButton onClick={handleUpgrade}>Upgrade</PrimaryButton>
            <SecondaryButton onClick={handleRegenerate}>Try Again</SecondaryButton>
          </div>
        </div>
      </div>
    </div>
  );
}
