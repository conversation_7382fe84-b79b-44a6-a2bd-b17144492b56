import { AssistantMessageContextView } from '@src/components/chat-view/chat-message-view/assistant-message-context.view';
import { ChatMessageUpgradePlanView } from '@src/components/chat-view/chat-message-view/chat-message-upgrade-plan.view';
import { ApplyMessage } from '@src/components/chat-view/chat-message-view/message-components/apply-message';
import { CodeHunkView } from '@src/components/chat-view/chat-message-view/message-components/code-hunk.view';
import { LoadingPart } from '@src/components/chat-view/chat-message-view/message-components/loading-dots-part.view';
import { MessageDiff } from '@src/components/chat-view/chat-message-view/message-components/message-diff';
import { ToolCallView } from '@src/components/chat-view/chat-message-view/message-components/tool-call.view';
import { mergeMarkdownMessageContent } from '@src/components/chat-view/chat-message-view/utils';
import type {
  ChatMessage,
  ErrorTextMessagePart,
  MemoryMessagePart,
  WarningTextMessagePart,
} from '@src/models/chat.model';
import { useMemo } from 'react';
import { twMerge } from 'tailwind-merge';
import Logo from '../../../icons/logo-icon.svg';
import { MarkdownMessageView } from '../markdown-message/markdown-message.view';
import { ChatAction } from '../types';
import { ChatMessageErrorView } from './chat-message-error.view';
import { ChatMessageMemoryView } from './chat-message-memory.view';
import { ChatMessageWarningView } from './chat-message-warning.view';
import { GroupedChecklist } from './message-components/grouped-checklist';
import LoadingMarkerPartView from './message-components/loading-marker-part.view';
import { RouteMessage } from './message-components/route-message';
import { ShellCommand } from './message-components/shell-command';

interface ChatMessageAssistantProps {
  message: ChatMessage;
  onExternalAction?: (action: ChatAction) => void;
  isLastMessage?: boolean;
  shouldShowAuthor?: boolean;
  className?: string;
}

export function ChatMessageAssistantView({
  isLastMessage,
  message,
  shouldShowAuthor = true,
  onExternalAction,
  className,
}: ChatMessageAssistantProps) {
  const mergedContent = useMemo(() => {
    return mergeMarkdownMessageContent(message.content);
  }, [message.content]);

  const isError = mergedContent[0]?.type === 'errorText';
  const isWarning = mergedContent[0]?.type === 'warningText';
  const hasTextPart = mergedContent.some(
    (item) => item.type === 'text' || item.type === 'codeSnippet'
  );
  // TODO: This is a workaround to show message context in Coding Agent chat.
  // Will be removed when we create a GraphQL API to manage message parts and remove AgentThoughts component altogether
  const hasAgentThoughts = mergedContent.some(
    (item) => item.type === 'uiComponent' && item.component.name === 'AgentThoughts'
  );
  const showContext = !isError && (hasTextPart || hasAgentThoughts);

  const messageNodes = useMemo(() => {
    const result: JSX.Element[] = [];

    for (let idx = 0; idx < mergedContent.length; idx++) {
      const item = mergedContent[idx];

      if (!item) continue;

      if (item.type === 'loadingMarker') {
        result.push(<LoadingMarkerPartView key={idx} steps={item.steps} />);
      } else if (item.type === 'uiComponent') {
        result.push(
          <RouteMessage
            key={idx}
            id={message.id}
            content={item}
            onExternalAction={onExternalAction}
          />
        );
      } else if (item.type === 'memoryCreated') {
        result.push(<ChatMessageMemoryView key={idx} content={item as MemoryMessagePart} />);
      } else if (item.type === 'text') {
        result.push(
          <MarkdownMessageView
            key={idx}
            content={item.text}
            className={'text-wrap break-words py-2'}
          />
        );
      } else if (item.type === 'loadingPart') {
        result.push(<LoadingPart key={idx} text={item.text} />);
      } else if (item.type === 'codePatch') {
        result.push(
          <MessageDiff
            key={idx}
            parentMessage={message}
            diffs={item.diffs}
            onExternalAction={onExternalAction}
          />
        );
      } else if (item.type === 'codeHunk') {
        result.push(<CodeHunkView key={idx} lineBasedDiff={item.lineBasedDiff} />);
      } else if (item.type === 'applyMessage') {
        result.push(
          <ApplyMessage key={idx} messageId={message.id} onExternalAction={onExternalAction} />
        );
      } else if (item.type === 'toolCall' && !item.isHidden) {
        result.push(<ToolCallView key={idx} {...item} />);
      } else if (item.type === 'shellCommand') {
        const prevItem = mergedContent[idx - 1];
        result.push(
          <ShellCommand
            key={idx}
            messageId={message.id}
            isDisabled={!isLastMessage}
            onExternalAction={onExternalAction}
            shouldDiscourageRunning={
              prevItem?.type === 'codePatch' && prevItem.diffs.every((diff) => !diff.isApplied)
            }
            {...item}
          />
        );
      } else if (item.type === 'groupedChecklist') {
        result.push(
          <GroupedChecklist
            key={idx}
            isDisabled={!isLastMessage || item.disabled}
            messageId={message.id}
            onExternalAction={onExternalAction}
            {...item}
          />
        );
      }
    }

    return result;
  }, [isLastMessage, mergedContent, message, onExternalAction]);

  if (isError) {
    const errorMessagePart = message.content[0] as ErrorTextMessagePart;
    const firstAction = errorMessagePart?.actions?.[0];
    const isUpgradePlanError = firstAction?.type === 'upgradePlan';

    return isUpgradePlanError ? (
      <ChatMessageUpgradePlanView
        waitSeconds={firstAction.waitSeconds}
        onExternalAction={onExternalAction}
      />
    ) : (
      <ChatMessageErrorView
        content={message.content[0] as ErrorTextMessagePart}
        onExternalAction={onExternalAction}
      />
    );
  }

  if (isWarning) {
    return (
      <ChatMessageWarningView
        content={mergedContent[0] as WarningTextMessagePart}
        onExternalAction={onExternalAction}
      />
    );
  }

  return (
    <div
      className={twMerge('peer px-5 py-2 has-[+.peer]:pb-0 [&+.peer]:pt-0', className)}
      aria-label="Chat message assistant"
    >
      {shouldShowAuthor && (
        <div className="flex items-center pt-2 font-semibold leading-4">
          <Logo className="text-primary-background me-1.5 h-5 w-5" />
          {message.context?.author || 'Zencoder'}
        </div>
      )}
      {showContext && <AssistantMessageContextView context={message.context ?? {}} />}
      {messageNodes}
    </div>
  );
}
