import { useAnalytics } from '@src/hooks/use-analytics';
import { ErrorTextMessagePart } from '@src/models/chat.model';
import { Routes } from '@src/models/router';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';
import { SecondaryButton } from '../../ui-kit/button';
import { Icon } from '../../ui-kit/icon';
import { MarkdownMessageView } from '../markdown-message/markdown-message.view';
import { ChatAction } from '../types';

interface Props {
  content: ErrorTextMessagePart;
  onExternalAction?: (action: ChatAction) => void;
  className?: string;
}

export function ChatMessageErrorView({ content, onExternalAction, className }: Props) {
  const navigate = useNavigate();
  const hasActions = !!content.actions?.length;
  const analytics = useAnalytics();

  useEffect(() => {
    analytics.track('Error message displayed', {
      error_text: content.text,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const doRegenerate = () => {
    onExternalAction?.({
      type: 'regenerateLastAnswer',
    });
  };

  return (
    <div
      className={twMerge(
        'm-1',
        'rounded-xl',
        'p-1',
        'border',
        'border-error-foreground',
        className
      )}
      aria-label="Chat message error"
    >
      <h4 className={'px-1 pt-1 mb-0 truncate leading-5'}>
        <Icon type={'error'} className={'me-1 text-error-foreground'} />
        {content.title ?? 'Something went wrong'}
      </h4>
      <MarkdownMessageView content={content.text} className={'text-sm py-2 px-7 overflow-x-auto'} />
      {hasActions && (
        <div className={'pt-1 px-7 pb-2 flex flex-wrap gap-1'}>
          {content.actions?.map((action, i) => {
            return (
              <SecondaryButton
                key={i}
                onClick={() => {
                  switch (action.type) {
                    case 'externalAction':
                      onExternalAction?.({
                        type: action.action,
                      });
                      break;
                    case 'openModelsSettings':
                      navigate(`/${Routes.modelsSettings}`);
                      break;
                    default:
                      throw new Error(`Unexpected action type: ${action.type}`);
                  }
                }}
              >
                {action.title}
              </SecondaryButton>
            );
          })}
        </div>
      )}
      {content.allowRegenerate && (
        <div className={'py-1 px-7'}>
          <SecondaryButton onClick={doRegenerate}>Regenerate</SecondaryButton>
        </div>
      )}
    </div>
  );
}
