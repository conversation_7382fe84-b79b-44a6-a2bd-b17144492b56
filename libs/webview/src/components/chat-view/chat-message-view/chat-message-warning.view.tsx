import { useAnalytics } from '@src/hooks/use-analytics';
import { WarningTextMessagePart } from '@src/models/chat.model';
import { useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { PrimaryButton, SecondaryButton } from '../../ui-kit/button';
import { Icon } from '../../ui-kit/icon';
import { MarkdownMessageView } from '../markdown-message/markdown-message.view';
import { ChatAction } from '../types';

interface Props {
  content: WarningTextMessagePart;
  onExternalAction?: (action: ChatAction) => void;
  className?: string;
}

export function ChatMessageWarningView({ content, onExternalAction, className }: Props) {
  const analytics = useAnalytics();
  useEffect(() => {
    analytics.track('Warning message displayed', {
      error_text: `${content.title}\n${content.text}`,
    });
  });

  return (
    <div
      className={twMerge(
        'm-1',
        'rounded-xl',
        'p-1',
        'border',
        'border-warning-foreground',
        className
      )}
      aria-label="Chat message warning"
    >
      <h4 className={'px-1 pt-1 mb-0 truncate leading-5'}>
        <Icon type={'warning'} className={'me-1 text-warning-foreground'} />
        {content.title}
      </h4>
      <MarkdownMessageView content={content.text} className={'text-sm py-2 px-7 overflow-x-auto'} />
      <div className={'pt-1 px-7 pb-2 flex flex-wrap gap-1'}>
        {content.actions.map((action, i) => {
          if (i === 0) {
            return (
              <PrimaryButton
                key={i}
                onClick={() => {
                  onExternalAction?.({
                    type: action.action,
                  });
                }}
              >
                {action.title}
              </PrimaryButton>
            );
          } else {
            return (
              <SecondaryButton
                key={i}
                onClick={() => {
                  onExternalAction?.({
                    type: action.action,
                  });
                }}
              >
                {action.title}
              </SecondaryButton>
            );
          }
        })}
      </div>
    </div>
  );
}
