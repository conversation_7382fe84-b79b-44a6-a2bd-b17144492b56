import { checkIfShouldWarnForLongChat } from '@src/components/chat-view/check-if-should-warn-for-long-chat';
import { ChatAction } from '@src/components/chat-view/types';
import { LinkButton } from '@src/components/ui-kit/button';
import { useAnalytics } from '@src/hooks/use-analytics';
import CloseIcon from '@src/icons/close-icon.svg';
import LightBulbIcon from '@src/icons/light-bulb.svg';
import { ideApi } from '@src/index';
import { ChatId, ChatModel, ChatWarning } from '@src/models/chat.model';
import { useCallback, useMemo } from 'react';
import { twMerge } from 'tailwind-merge';
import { NewChatButton } from '../common/new-chat-button';

const warningCtClassName =
  'flex border border-warning-foreground rounded-lg px-2 pt-1 pb-[18px] -mb-4';
const warningTextClassName = 'leading-6 text-sm flex-1 min-w-8';

interface Props {
  chat: ChatModel;
  onExternalAction: (action: ChatAction) => void;
}

export function ChatWarningView({ chat, onExternalAction }: Props) {
  const chatId = useMemo(() => chat.id, [chat]);

  const showExternalWarning = !!chat.warning;
  const showLongChatWarning = useMemo(() => checkIfShouldWarnForLongChat(chat), [chat]);

  if (showExternalWarning) {
    return <ExternalWarningView {...{ chatId, warning: chat.warning!, onExternalAction }} />;
  }
  if (showLongChatWarning) {
    return <LongChatWarningView chatId={chatId} />;
  }
  return null;
}

function ExternalWarningView({
  warning,
  onExternalAction,
}: {
  chatId: ChatId;
  warning: ChatWarning;
  onExternalAction: (action: ChatAction) => void;
}) {
  return (
    <div className={twMerge(warningCtClassName, 'items-center')}>
      <div className={warningTextClassName}>{warning.text}</div>
      {warning.actions?.map((action, i) => {
        return (
          <LinkButton
            key={i}
            onClick={() => {
              switch (action.type) {
                case 'externalAction':
                  onExternalAction?.({
                    type: action.action,
                  });
                  break;
                default:
                  throw new Error(`Unexpected action type: ${action.type}`);
              }
            }}
          >
            {action.title}
          </LinkButton>
        );
      })}
    </div>
  );
}

function LongChatWarningView({ chatId }: { chatId: ChatId }) {
  const analytics = useAnalytics();

  const onCloseClick = useCallback(() => {
    analytics.track('Long chat warning: Dismiss button clicked');
    ideApi.postMessage({
      type: 'longChatWarningDismissed',
      chatId,
    });
  }, [analytics, chatId]);

  return (
    <div className={twMerge(warningCtClassName, 'items-start')}>
      <LightBulbIcon className={'text-warning-foreground ms-0.5 me-1.5 my-1'} />
      <div className={warningTextClassName}>Long chats use up limits faster</div>
      <NewChatButton />
      <LinkButton className={'px-1'} onClick={onCloseClick}>
        <CloseIcon />
      </LinkButton>
    </div>
  );
}
