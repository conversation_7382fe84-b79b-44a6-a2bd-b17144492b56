import { InlineLoaderView } from '@src/components/loaders/inline-loader.view';
import { LinkButton, PrimaryButton } from '@src/components/ui-kit/button';
import { Field, Input, Label, Select, Textarea } from '@src/components/ui-kit/form-elements';
import { Icon } from '@src/components/ui-kit/icon';
import { ideApi } from '@src/index';
import { McpServerInfo } from '@src/models/mcp-servers.model';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import 'react-json-view-lite/dist/index.css';
import { useNavigate, useParams } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';
import { NewChatButton } from '../common/new-chat-button';

interface AgentToolEditParams extends Record<string, string> {
  toolId: string;
}

function getCustomMcpServerConfig(toolId = ''): McpServerInfo {
  return {
    id: toolId,
    name: toolId.replaceAll('_', ' '),
    configs: [
      {
        command: '',
      },
    ],
    status: {
      status: 'library',
    },
  };
}

function prettifyJson(jsonConfig: object): string {
  try {
    return JSON.stringify(jsonConfig, undefined, 4);
  } catch (error) {
    return JSON.stringify(jsonConfig);
  }
}

export function AgentToolEditView() {
  let { toolId } = useParams<AgentToolEditParams>();
  const { state } = useAppStore();
  const navigate = useNavigate();

  const isNewCustomServer = !toolId || !state.agentTools || !state.agentTools.tools[toolId];

  const tool = useMemo(() => {
    if (isNewCustomServer) {
      return getCustomMcpServerConfig(toolId);
    }
    return state.agentTools!.tools[toolId]!;
  }, [state, toolId, isNewCustomServer]);

  const [initialConfig, setInitialConfig] = useState(
    prettifyJson(tool?.status.config ?? (tool?.configs && tool?.configs[0]) ?? { command: '' })
  );
  const [toolName, setToolName] = useState(tool?.name ?? '');
  // for custom MCP server toolName === toolId with _ instead of spaces
  const calculatedToolId = isNewCustomServer ? toolName.replaceAll(' ', '_') : toolId;

  const [selectedConfigIndex, setSelectedConfigIndex] = useState<number>(0);
  const [jsonConfig, setJsonConfig] = useState<string>(initialConfig);

  const isConfigValid = useMemo(() => {
    try {
      JSON.parse(jsonConfig);
      return true;
    } catch (error) {
      return false;
    }
  }, [jsonConfig]);

  const toolActionMessage = useMemo(() => {
    const cTool = state.agentTools?.tools[calculatedToolId] ?? tool;

    switch (cTool.status.status) {
      case 'installed':
        return jsonConfig === initialConfig ? 'Uninstall' : 'Save';
      case 'installing':
        return 'Installing';
      default:
        return 'Install';
    }
  }, [state.agentTools?.tools, calculatedToolId, tool, jsonConfig, initialConfig]);

  // Initialize the configuration when the tool is loaded or changed
  useEffect(() => {
    if (
      tool?.configs &&
      tool.configs.length > 0 &&
      tool.configs[selectedConfigIndex] &&
      tool.status?.status !== 'installed'
    ) {
      const selectedConfig = tool.configs[selectedConfigIndex];

      setInitialConfig(prettifyJson(selectedConfig));
      setJsonConfig(prettifyJson(selectedConfig));
    }
  }, [selectedConfigIndex]);

  const goToAgentTools = useCallback(() => {
    navigate(`/${Routes.agentTools}`);
  }, [navigate]);

  const handleClick = useCallback(() => {
    if (!tool) return;

    const config = JSON.parse(jsonConfig);

    // API call to install the tool with the selected configuration
    ideApi.postMessage({
      type: toolActionMessage === 'Uninstall' ? 'uninstallAgentTool' : 'installAgentTool',
      payload: {
        toolId: calculatedToolId,
        toolName: isNewCustomServer ? toolName : tool.name,
        config,
      },
    });
    setInitialConfig(prettifyJson(config));
    setJsonConfig(prettifyJson(config));
  }, [calculatedToolId, isNewCustomServer, jsonConfig, tool, toolActionMessage, toolName]);

  const configCommands = useMemo(() => {
    if (!tool?.configs) return [];

    return tool.configs.map((config, index) => ({
      command: config.command,
      index,
    }));
  }, [tool]);

  const [isJustInstalled, setIsJustInstalled] = useState(false);
  const prevStateRef = useRef<string>(tool?.status.status);

  useEffect(() => {
    setIsJustInstalled(
      tool?.status.status === 'installed' && prevStateRef.current === 'installing'
    );
    prevStateRef.current = tool?.status.status;
  }, [tool?.status.status]);

  if (!tool) {
    return (
      <div className="flex flex-col h-full select-none overflow-hidden">
        <header className={'px-4 py-2 border-b flex flex-row justify-between'}>
          <LinkButton className={'ps-1'} onClick={goToAgentTools}>
            <Icon type={'chevron-left'} size={16} />
            Agent Tools
          </LinkButton>
        </header>
        <div className="flex-1 flex items-center justify-center">
          <p>Tool not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full select-none overflow-hidden">
      <header className={'px-4 py-2 border-b flex flex-row justify-between'}>
        <LinkButton className={'ps-1'} onClick={goToAgentTools}>
          <Icon type={'chevron-left'} size={16} />
          Agent Tools
        </LinkButton>
        <NewChatButton />
      </header>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <h1 className="flex my-0">{tool.author ? tool.name : 'Custom MCP Server'}</h1>
              {!tool.author?.startsWith('ZENCODER') && (
                <PrimaryButton disabled={!isConfigValid} onClick={handleClick}>
                  {toolActionMessage === 'Installing' ? (
                    <InlineLoaderView size="sm" color="secondary">
                      {toolActionMessage}
                    </InlineLoaderView>
                  ) : (
                    toolActionMessage
                  )}
                </PrimaryButton>
              )}
            </div>

            {tool.link && (
              <a
                href={tool.link}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline break-words"
              >
                {tool.link}
              </a>
            )}
          </div>

          {!tool.author && (
            <Field className={'mb-3'}>
              <Label className="block mb-2">Name*</Label>
              <Input
                value={toolName}
                disabled={!isNewCustomServer}
                autoFocus={true}
                placeholder={'Name'}
                className={'w-full'}
                onChange={(e) => setToolName(e.target.value)}
              />
            </Field>
          )}

          {tool.author?.startsWith('ZENCODER') && tool.description && (
            <div className="text-sm font-normal">{tool.description}</div>
          )}

          {isJustInstalled && (
            <div
              className="lex items-top p-3 border border-[var(--z-completed)] break-words rounded-lg t-4"
              aria-label="Tool installation success"
            >
              <h4 className={'px-1 pt-1 mb-0 leading-5'}>
                <Icon type={'check'} className={'me-1 text-[var(--z-completed)]'} />
                Successfully installed
              </h4>
            </div>
          )}
          {tool.author && !tool.author.startsWith('ZENCODER') && (
            <div className="flex items-top p-3 border border-yellow-400 rounded-lg t-4">
              <Icon type="lightbulb" size={20} className="text-yellow-500 mr-2" />
              <span className="text-sm">
                This MCP server is community-built. Install and use responsibly.
              </span>
            </div>
          )}

          {tool.status?.status === 'error' && tool.status.detail && (
            <div
              className="lex items-top p-3 border border-error-foreground break-words rounded-lg t-4"
              aria-label="Tool installation error"
            >
              <h4 className={'px-1 pt-1 mb-0 leading-5'}>
                <Icon type={'error'} className={'me-1 text-error-foreground'} />
                {tool.status.detail}
              </h4>
            </div>
          )}
          {tool.author &&
            !tool.author.startsWith('ZENCODER') &&
            tool.status.status !== 'installed' &&
            configCommands.length > 1 && (
              <div className="flex flex-col gap-1.5">
                <h3 className="text-normal">Installation method</h3>
                <div className="text-sm text-secondary-foreground">
                  Ensure that the corresponding tool (like uv, npm, or docker) is installed for the
                  selected method.
                </div>
                <Select
                  value={selectedConfigIndex.toString()}
                  onChange={(e) => setSelectedConfigIndex(parseInt(e.target.value, 10))}
                  className="w-full bg-editor-background p-2 focus:outline-none focus:ring rounded-lg border"
                >
                  {configCommands.map((item, index) => (
                    <option key={index} value={item.index.toString()}>
                      {item.command}
                    </option>
                  ))}
                </Select>
              </div>
            )}

          {!tool.author?.startsWith('ZENCODER') && (
            <div className="flex flex-col gap-1.5">
              <h3 className="text-normal">MCP server config</h3>
              <div className="text-sm text-secondary-foreground">
                Some properties in the config may need to be configured, depending on the MCP
                server.
              </div>
              <div className="min-h-[300px]">
                <Textarea
                  value={jsonConfig}
                  onChange={(e) => {
                    setJsonConfig(e.target.value);
                  }}
                  className={twMerge(
                    'w-full font-mono',
                    !isConfigValid && 'border-error-foreground '
                  )}
                  rows={18}
                />
              </div>
              {!isConfigValid && (
                <div className="text-error-foreground text-sm">
                  Something went wrong. Check your config, maybe you’ve missed something there.
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
