import ChevronRightIcon from '@src/icons/chevron-right.svg';
import McpIcon from '@src/icons/mcp.svg';
import { McpServerInfo } from '@src/models/mcp-servers.model';
import { Routes } from '@src/models/router';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { LinkButton } from '../ui-kit/button';

export const ToolCard = ({
  tool,
  toolId,
  showInstalledLabel,
}: {
  tool: McpServerInfo;
  toolId: string;
  showInstalledLabel: boolean;
}) => {
  const navigate = useNavigate();

  const goToToolEditPage = () => {
    navigate(`/${Routes.agentToolEdit}/${toolId}`, { replace: true });
  };

  const { type, content: Content } = useMemo(() => {
    // If the tool has a custom icon, use it
    if (tool.icon) {
      return {
        type: 'base64',
        content: tool.icon,
      };
    }

    // For custom servers without an icon, use the MCP SVG icon based on theme
    return {
      type: 'svg',
      content: McpIcon,
    };
  }, [tool.icon]);

  const toolAuthorText = useMemo(() => {
    if (tool.author?.startsWith('ZENCODER')) {
      return 'Zencoder';
    }
    return tool.author ?? ' ';
  }, [tool.author]);

  return (
    <LinkButton onClick={goToToolEditPage} className={`p-3 group rounded-lg`}>
      <div className="flex flex-col gap-y-3 w-full">
        <div className="flex items-center w-full">
          <div className="mr-3 flex-shrink-0 w-8 h-8 flex items-center justify-center">
            {type === 'base64' ? (
              <img src={`data:image/png;base64, ${Content}`} alt="" className="rounded-sm" />
            ) : (
              <Content className="w-8 h-8 text-secondary-foreground" />
            )}
          </div>

          <div className="flex flex-col items-start gap-x-4 w-full">
            <div className="flex items-center justify-between w-full ">
              <h3 className="font-medium flex mt-0">{tool.name}</h3>
              {Boolean(showInstalledLabel) && tool.status.status === 'installed' && (
                <div className="group-hover:hidden bg-secondary-background px-[6px] py-[1px] rounded-full text-sm">
                  Installed
                </div>
              )}
              <ChevronRightIcon
                className={'shrink-0 hidden group-hover:block'}
                aria-label="Edit agent tool"
              />
            </div>
            <div className="text-small text-secondary-foreground">{toolAuthorText}</div>
          </div>
        </div>
        {tool.description && (
          <div className="text-left w-full text-sm font-normal">{tool.description}</div>
        )}
      </div>
    </LinkButton>
  );
};
