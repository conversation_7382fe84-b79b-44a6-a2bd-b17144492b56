import { Input } from '@src/components/ui-kit/form-elements';
import { useMcpTools } from '@src/hooks/use-mcp-tools';
import CloseIcon from '@src/icons/close-icon.svg';
import LibraryIcon from '@src/icons/library.svg';
import { ideApi } from '@src/index';
import { ErrorInfo, McpServerCategory, McpServerInfo } from '@src/models/mcp-servers.model';
import { Routes } from '@src/models/router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSessionStorage } from 'usehooks-ts';
import { GoBackButton } from '../common/go-back-button';
import { NewChatButton } from '../common/new-chat-button';
import { BaseLoaderView } from '../loaders/base-loader.view';
import { LinkButton, SecondaryButton } from '../ui-kit/button';
import { Tab, TabGroup, TabList } from '../ui-kit/tabs';
import { ToolCard } from './tool-card-view';

export function AgentToolsView() {
  const { tools, isLoading, error } = useMcpTools();
  const [selectedCategory, setSelectedCategory] = useSessionStorage(
    'agent-tools-category',
    McpServerCategory.ALL
  );
  const [searchQuery, setSearchQuery] = useState('');
  const [dismissedError, setDismissedError] = useState(false);

  // Reset dismissed error state when error changes
  useEffect(() => {
    if (error) {
      setDismissedError(false);
    }
  }, [error]);

  const navigate = useNavigate();

  const filteredTools = useMemo(() => {
    const toolsGroups: Record<McpServerCategory, Array<[string, McpServerInfo]>> = {
      [McpServerCategory.ALL]: [],
      [McpServerCategory.ZENCODER]: [],
      [McpServerCategory.CUSTOM]: [],
      [McpServerCategory.LIBRARY]: [],
    };

    Object.keys(tools).forEach((toolKey) => {
      const tool = tools[toolKey];

      if (!tool || !tool.status) {
        return;
      }

      if (tool.status.status === 'installed') {
        toolsGroups[McpServerCategory.ALL].push([toolKey, tool]);

        if (tool.author?.startsWith('ZENCODER')) {
          toolsGroups[McpServerCategory.ZENCODER].push([toolKey, tool]);
        } else {
          toolsGroups[McpServerCategory.CUSTOM].push([toolKey, tool]);
        }
      }

      if (
        // not Zencoder and not user created
        tool.author &&
        !tool.author?.startsWith('ZENCODER')
      ) {
        toolsGroups[McpServerCategory.LIBRARY].push([toolKey, tool]);
      }
    });

    const searchLower = searchQuery.toLowerCase();

    // filter mcp library tools
    if (searchQuery) {
      toolsGroups[McpServerCategory.LIBRARY] = toolsGroups[McpServerCategory.LIBRARY].filter(
        ([toolKey]) => {
          const tool = tools[toolKey];

          if (!tool) {
            return false;
          }

          return (
            tool.name.toLowerCase().includes(searchLower) ||
            tool.description?.toLowerCase().includes(searchLower)
          );
        }
      );
    }

    for (const groupKey of Object.keys(toolsGroups) as unknown as McpServerCategory[]) {
      const group = toolsGroups[groupKey];

      if (+groupKey === McpServerCategory.LIBRARY && searchQuery) {
        // first show the tools where name match the search query
        group.sort(([aToolKey], [bToolKey]) => {
          const toolA = tools[aToolKey];
          const toolB = tools[bToolKey];

          if (!toolA || !toolB) {
            return 0;
          }

          const nameFound =
            +toolB.name.toLowerCase().includes(searchLower) -
            +toolA.name.toLowerCase()?.includes(searchLower);

          if (nameFound !== 0) {
            return nameFound;
          }

          return toolA.name.localeCompare(toolB.name);
        });
      } else {
        group.sort((a, b) => tools[a[0]]!.name.localeCompare(tools[b[0]]!.name));
      }
    }

    return toolsGroups;
  }, [searchQuery, tools]);

  const onNewMcpServerClick = useCallback(() => {
    const baseId = 'Custom_MCP_server';

    let id = baseId;
    let index = 1;

    while (tools && tools[id]) {
      id = `${baseId}_${index}`;
      index += 1;
    }

    navigate(`/${Routes.agentToolEdit}/${id}`, { replace: true });
  }, [navigate, tools]);

  if (isLoading) {
    return (
      <BaseLoaderView
        progress={50}
        status="Fetching tools from registry and checking installation status..."
      >
        Loading Agent Tools
      </BaseLoaderView>
    );
  }

  return (
    <>
      <header className={'px-2 py-2 border-b flex flex-row justify-between items-center'}>
        <GoBackButton />
        <NewChatButton />
      </header>
      <div className={'flex flex-col text-foreground gap-y-2 px-2'}>
        <div className={'px-3'}>
          <div className={'pt-4 mb-3'}>
            <div className={'flex flex-row gap-x-2 justify-between mb-3'}>
              <h1 className={'m-0'}>Agent Tools</h1>
              <SecondaryButton onClick={onNewMcpServerClick}>Add Custom MCP</SecondaryButton>
            </div>
            <div>
              <p className={'text-secondary-foreground'}>
                Seamlessly make official and community built MCP tools available to Zencoder agents.
              </p>
            </div>
          </div>

          {error && !dismissedError && (
            <ErrorMessage error={error} onDismiss={() => setDismissedError(true)} />
          )}
        </div>
        <TabGroup selectedIndex={selectedCategory}>
          <div className="px-3">
            <TabList>
              <Tab onClick={() => setSelectedCategory(McpServerCategory.ALL)}>All</Tab>
              <Tab
                onClick={() => setSelectedCategory(McpServerCategory.ZENCODER)}
                className={'flex flex-row gap-x-1 items-center'}
              >
                Zencoder{' '}
                <div className="rounded-full flex items-center justify-center px-1 h-3.5 bg-current font-semibold text-xs">
                  <span className={'text-editor-background'}>
                    {filteredTools[McpServerCategory.ZENCODER].length}
                  </span>
                </div>
              </Tab>
              <Tab
                onClick={() => setSelectedCategory(McpServerCategory.CUSTOM)}
                className={'flex flex-row gap-x-1 items-center'}
              >
                Custom{' '}
                <div className="rounded-full flex items-center justify-center px-1 h-3.5 bg-current font-semibold text-xs">
                  <span className={'text-editor-background'}>
                    {filteredTools[McpServerCategory.CUSTOM].length}
                  </span>
                </div>
              </Tab>
              <Tab alignRight onClick={() => setSelectedCategory(McpServerCategory.LIBRARY)}>
                <LibraryIcon className="mr-1 w-4 h-4" />
                MCP Library
              </Tab>
            </TabList>
          </div>
          <div className="mt-3">
            <div>
              {selectedCategory === McpServerCategory.LIBRARY && (
                <div className="relative px-3">
                  <Input
                    autoFocus={true}
                    className="mb-2 w-full h-8 rounded-lg pe-8 ps-2"
                    value={searchQuery}
                    onKeyDown={(e) => {
                      if (e.key === ' ') {
                        e.stopPropagation();
                      }
                    }}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                    }}
                    placeholder="Search"
                  />
                  {searchQuery && (
                    <LinkButton
                      size={'sm'}
                      className="absolute z-10 right-2 top-1/2 -translate-y-1/2 mr-2"
                      onClick={() => {
                        setSearchQuery('');
                      }}
                    >
                      <CloseIcon className="block w-4 h-4" />
                    </LinkButton>
                  )}
                </div>
              )}
              {filteredTools[selectedCategory].length === 0 ? (
                <EmptyState title="No available tools" message="No active tools found" />
              ) : (
                <div className="grid grid-cols-1 gap-4">
                  {filteredTools[selectedCategory].map(([toolId, tool]) => (
                    <ToolCard
                      key={tool.name}
                      tool={tool}
                      toolId={toolId}
                      showInstalledLabel={selectedCategory === McpServerCategory.LIBRARY}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </TabGroup>
      </div>
    </>
  );
}

function EmptyState({ title, message }: { title: string; message: string }) {
  return (
    <div className="flex flex-col items-center justify-center h-64 text-center p-4">
      <h3 className="text-xl font-medium mb-2">{title}</h3>
      <p className="text-secondary-foreground">{message}</p>
    </div>
  );
}

/**
 * Component to display error messages with enhanced UI and actions
 */
function ErrorMessage({ error, onDismiss }: { error: ErrorInfo; onDismiss?: () => void }) {
  const bgColor =
    error.severity === 'ERROR'
      ? 'bg-red-100 dark:bg-red-900/20'
      : error.severity === 'WARNING'
        ? 'bg-yellow-100 dark:bg-yellow-900/20'
        : 'bg-blue-100 dark:bg-blue-900/20';

  const textColor =
    error.severity === 'ERROR'
      ? 'text-red-800 dark:text-red-200'
      : error.severity === 'WARNING'
        ? 'text-yellow-800 dark:text-yellow-200'
        : 'text-blue-800 dark:text-blue-200';

  const borderColor =
    error.severity === 'ERROR'
      ? 'border-red-400 dark:border-red-800'
      : error.severity === 'WARNING'
        ? 'border-yellow-400 dark:border-yellow-800'
        : 'border-blue-400 dark:border-blue-800';

  const iconColor =
    error.severity === 'ERROR'
      ? 'text-red-500 dark:text-red-400'
      : error.severity === 'WARNING'
        ? 'text-yellow-500 dark:text-yellow-400'
        : 'text-blue-500 dark:text-blue-400';

  // Function to copy error details to clipboard
  const copyErrorToClipboard = () => {
    const errorText = `${error.message}${error.details ? '\n\nDetails:\n' + error.details : ''}`;
    navigator.clipboard
      .writeText(errorText)
      .then(() => {
        // Could show a toast notification here if available
        console.log('Error details copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy error details:', err);
      });
  };

  return (
    <div className={`p-4 mb-4 rounded-md border ${bgColor} ${borderColor}`}>
      <div className="flex items-start">
        {/* Icon based on severity */}
        <div className={`mr-3 flex-shrink-0 ${iconColor}`}>
          {error.severity === 'ERROR' && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          )}
          {error.severity === 'WARNING' && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
          {error.severity === 'INFO' && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>

        {/* Error content */}
        <div className="flex-1">
          <h3 className={`text-sm font-medium ${textColor}`}>{error.message}</h3>
          {error.details && (
            <div className={`mt-2 text-sm ${textColor} opacity-90`}>{error.details}</div>
          )}

          {/* Action buttons */}
          <div className="mt-3 flex space-x-2">
            {error.details && (
              <button
                onClick={copyErrorToClipboard}
                className={`inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded ${textColor} hover:bg-opacity-10 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                  />
                </svg>
                Copy Details
              </button>
            )}
            {onDismiss && (
              <button
                onClick={onDismiss}
                className={`inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded ${textColor} hover:bg-opacity-10 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
                Dismiss
              </button>
            )}
            {error.severity === 'ERROR' && (
              <button
                onClick={() => ideApi.postMessage({ type: 'getAgentToolsState' })}
                className={`inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded ${textColor} hover:bg-opacity-10 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Retry
              </button>
            )}
          </div>
        </div>

        {/* Close button */}
        {onDismiss && (
          <div className="ml-3 flex-shrink-0">
            <button
              type="button"
              className={`inline-flex ${textColor} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
              onClick={onDismiss}
            >
              <span className="sr-only">Dismiss</span>
              <svg
                className="h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
