import { pluginFeatureFlags } from '@src/analytics';
import { Kbd } from '@src/components/ui-kit/kbd';

import { useToggleFeatureFlag } from '@src/hooks/feature-flags/use-toggle-feature-flag';
import { useCustomAgent } from '@src/hooks/use-custom-agent';
import CoffeeModeIcon from '@src/icons/coffee-mode.svg';
import { ideApi } from '@src/index';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { Link } from 'react-router-dom';
import { twJoin } from 'tailwind-merge';

interface Props {
  className?: string;
}

export function ChatTips({ className }: Props) {
  const {
    state: { settings },
  } = useAppStore();

  const handleSlashCommandClick = async (commandType: string) => {
    ideApi.postMessage({
      type: 'executeSlashCommand',
      commandType,
    });
  };

  const enableCoffeeMode = useToggleFeatureFlag('enableCoffeeMode', 'on');

  const enableE2EAgent = useToggleFeatureFlag('enable-e2e-agent', 'on');

  const reviewAgent = useCustomAgent('review');

  return (
    <div className={twJoin(className, 'px-4 pb-8')}>
      <div>
        <h2 className={'text-secondary-foreground mb-3'}>Agents</h2>
        <div className={'flex flex-col gap-y-3'}>
          <p>
            <span
              className={'text-link-foreground cursor-pointer'}
              onClick={() => handleSlashCommandClick('code')}
            >
              /code
            </span>{' '}
            to generate new feature or fix bug
          </p>
          {enableE2EAgent === 'on' && (
            <p>
              <span
                className={'text-link-foreground cursor-pointer'}
                onClick={() => handleSlashCommandClick('e2e-test')}
              >
                /e2e-test
              </span>{' '}
              to generate end-to-end tests
            </p>
          )}
          <p>
            <span
              className={'text-link-foreground cursor-pointer'}
              onClick={() => handleSlashCommandClick('unittests')}
            >
              /unittests
            </span>{' '}
            to generate unit tests
          </p>
          {!!reviewAgent.agent && (
            <p>
              <span
                className={'text-link-foreground cursor-pointer'}
                onClick={reviewAgent.onCallAgent}
              >
                /{reviewAgent.agent.commandName}
              </span>{' '}
              code to recommend improvements
            </p>
          )}
          <p>
            <Link
              to={`/${Routes.customAgents}`}
              className="text-foreground hover:text-foreground font-semibold"
            >
              View all...
            </Link>
          </p>
        </div>
      </div>

      {/** TODO [SN] remove pluginFeatureFlags when JB supports new flags */}
      {(pluginFeatureFlags.enableCoffeeMode || enableCoffeeMode === 'on') && (
        <div className="mt-8">
          <h2 className={'text-secondary-foreground'}>
            <CoffeeModeIcon
              className={'inline-block text-primary-background'}
              width={20}
              height={20}
            />
            Coffee mode
          </h2>
          <div className={'flex flex-col gap-y-2'}>
            <p>
              Run with <Kbd caption={settings?.isMacOs ? '⌘' : 'CTRL'} highlight />{' '}
              <Kbd caption="⇧" highlight /> <Kbd caption="↵" highlight /> to automatically apply
              changes
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
