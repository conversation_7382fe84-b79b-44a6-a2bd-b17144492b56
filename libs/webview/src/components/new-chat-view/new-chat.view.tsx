import { <PERSON><PERSON><PERSON>ead<PERSON> } from '@src/components/chat-view/chat-header.view';
import { PlaceholderElement } from '@src/components/chat-view/chat-input-field-view/message-editor/placeholder-element.view';
import { useCodingAgentDefaultState } from '@src/hooks/use-coding-agent-default-state';
import LogoIcon from '@src/icons/logo-icon.svg';
import { ChatSettings } from '@src/models/chat.model';
import { Routes } from '@src/models/router';
import { useAppStore } from '@src/store/app.store';
import { trimMessage } from '@src/utils/chat.helpers';
import { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { ideApi } from '../..';
import { ChatTips } from '../chat-tips';
import {
  ChatInputField,
  UserMessage,
} from '../chat-view/chat-input-field-view/chat-input-field.view';

export function NewChatView() {
  const {
    state: { userInstructions, repoIndexData },
  } = useAppStore();
  const [isCodingAgentEnabledByDefault, setIsCodingAgentEnabledByDefault] =
    useCodingAgentDefaultState();
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    isCurrentFileUsageEnabled: true,
    isRagSearchEnabled: repoIndexData?.isIndexingEnabled,
    isAgent: isCodingAgentEnabledByDefault,
  });

  useEffect(() => {
    ideApi.postMessage({
      type: 'chatSessionsRequired',
    });
  }, []);

  useEffect(() => {
    ideApi.postMessage({
      type: 'requireCustomInstructions',
    });
    setChatSettings((prevSettings) => ({
      ...prevSettings,
      isRagSearchEnabled: repoIndexData?.isIndexingEnabled,
    }));
  }, [repoIndexData?.isIndexingEnabled]);

  const handleSendMessage = useCallback(
    (message: UserMessage) => {
      setIsCodingAgentEnabledByDefault(message.chatSettings.isAgent);
      ideApi.postMessage({
        type: 'startNewChat',
        message: trimMessage(message),
      });
    },
    [setIsCodingAgentEnabledByDefault]
  );

  return (
    <div className={'flex flex-col h-full select-none overflow-hidden'}>
      <ChatHeader />
      <div className="flex flex-col flex-1 overflow-y-auto">
        <div
          className={'flex flex-col h-full bg-background justify-between'}
          role={'application'}
          aria-label={'New chat view'}
        >
          <div className={'flex flex-col justify-between h-full'}>
            <div className={'p-4'}>
              <LogoIcon className={'text-primary-background'} width={32} height={32} />
              <h1 className={'text-nowrap font-bricolage text-[26px] font-medium'}>Ask Zencoder</h1>
              <p>anything to help with your coding tasks or to learn something new</p>
            </div>
            <div>
              {!userInstructions?.length && (
                <div>
                  <h2 className={'mx-4 mt-2 mb-3 text-secondary-foreground'}>Tips</h2>
                  <div className={'flex flex-col gap-y-2 mx-4 pb-4'}>
                    <p>
                      Personalise Zencoder responses by adding custom{' '}
                      <Link to={`/${Routes.customInstructions}`}>AI instructions</Link>
                    </p>
                  </div>
                </div>
              )}
              <ChatTips />
            </div>
          </div>
        </div>
      </div>

      <ChatInputField
        className={'p-1 pt-0 sticky bottom-0'}
        onCommit={handleSendMessage}
        onSetChatSettings={setChatSettings}
        placeholder={<PlaceholderElement />}
        chatSettings={chatSettings}
        storageKey={'chat_history_draft'}
        isChatInProgress={false}
        aria-label="New chat input field"
        buttonCaption="Send"
        isFirstMessage
      />
    </div>
  );
}
