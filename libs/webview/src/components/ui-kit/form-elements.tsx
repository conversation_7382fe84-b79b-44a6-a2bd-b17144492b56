import {
  Input as BaseInput,
  Label as BaseLabel,
  Listbox as BaseListbox,
  Select as BaseSelect,
  Textarea as BaseTextarea,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from '@headlessui/react';
import CheckIcon from '@src/icons/check.svg';
import ChevronDown from '@src/icons/chevron-down.svg';
import { ComponentProps, forwardRef } from 'react';
import { twJoin, twMerge } from 'tailwind-merge';

export { Field } from '@headlessui/react';

export const Input = forwardRef<HTMLInputElement, ComponentProps<'input'>>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <BaseInput
      className={twMerge(
        'border',
        'bg-editor-background',
        'rounded-lg',
        'overflow-hidden',
        'focus:outline-none',
        'focus:ring',
        'active:ring',
        'px-3',
        'py-2',
        'placeholder-disabled-foreground',
        'data-[disabled]:opacity-65',
        'block',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const Label = forwardRef<HTMLLabelElement, ComponentProps<'label'>>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <BaseLabel className={twMerge('data-[disabled]:opacity-65', className)} {...rest} ref={ref} />
  );
});

export const Textarea = forwardRef<
  HTMLDivElement,
  {
    value?: string;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
    resizable?: boolean;
    rows?: number;
    maxLength?: number;
    onChange?: React.ChangeEventHandler<HTMLTextAreaElement>;
  }
>((props, ref) => {
  const { value, disabled, resizable = true, className, ...rest } = props;

  return (
    <div
      ref={ref}
      className={twMerge(
        'border',
        'bg-editor-background',
        'rounded-lg',
        'overflow-hidden',
        'focus-within:ring',
        'min-h-12',
        resizable && 'resize-y',
        disabled && 'data-[disabled]:opacity-65',
        className
      )}
    >
      <BaseTextarea
        value={value}
        disabled={disabled}
        className={twMerge(
          twJoin(
            'block',
            'overflow-auto',
            'bg-transparent',
            'border-0',
            'resize-none',
            'h-full',
            'w-full',
            'focus:outline-none',
            'px-3',
            'py-2',
            'disabled:text-disabled-foreground',
            'placeholder-disabled-foreground'
          )
        )}
        {...rest}
      />
    </div>
  );
});

export const Select = forwardRef<HTMLSelectElement, ComponentProps<'select'>>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <div className={twMerge(className, 'relative border-none px-0 py-0')}>
      <BaseSelect
        className={twMerge(
          'block appearance-none focus:outline-none focus:ring bg-transparent pe-6 rounded w-full',
          className
        )}
        {...rest}
        ref={ref}
      />
      <ChevronDown
        className="w-4 h-4 block absolute right-1 top-1/2 transform -translate-y-1/2 pointer-events-none"
        aria-hidden="true"
      />
    </div>
  );
});

interface ListBoxOption {
  title: string;
  description?: string;
  id: string;
}

export const ListBox = forwardRef<
  typeof BaseListbox,
  {
    className?: string;
    options: ListBoxOption[];
    value: ListBoxOption;
    disabled?: boolean;
    onChange: (value: ListBoxOption) => void;
  }
>(({ className, options, value, disabled, onChange }, ref) => {
  return (
    <BaseListbox
      disabled={disabled}
      // className={className}
      // ref={ref}
      value={value}
      onChange={onChange}
    >
      <ListboxButton
        className={twJoin(
          'relative',
          'block',
          'w-full',
          'rounded-lg',
          'border',
          'bg-editor-background',
          'focus:outline-none',
          'focus:ring',
          'active:ring',
          'px-3',
          'py-2',
          'data-[disabled]:opacity-65',
          'text-start'
        )}
      >
        {value.title}
        {value.description && (
          <span className="text-disabled-foreground"> {value.description}</span>
        )}
        <ChevronDown
          className="w-4 h-4 block absolute right-1 top-1/2 transform -translate-y-1/2 pointer-events-none"
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor="bottom"
        transition
        className="w-[var(--button-width)] mt-1 max-h-32 rounded-lg border p-1 focus:outline-none transition duration-100 ease-in data-leave:data-closed:opacity-0 bg-background shadow-menu"
      >
        {options.map((option) => (
          <ListboxOption
            key={option.id}
            value={option}
            className="group flex cursor-default items-center gap-2 justify-between rounded px-1 py-0.5 min-h-6 select-none hover:bg-hover-background focus:bg-hover-background"
          >
            <div className="text-base">
              <span>{option.title}</span>
              {option.description && (
                <span className="text-sm text-disabled-foreground"> {option.description}</span>
              )}
            </div>
            <CheckIcon className="invisible group-data-[headlessui-state~='selected']:visible" />
          </ListboxOption>
        ))}
      </ListboxOptions>
    </BaseListbox>
  );
});
