import React, { forwardRef } from 'react';

// Mock Input component
export const Input = forwardRef<HTMLInputElement, React.ComponentProps<'input'>>((props, ref) => {
  return <input {...props} ref={ref} />;
});

// Add other mocked components as needed
export const Select = forwardRef<HTMLSelectElement, React.ComponentProps<'select'>>(
  (props, ref) => {
    return <select {...props} ref={ref} />;
  }
);
