import { Combobox, ComboboxInput, ComboboxOption, ComboboxOptions } from '@headlessui/react';
import CheckIcon from '@src/icons/check.svg';
import ArrowIcon from '@src/icons/chevron-down.svg';
import CrossIcon from '@src/icons/close-icon.svg';
import { FunctionComponent, SVGProps, useMemo, useState } from 'react';
import { twJoin, twMerge } from 'tailwind-merge';

type Tag = {
  id: string;
  name: string;
  icon?: string;
  author?: string;
};

type Props<T extends Tag> = {
  showHeader: boolean;
  title?: string;
  tags: T[];
  managementButtonTitle: string;
  selectedTags: T[];
  onTagsEdit: (value: T[]) => void;
  constantTags?: T[];
  placeholder: string;
  readonly?: boolean;
  onManageClick?: () => void;
  compare?: (first: T, second: T) => number;
  defaultTagIcon?: FunctionComponent<SVGProps<SVGSVGElement>> | string;
};

export default function TagSelect<T extends Tag>(props: Readonly<Props<T>>) {
  const {
    selectedTags,
    onTagsEdit,
    managementButtonTitle,
    showHeader,
    defaultTagIcon: DefaultTagIcon,
  } = props;
  const [query, setQuery] = useState('');
  const removeTag = (id: string) => {
    onTagsEdit(selectedTags.filter((tag) => tag.id !== id));
  };

  const sortedTags = useMemo(() => {
    const filteredTags =
      query === ''
        ? props.tags
        : props.tags.filter((tag) => {
            return tag.name.toLowerCase().includes(query.toLowerCase());
          });
    return props.compare ? filteredTags.sort(props.compare) : filteredTags;
  }, [props.compare, props.tags, query]);

  const onUnselectAllClick = () => {
    const nameSet = new Set<string>();
    sortedTags.forEach((tag) => nameSet.add(tag.name));
    onTagsEdit(selectedTags.filter((p) => !nameSet.has(p.name)));
  };

  const onSelectAllClick = () => {
    onTagsEdit([...new Set([...selectedTags, ...sortedTags])]);
  };

  const hasSelectedTags =
    selectedTags.length > 0 &&
    sortedTags.some((tag) => {
      const selectedNames = selectedTags.map((t) => t.name);
      return selectedNames.includes(tag.name);
    });

  if (props.readonly) {
    return (
      <div
        className={twMerge(
          'bg-transparent',
          'flex',
          'flex-wrap',
          'items-start',
          'gap-1',
          'p-1',
          'w-full',
          'relative'
        )}
      >
        {' '}
        {props.selectedTags.map((tag) => (
          <div
            key={tag.id}
            className={twMerge(
              'bg-transparent',
              'text-secondary-foreground',
              'border',
              'rounded',
              'px-1.5',
              'py-1',
              'flex',
              'items-center',
              'gap-1'
            )}
          >
            <div className={'flex flex-row gap-2 items-center'}>
              <TagIcon icon={tag.icon} defaultIcon={DefaultTagIcon} />
              <span>{tag.name}</span>
            </div>
          </div>
        ))}
      </div>
    );
  } else {
    return (
      <Combobox
        value={selectedTags}
        onChange={(value) => {
          onTagsEdit(value);
          setQuery('');
        }}
        multiple={true}
        immediate={true}
      >
        <div
          className={twMerge(
            'rounded-lg',
            'border',
            'bg-editor-background',
            'flex',
            'flex-wrap',
            'items-start',
            'gap-1',
            'p-1',
            'w-full',
            'relative'
          )}
        >
          {props.constantTags?.map((tag) => (
            <div
              key={tag.id}
              className={twMerge(
                'bg-transparent',
                'text-secondary-foreground',
                'border',
                'rounded',
                'px-1.5',
                'py-1',
                'flex',
                'items-center',
                'gap-1'
              )}
            >
              <div className={'flex flex-row gap-2 items-center'}>
                <TagIcon icon={tag.icon} defaultIcon={DefaultTagIcon} />
                <span>{tag.name}</span>
              </div>
            </div>
          ))}
          {selectedTags.map((tag) => (
            <div
              key={tag.id}
              className={twMerge(
                'bg-secondary-button-background',
                'text-foreground',
                'rounded',
                'pl-1.5',
                'pr-1',
                'py-1',
                'flex',
                'items-center',
                'gap-1'
              )}
            >
              <div className={'flex flex-row gap-2 items-center'}>
                <TagIcon icon={tag.icon} defaultIcon={DefaultTagIcon} />
                <span>{tag.name}</span>
              </div>
              <CrossIcon
                onClick={() => removeTag(tag.id)}
                className={'cursor-pointer flex-shrink-0'}
              />
            </div>
          ))}
          <div className="min-w-[50%] grow basis-0">
            <ComboboxInput
              placeholder={props.placeholder}
              value={query}
              className={twMerge(
                'bg-transparent',
                'overflow-hidden',
                'focus:outline-none',
                'px-2',
                'py-1',
                'placeholder-disabled-foreground',
                'data-[disabled]:opacity-65',
                'w-full',
                'block'
              )}
              onChange={(e) => setQuery(e.target.value)}
            />
          </div>
        </div>
        <ComboboxOptions
          className={twJoin(
            'block',
            'focus:outline-none',
            'bg-editor-background',
            'rounded-lg',
            'border',
            'empty:invisible',
            'w-full',
            'mt-1',
            'max-h-[20rem]',
            'overflow-y-auto'
          )}
        >
          <div>
            {showHeader && (
              <div
                className={twJoin(
                  'w-full',
                  'flex',
                  'justify-between',
                  'items-center',
                  'h-6',
                  'text-secondary-foreground',
                  'bg-editor-background',
                  'text-xs',
                  'font-bold',
                  'p-2',
                  'sticky',
                  'top-0',
                  'left-0'
                )}
              >
                <span>{props.title ?? 'Items'}</span>
                {!hasSelectedTags && (
                  <span onClick={onSelectAllClick} className={'cursor-pointer'}>
                    Select all
                  </span>
                )}
                {hasSelectedTags && (
                  <span onClick={onUnselectAllClick} className={'cursor-pointer'}>
                    Unselect all
                  </span>
                )}
              </div>
            )}
            {sortedTags.map((tag) => {
              return (
                <ComboboxOption
                  key={tag.id}
                  value={tag}
                  className={twJoin(
                    'group',
                    'data-[focus]:bg-secondary-button-background',
                    'text-foreground',
                    'px-2',
                    'rounded-lg',
                    'cursor-pointer'
                  )}
                >
                  <div className={'w-full flex justify-between items-center px-1 py-1'}>
                    <div className={'flex flex-row gap-2 items-center'}>
                      <TagIcon icon={tag.icon} defaultIcon={DefaultTagIcon} />
                      <span>{tag.name}</span>
                    </div>
                    <CheckIcon className={'group-aria-selected:visible invisible flex-shrink-0'} />
                  </div>
                </ComboboxOption>
              );
            })}
            {props.onManageClick && (
              <div
                className={twJoin(
                  'w-full',
                  'flex',
                  'justify-between',
                  'items-center',
                  'py-1',
                  'text-secondary-foreground',
                  'px-3',
                  'pb-1',
                  'cursor-pointer',
                  'rounded-lg',
                  'focus:bg-secondary-button-background',
                  'hover:bg-secondary-button-background'
                )}
                role={'button'}
                onClick={props.onManageClick}
              >
                <span>{managementButtonTitle}</span>
                <ArrowIcon className={'-rotate-90'} />
              </div>
            )}
          </div>
        </ComboboxOptions>
      </Combobox>
    );
  }
}

function TagIcon({
  icon,
  defaultIcon: DefaultIcon,
}: {
  icon: string | undefined;
  defaultIcon: FunctionComponent<SVGProps<SVGSVGElement>> | string | undefined;
}) {
  return icon ? (
    <div className={'flex items-center justify-center w-4 h-4 flex-shrink-0'}>
      <img src={`data:image/png;base64, ${icon}`} alt="" />
    </div>
  ) : (
    DefaultIcon &&
      (typeof DefaultIcon === 'string' ? (
        <div className={'flex items-center justify-center w-4 h-4 flex-shrink-0'}>
          <img src={`data:image/png;base64, ${icon}`} alt="" />
        </div>
      ) : (
        <DefaultIcon className={'w-4 h-4 text-secondary-foreground flex-shrink-0'} />
      ))
  );
}
