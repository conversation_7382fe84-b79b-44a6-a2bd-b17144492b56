import { Tab as BaseTab, <PERSON>bList as BaseTabList, TabListProps } from '@headlessui/react';
import { ComponentProps, forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';

export { TabGroup, TabPanel, TabPanels } from '@headlessui/react';

export interface TabProps extends ComponentProps<'button'> {
  alignRight?: boolean;
}

export const Tab = forwardRef<HTMLButtonElement, TabProps>((props, ref) => {
  const { className, alignRight, ...rest } = props;
  return (
    <BaseTab
      className={twMerge(
        'py-2',
        'mr-4',
        'ml-0',
        'text-base',
        'font-semibold',
        'relative',
        'transition-colors',
        'cursor-pointer',
        'text-secondary-foreground',
        'hover:text-primary-foreground',
        'data-[selected]:text-foreground',
        'data-[hover]:text-foreground',
        'focus:outline-none',
        'after:absolute',
        'after:bottom-[-1px]',
        'after:left-0',
        'after:w-full',
        'after:h-[1px]',
        'after:bg-transparent',
        'data-[selected]:after:bg-primary-background',
        'after:z-10',
        alignRight && 'ml-auto',
        'last:mr-0', // Remove right margin for the last tab
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const TabList = forwardRef<HTMLDivElement, TabListProps & { className?: string }>(
  (props, ref) => {
    const { className, ...rest } = props;
    return (
      <BaseTabList
        className={twMerge(
          'w-full',
          'flex',
          'flex-row',
          'border-b',
          'border-editor-widget-border',
          className
        )}
        {...rest}
        ref={ref}
      />
    );
  }
);
