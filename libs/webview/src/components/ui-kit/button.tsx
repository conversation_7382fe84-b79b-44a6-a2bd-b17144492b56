import { Button as BaseB<PERSON>on, <PERSON>u, <PERSON>u<PERSON>utton, MenuItem, MenuItems } from '@headlessui/react';
import { ComponentProps, forwardRef, ReactNode } from 'react';
import { twMerge } from 'tailwind-merge';
import { Icon } from './icon';

type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends ComponentProps<'button'> {
  size?: ButtonSize;
  round?: boolean;
}

const sizeClasses: Record<ButtonSize, string[]> = {
  sm: ['text-sm', 'p-0', 'leading-4'],
  md: ['text-sm', 'font-semibold', 'leading-4', 'py-1', 'px-2'],
  lg: ['text-[14px]', 'font-semibold', 'leading-5', 'py-3', 'px-6'],
};

const Button = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, size = 'md', round = false, ...rest } = props;
  return (
    <BaseButton
      className={twMerge(
        sizeClasses[size],
        round ? 'rounded-full' : 'rounded',
        'select-none',
        'data-[disabled]:opacity-100',
        'focus:outline-none',
        'focus:ring',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const PrimaryButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-primary-foreground',
        'bg-primary-background',
        'data-[hover]:bg-primary-hover',
        'data-[hover]:data-[active]:bg-primary-active',
        'data-[disabled]:bg-transparent',
        'data-[disabled]:text-disabled-foreground',
        'data-[disabled]:shadow-disabled-box',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const SecondaryButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-secondary-button-foreground',
        'bg-secondary-button-background',
        'data-[hover]:bg-secondary-button-hover',
        'data-[hover]:data-[active]:bg-secondary-button-active',
        'data-[disabled]:bg-transparent',
        'data-[disabled]:text-disabled-foreground',
        'data-[disabled]:shadow-disabled-box',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const LinkButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-foreground',
        'bg-transparent',
        'data-[hover]:bg-hover-background',
        'data-[active]:bg-hover-background',
        'data-[disabled]:text-disabled-foreground',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const DestructiveButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-destructive-foreground',
        'bg-destructive-background',
        'data-[hover]:bg-destructive-hover',
        'data-[hover]:data-[active]:bg-destructive-active',
        'data-[disabled]:bg-transparent',
        'data-[disabled]:text-disabled-foreground',
        'data-[disabled]:shadow-disabled-box',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

interface SplitButtonProps extends ButtonProps {
  menuItems: Array<{
    label: ReactNode;
    onClick: () => void;
  }>;
  buttonClassName?: string;
  menuButtonClassName?: string;
}

export const SplitButton = forwardRef<HTMLButtonElement, SplitButtonProps>((props, ref) => {
  const {
    className,
    buttonClassName,
    menuButtonClassName,
    menuItems: items,
    size,
    ...restProps
  } = props;

  return (
    <div className={twMerge('flex gap-px', className)}>
      <PrimaryButton
        ref={ref}
        className={twMerge('rounded-r-none focus:z-10', buttonClassName)}
        size={size}
        {...restProps}
      />
      <Menu>
        <MenuButton
          className={twMerge('group rounded-l-none', menuButtonClassName)}
          as={PrimaryButton}
          size={size}
        >
          <Icon className="group-data-[open]:rotate-180" type="triangle-down" size={16} />
        </MenuButton>
        <MenuItems
          anchor="bottom start"
          className="bg-background flex flex-col gap-0.5 rounded border p-1 [--anchor-gap:4px] focus:outline-none focus:ring"
        >
          {items.map(({ label, onClick }, idx) => (
            <MenuItem key={idx}>
              <LinkButton className="px-1 py-1.5 text-left" onClick={onClick}>
                {label}
              </LinkButton>
            </MenuItem>
          ))}
        </MenuItems>
      </Menu>
    </div>
  );
});
