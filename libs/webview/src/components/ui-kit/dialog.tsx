import {
  DialogTitle as BaseDialogTitle,
  CloseButton,
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogProps,
} from '@headlessui/react';
import { Icon } from '@src/components/ui-kit/icon';
import { PropsWithChildren } from 'react';

export { Dialog, DialogPanel } from '@headlessui/react';

export function DialogTitle({ children }: PropsWithChildren<{}>) {
  return <BaseDialogTitle className={'mt-0 mb-2'}>{children}</BaseDialogTitle>;
}

export type ModalDialogProps = DialogProps &
  PropsWithChildren & {
    showCloseButton?: boolean;
  };

export function ModalDialog(props: ModalDialogProps) {
  const { children, showCloseButton = true, ...rest } = props;

  return (
    <Dialog {...rest}>
      {/* The backdrop, rendered as a fixed sibling to the panel container */}
      <DialogBackdrop className="fixed inset-0 bg-modal-dialog-backdrop" />

      {/* Full-screen container to center the panel */}
      <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
        {/* The actual dialog panel  */}
        <DialogPanel className="relative max-w-lg min-w-40 bg-background border rounded-lg p-4">
          {showCloseButton && (
            <CloseButton className={'absolute top-2 right-2'} onClick={() => rest.onClose?.(false)}>
              <Icon type={'close'} />
            </CloseButton>
          )}
          {children}
        </DialogPanel>
      </div>
    </Dialog>
  );
}
