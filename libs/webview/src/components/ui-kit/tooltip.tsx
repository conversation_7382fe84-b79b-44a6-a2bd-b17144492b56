import { useMemo } from 'react';
import { Tooltip as BaseTooltip } from 'react-tooltip';
import styled from 'styled-components';
import { vscEditorBackground, vscEditorForeground, vscInputBackground } from '../constants';

export const Tooltip = styled(BaseTooltip).attrs({
  className: 'zencoder-tooltip',
})`
  &.zencoder-tooltip {
    font-size: 12px;
    background-color: ${vscInputBackground};
    box-shadow: 0 2px 16px 0 ${vscEditorBackground};
    color: ${vscEditorForeground};
    padding: 4px;
    z-index: 1000;
    max-width: 80vw;
  }
`;

export function useTooltipId() {
  return useMemo(() => `tooltip-${Math.random().toString().substring(2)}`, []);
}
