import { DialogTitle, ModalDialog } from '@src/components/ui-kit/dialog';
import { ComponentProps, PropsWithChildren, useState } from 'react';
import { DestructiveButton, LinkButton, PrimaryButton, SecondaryButton } from './button';
import { Checkbox } from './checkbox';
import { Field, Input, Label, Textarea } from './form-elements';
import { ProgressBar } from './progress-bar';
import { Switch } from './switch';
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from './tabs';
import { Tooltip, useTooltipId } from './tooltip';

function DemoBlock(props: PropsWithChildren<ComponentProps<'div'>> & { title: string }) {
  const { children, title, ...rest } = props;
  return (
    <div className={'p-3 mb-5'} {...rest}>
      <h2>{title}</h2>
      {children}
    </div>
  );
}

export function UiKitDemo() {
  return (
    <TabGroup>
      <TabList>
        <Tab>Components</Tab>
        <Tab>Typography</Tab>
      </TabList>
      <TabPanels>
        <TabPanel>
          <ComponentsDemo />
        </TabPanel>
        <TabPanel>
          <TypographyDemo />
        </TabPanel>
      </TabPanels>
    </TabGroup>
  );
}

function ComponentsDemo() {
  const tooltipId = useTooltipId();

  return (
    <div>
      <DemoBlock title={'Buttons'}>
        <div className={'py-1'}>
          <SecondaryButton size={'sm'} className={'mr-1'}>
            Small
          </SecondaryButton>
          <SecondaryButton className={'mr-1'}>Normal</SecondaryButton>
          <SecondaryButton size={'lg'} className={'mr-1'}>
            Big
          </SecondaryButton>
        </div>
        {/*<div className={'py-1'}>*/}
        {/*  <Button round={true}>Round button</Button>*/}
        {/*</div>*/}
        <div className={'py-1'}>
          <PrimaryButton className={'mr-1'}>Primary</PrimaryButton>
          <PrimaryButton disabled={true}>Disabled</PrimaryButton>
        </div>
        <div className={'py-1'}>
          <SecondaryButton className={'mr-1'}>Secondary</SecondaryButton>
          <SecondaryButton disabled={true}>Disabled</SecondaryButton>
        </div>
        <div className={'py-1'}>
          <LinkButton className={'mr-1'}>Link button</LinkButton>
          <LinkButton disabled={true}>Disabled</LinkButton>
        </div>
        <div className={'py-1'}>
          <DestructiveButton className={'mr-1'}>Destructive button</DestructiveButton>
          <DestructiveButton disabled={true}>Disabled</DestructiveButton>
        </div>
      </DemoBlock>
      <DemoBlock title={'Tabs'}>
        <TabGroup>
          <TabList>
            <Tab>Tab 1</Tab>
            <Tab>Tab 2</Tab>
            <Tab>Tab 3</Tab>
          </TabList>
          <TabPanels>
            <TabPanel>Content 1</TabPanel>
            <TabPanel>Content 2</TabPanel>
            <TabPanel>Content 3</TabPanel>
          </TabPanels>
        </TabGroup>
      </DemoBlock>
      <DemoBlock title={'Tooltip'}>
        <SecondaryButton data-tooltip-id={tooltipId}>Hover me</SecondaryButton>
        <Tooltip id={tooltipId}>
          Some multi-
          <br />
          line text
        </Tooltip>
      </DemoBlock>
      <DemoBlock title={'Inputs'}>
        <Field>
          <Label className="data-[disabled]:opacity-50">Normal text input</Label>
          <Input type={'text'} placeholder={'Some placeholder'} />
        </Field>
        <Field disabled>
          <Label>Disabled field</Label>
          <Input type={'text'} />
        </Field>
        <Field>
          <Label>Textarea</Label>
          <Textarea />
        </Field>
        <Field>
          <Checkbox />
          <Label>Some checkbox</Label>
        </Field>
        <Field>
          <Checkbox disabled={true} />
          <Label>Disabled</Label>
        </Field>
        <Field>
          <Checkbox disabled={true} checked={true} />
          <Label>Disabled and checked</Label>
        </Field>
        <Field>
          <Switch />
          <Label>Some switch</Label>
        </Field>
        <Field>
          <Switch disabled={true} />
          <Label>Disabled</Label>
        </Field>
        <Field>
          <Switch disabled={true} checked={true} />
          <Label>Disabled and checked</Label>
        </Field>
      </DemoBlock>
      <DemoBlock title={'Progress bar'}>
        <ProgressBar label={'Default'} />
        <ProgressBar value={30} label={'Filled to 30%'} />
        <ProgressBar value={100}>
          <label>Full with custom children</label>
        </ProgressBar>
        <ProgressBar value={40} dotsCount={35} label={'Custom size'} />
      </DemoBlock>
      <DemoBlock title={'Dialogs'}>
        <DialogsExample />
      </DemoBlock>
    </div>
  );
}

function DialogsExample() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <PrimaryButton onClick={() => setIsOpen(true)}>Show dialog</PrimaryButton>
      <ModalDialog open={isOpen} onClose={() => setIsOpen(false)}>
        <DialogTitle>Title</DialogTitle>
        This is a content of the dialog.
      </ModalDialog>
    </>
  );
}

function TypographyDemo() {
  return (
    <div>
      <DemoBlock title={'Font Sizes'}>
        <p className="text-xs mb-2">text-xs: Extra Small Text</p>
        <p className="text-sm mb-2">text-sm: Small Text</p>
        <p className="text-base mb-2">text-base: Base Text Size</p>
        <p className="text-lg mb-2">text-lg: Large Text</p>
        <p className="text-[14px] mb-2">text-[14px]: Custom Size Text</p>
      </DemoBlock>

      <DemoBlock title={'Font Weights'}>
        <p className="font-normal mb-2">font-normal: Normal weight text</p>
        <p className="font-medium mb-2">font-medium: Medium weight text</p>
        <p className="font-semibold mb-2">font-semibold: Semi-bold text</p>
        <p className="font-bold mb-2">font-bold: Bold text</p>
      </DemoBlock>

      <DemoBlock title={'Text Colors'}>
        <p className="text-foreground mb-2">text-foreground: Default text color</p>
        <p className="text-primary-foreground mb-2">text-primary-foreground: Primary text color</p>
        <p className="text-secondary-foreground mb-2">
          text-secondary-foreground: Secondary text color
        </p>
        <p className="text-disabled-foreground mb-2">
          text-disabled-foreground: Disabled text color
        </p>
        <p className="text-warning-foreground mb-2">text-warning-foreground: Warning text color</p>
        <p className="text-error-foreground mb-2">text-error-foreground: Error text color</p>
        <p className="text-link-foreground mb-2">text-link-foreground: Link text color</p>
        <p className="text-editor-foreground mb-2">text-editor-foreground: Editor text color</p>
      </DemoBlock>

      <DemoBlock title={'Text Alignment'}>
        <p className="text-left mb-2">text-left: Left aligned text</p>
        <p className="text-center mb-2">text-center: Center aligned text</p>
        <p className="text-right mb-2">text-right: Right aligned text</p>
        <p className="text-start mb-2">text-start: Start aligned text (same as left in LTR)</p>
      </DemoBlock>

      <DemoBlock title={'Text Decoration & Transformation'}>
        <p className="underline mb-2">underline: Underlined text</p>
        <p className="underline underline-offset-4 decoration-2 mb-2">
          underline underline-offset-4 decoration-2: Styled underline
        </p>
        <p className="line-through mb-2">line-through: Strikethrough text</p>
        <p className="uppercase mb-2">uppercase: UPPERCASE TEXT</p>
        <p className="lowercase mb-2">lowercase: lowercase text</p>
        <p className="capitalize mb-2">capitalize: Capitalized Text</p>
      </DemoBlock>

      <DemoBlock title={'Line Height'}>
        <p className="leading-4 mb-2">leading-4: Tight line height</p>
        <p className="leading-5 mb-2">leading-5: Medium line height</p>
        <p className="leading-6 mb-2">leading-6: Relaxed line height</p>
      </DemoBlock>

      <DemoBlock title={'Text Overflow'}>
        <div className="w-48 mb-2">
          <p className="truncate">
            truncate: This text is too long and will be truncated with an ellipsis
          </p>
        </div>
        <div className="w-48 mb-2">
          <p className="text-ellipsis overflow-hidden">
            text-ellipsis overflow-hidden: Text with ellipsis when overflowing
          </p>
        </div>
        <div className="w-48 mb-2">
          <p className="text-nowrap overflow-hidden">
            text-nowrap overflow-hidden: Text that doesn't wrap to next line
          </p>
        </div>
      </DemoBlock>

      <DemoBlock title={'Font Families'}>
        <p className="font-sans mb-2">font-sans: Sans-serif font family</p>
        <p className="font-editor mb-2">font-editor: Editor font family (from variables)</p>
      </DemoBlock>

      <DemoBlock title={'Combined Styles'}>
        <p className="text-sm font-semibold text-primary-foreground mb-2">
          text-sm font-semibold text-primary-foreground: Small, semi-bold primary text
        </p>
        <p className="text-base font-bold text-disabled-foreground mb-2">
          text-base font-bold text-disabled-foreground: Base size, bold, disabled text
        </p>
        <p className="text-lg font-medium leading-6 mb-2">
          text-lg font-medium leading-6: Large, medium weight text with relaxed line height
        </p>
        <p className="text-xs text-disabled-foreground font-bold mb-2">
          text-xs text-disabled-foreground font-bold: Extra small, bold, disabled text
        </p>
      </DemoBlock>
    </div>
  );
}
