import { MentionItem } from '@src/components/chat-view/chat-input-field-view/message-editor/mention/types';
import { Agent } from '@src/models/custom-agents.model';
import { JSONContent } from '@tiptap/core';
import type { UiComponent } from '../components/chat-view/chat-message-view/message-components/registry';

export type ChatId = string;
export type ChatMessageRole = 'user' | 'assistant' | 'system';

export interface ChatAttachedFilePath {
  /**
   * BE- and Repo-indexing-friendly path. From the root of workspace.
   * Includes a prefix for workspace root folder - required in multi-root workspaces.
   */
  path: string;
  /**
   * User-friendly path. Relative to (some) workspace root folder.
   *
   * For backwards compatibility with old stored chats, this field may be missing.
   */
  displayPath?: string;
  /**
   * Absolute path on disk.
   */
  fsPath: string;
  /**
   * TODO: description
   */
  sources?: string[];
}

export interface ChatAttachedFile extends ChatAttachedFilePath {
  content: string;
  language?: string;
}

export interface ChatSearchFile {
  fsPath: string;
  displayPath?: string;
  path: string;
}

export interface RollbackFilePath extends ChatAttachedFilePath {
  isToRemove: boolean;
  isMissingSnapshot: boolean;
  snapshotId?: string;
}

export interface RollbackInfo {
  timestamp: number;
  files: RollbackFilePath[];
}

export interface ChatWarning {
  text: string;
  actions?: Array<{ title: string; type: 'externalAction'; action: string }>;
}

export interface LoadingMarkerSteps {
  status: 'idle' | 'active' | 'finished';
  title: string;
}

export interface LoadingMarkerPart {
  type: 'loadingMarker';
  steps?: LoadingMarkerSteps[];
}

export interface TextMessagePart {
  type: 'text';
  text: string;
}

export interface JiraIssueMessagePart {
  type: 'jiraIssue';
  key: string;
  summary: string;
  description: string;
}

export interface FileMentionMessagePart {
  type: 'fileMention';
  fileName: string;
  path: string;
  fsPath: string;
}

export interface SlashCommandMessagePart {
  type: 'command';
  commandName: string;
  customAgentId?: string;
}

export interface ErrorTextMessagePart {
  type: 'errorText';
  text: string;
  title?: string;
  /**
   * @deprecated
   * use actions
   */
  allowRegenerate?: boolean;
  actions?: Array<
    { title: string } & (
      | { type: 'externalAction'; action: string }
      | { type: 'openModelsSettings' }
      | { type: 'upgradePlan'; waitSeconds?: number }
    )
  >;
}

export interface WarningTextMessagePart {
  type: 'warningText';
  text: string;
  title: string;
  actions: Array<{ title: string; action: string }>;
}

export interface CodeSnippetMessagePart {
  type: 'codeSnippet';
  language?: string;
  text: string;
}

export interface MemoryMessagePart {
  type: 'memoryCreated';
  memoryItems: ChatMemoryItem[];
}

export interface UiComponentMessagePart {
  type: 'uiComponent';
  component: UiComponent;
}

export interface ClientDiff {
  path: string;
  patch: string;
  isNew: boolean;
  isApplied?: boolean;
  isUnread?: boolean;
}

export interface CodePatchMessagePart {
  type: 'codePatch';
  diffs: ClientDiff[];
}

export interface CodeHunkMessagePart {
  type: 'codeHunk';
  lineBasedDiff: string;
  refToolCallId?: string;
}

export interface LoadingMessagePart {
  type: 'loadingPart';
  text?: string;
}

export interface ApplyActionMessagePart {
  type: 'applyMessage';
  messageId: string;
}

export interface ToolCallMessagePart {
  type: 'toolCall';
  toolCallId: string;
  title?: string;
  statusText?: string;
  status: 'IN_PROGRESS' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'UNRECOGNIZED';
  context?: {
    files?: ChatAttachedFilePath[];
  };
  isHidden: boolean;
}

export interface GroupedChecklistMessagePart {
  type: 'groupedChecklist';
  disabled: boolean;
  id: string;
  items: Array<{
    group: string;
    text: string;
    isSelected: boolean;
  }>;
  newItemButtonText: string;
  newItemGroupName: string;
  submitButtonText: string;
  title: string;
  newItemPlaceholder?: string;
}

export interface CommandInfo {
  name: string;
  arguments: string[];
}

export interface ShellCommandMessagePart {
  type: 'shellCommand';
  id: string;
  command: string;
  commandsRequiringConfirmation: CommandInfo[];
  status: 'CONFIRMATION' | 'IN_PROGRESS' | 'SUCCESS' | 'ERROR' | 'CANCELLED' | 'UNRECOGNIZED';
  outputPath?: string;
  shell?: string;
  terminalName?: string;
}

export type MessageContent = (
  | LoadingMarkerPart
  | TextMessagePart
  | SlashCommandMessagePart
  | JiraIssueMessagePart
  | FileMentionMessagePart
  | ErrorTextMessagePart
  | CodeSnippetMessagePart
  | MemoryMessagePart
  | UiComponentMessagePart
  | CodePatchMessagePart
  | CodeHunkMessagePart
  | LoadingMessagePart
  | ApplyActionMessagePart
  | ToolCallMessagePart
  | GroupedChecklistMessagePart
  | ShellCommandMessagePart
  | WarningTextMessagePart
)[];

export interface ChatMessageContext {
  readonly attachedFiles?: (ChatAttachedFile | ChatSearchFile)[];
  readonly currentFile?: ChatAttachedFile | ChatSearchFile;
  readonly codebaseEnabled?: boolean;
  readonly rerankedFiles?: ChatAttachedFile[];
  readonly usedFilePaths?: ChatAttachedFilePath[];
  readonly author?: string;
  readonly operationId?: string;
  readonly mentions?: MentionItem[];
  /**
   * Data to perform a rollback of this message and all next messages.
   */
  readonly rollbackInfo?: RollbackInfo;
  /**
   * Data to revert applied diffs from this message
   */
  readonly revertAppliedDiffsInfo?: RollbackInfo;
}

export interface ChatMessage {
  readonly id: string;
  readonly role: ChatMessageRole;
  readonly content: MessageContent;
  readonly rawContent?: JSONContent;
  readonly context?: ChatMessageContext;
  readonly createdAt: number;
  readonly isGhost?: boolean;
  readonly isHidden?: boolean;
}

export interface ChatModel {
  readonly id: ChatId;
  readonly messages: ChatMessage[];
  readonly isStreaming?: boolean;
  readonly createdAt: number;
  readonly updatedAt: number;
  readonly title: string;
  readonly isRagSearchEnabled?: boolean;
  readonly isCurrentFileUsageEnabled?: boolean;
  readonly isAgent?: boolean;
  readonly isCustomAgent?: boolean;
  readonly customAgent?: Agent;
  readonly isUnitTestsAgent?: boolean;
  readonly isE2EAgent?: boolean;
  readonly autoApply?: boolean;
  readonly isLongChatWarningDismissed?: boolean;
  readonly hasClientOperations?: boolean;
  readonly warning?: ChatWarning;
}

export interface ChatSession extends Pick<ChatModel, 'id' | 'title' | 'updatedAt' | 'isAgent'> {}

export type MemoryItemId = string;

export interface ChatMemoryItem {
  id: MemoryItemId;
  text: string;
  createdAt: number;
}

export interface ChatSettings
  extends Pick<
    ChatModel,
    | 'isRagSearchEnabled'
    | 'isCurrentFileUsageEnabled'
    | 'isAgent'
    | 'isCustomAgent'
    | 'customAgent'
    | 'isUnitTestsAgent'
    | 'isE2EAgent'
    | 'autoApply'
  > {}

export function isAgenticChat(settings: ChatSettings): boolean {
  return (
    !!settings.isAgent ||
    !!settings.isCustomAgent ||
    !!settings.isUnitTestsAgent ||
    !!settings.isE2EAgent
  );
}
