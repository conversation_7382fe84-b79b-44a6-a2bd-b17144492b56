import * as Sentry from '@sentry/react';

type Permission = 'zencoder.agents.share';
type Role = 'Admin' | 'Manager' | 'member';

export type WebviewSentryOptions = Sentry.BrowserOptions;
export interface WebviewAnalyticsOptions {
  writeKey: string;
  dataPlaneURL: string;
  anonymousId: string;
  userId?: string;
  isEnabled: boolean;
}

type KeybindingCommand = 'zencoder.insert-into-chat';

export interface Keybinding {
  command: KeybindingCommand;
  key: string;
  mac: string;
  isUpdated: boolean;
}

export interface Settings {
  isAuthenticated: boolean;
  userId: string | null;
  userName?: string;
  sentryOptions?: object;
  showChatByDefault: boolean;
  areChatSessionsPersisted: boolean;
  isMacOs: boolean;
  isWindows: boolean;
  isZencoderDevMode: boolean;
  ideType: 'jetbrains' | 'vscode';
  displayDebugInfo: boolean;
  zencoderPermissions?: Permission[];
  zencoderRoles?: Role[];
  urlSettings?: {
    cloudStorageEndpoint: string;
  };
}
