export enum Routes {
  startScreen = 'start-screen',
  customAgents = 'custom-agents',
  editCustomAgent = 'edit-custom-agent',
  viewLibraryAgent = 'view-library-agent',
  signIn = 'signIn',
  repoIndexSettings = 'repo-index-settings',
  customInstructions = 'custom-instructions',
  memoriesSettings = 'memories-settings',
  chatHistory = 'chat-history',
  newChat = 'new-chat',
  chat = 'chat',
  contextViewer = 'context-viewer',
  zenLoader = 'zen-loader',
  unitTestsViewer = 'unit-tests-viewer',
  uiKitDemo = 'ui-kit-demo',
  modelsSettings = 'models-settings',
  integrations = 'integrations',
  agentTools = 'agent-tools',
  agentToolEdit = 'agent-tool-edit',
}

export const ANALYTICS_SCREEN_NAMES: Partial<Record<Routes, string>> = {
  [Routes.customAgents]: 'Custom Agents',
  [Routes.signIn]: 'Sign In',
  [Routes.repoIndexSettings]: 'Repository Index Settings',
  [Routes.customInstructions]: 'Custom Instructions',
  [Routes.memoriesSettings]: 'Memories Settings',
  [Routes.chatHistory]: 'Chat History',
  [Routes.newChat]: 'New Chat',
  [Routes.chat]: 'Chat View',
  [Routes.unitTestsViewer]: 'Unit Tests Screen',
  [Routes.modelsSettings]: 'Models Settings',
  [Routes.integrations]: 'Integrations',
  [Routes.editCustomAgent]: 'Custom Agent Editing',
  [Routes.viewLibraryAgent]: 'Library Agent View',
  [Routes.agentTools]: 'Agent tools',
  [Routes.agentToolEdit]: 'Agent Tool Edit',
};

export const initialPaths = [Routes.startScreen, Routes.signIn, Routes.chatHistory].map(
  (r) => `/${r}`
);
