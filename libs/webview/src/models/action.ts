import { BannerState } from '@src/models/banner.model';
import { Integrations } from '@src/models/integrations.model';
import { JiraIssue } from '@src/models/jira.model';
import { ModelsSettings } from '@src/models/models-settings.model';
import { RepoIndexData } from '@src/models/repo-index-data.model';
import { AppState } from './app-state.model';
import {
  ChatAttachedFile,
  ChatMemoryItem,
  ChatModel,
  ChatSession,
  ChatWarning,
} from './chat.model';
import { Agent } from './custom-agents.model';
import { McpServerInfo } from './mcp-servers.model';
import { ProgressDetails } from './progress.model';
import { Keybinding, Settings } from './settings.model';
import { ShellToolConfig } from './shell-tool-config.model';

export type Action =
  | SetStateAction
  | SetSettingsAction
  | ShowUnitTestsProgressAction
  | InitGenerateUnitsTestAction
  | SetCommandContextAction
  | ResetStateAction
  | SetActiveChatAction
  | SetKeybindingAction
  | SetChatSessionsAction
  | SetClearChatsDialogShown
  | SetCustomAgentsAction
  | SetRepoIndexDataAction
  | SetModelsSettingsAction
  | SetCustomInstructionsAction
  | SetChatMemoriesAction
  | SetActiveFile
  | SetIntegrations
  | SetJiraIssuesSearchResultAction
  | SetIsLatestVersionInstalled
  | SetBannerState
  | SetFileSelectForMention
  | SetShellToolConfigAction
  | SetFeatureFlagsVariants
  | SetAgentToolsAction;

export interface SetStateAction {
  type: 'set-state';
  state: AppState;
}

export interface SetSettingsAction {
  type: 'set-settings';
  settings: Settings;
}

export interface ResetStateAction {
  type: 'reset-state';
}

export interface ShowUnitTestsProgressAction {
  type: 'show-unit-tests-progress';
  actionId: string;
  symbolName: string;
  progressDetails: ProgressDetails;
}

export interface InitGenerateUnitsTestAction {
  type: 'init-generate-unit-tests';
  actionId: string;
  symbolName: string;
  bulletPoints: string[];
}

export interface SetCommandContextAction {
  type: 'set-command-context';
  context: Record<string, unknown>;
  symbolName: string;
}

export interface SetKeybindingAction {
  type: 'set-keybinding';
  keybinding: Keybinding;
}

export interface SetSuggestedDocstringsAction {
  type: 'set-suggested-docstrings';
  actionId: string;
  operationId: string;
  suggestions: string[];
  symbolName: string;
}

export interface SetActiveChatAction {
  type: 'set-active-chat';
  chat: ChatModel;
  isStreaming: boolean;
  hasClientOperations?: boolean;
  overrideCurrent?: boolean;
  warning?: ChatWarning;
}

export interface SetChatSessionsAction {
  type: 'set-chat-sessions';
  chatSessions: ChatSession[];
}

export interface SetClearChatsDialogShown {
  type: 'set-clear-chats-dialog-shown';
  isShown: boolean;
}

export interface SetCustomAgentsAction {
  type: 'set-custom-agents';
  customAgents: Agent[];
}

export interface SetRepoIndexDataAction {
  type: 'set-repo-index-data';
  repoIndexData: RepoIndexData;
}

export interface SetModelsSettingsAction {
  type: 'set-models-settings';
  settings: ModelsSettings;
}

export interface SetCustomInstructionsAction {
  type: 'set-custom-instructions';
  userInstructions?: string;
}

export interface SetJiraIssuesSearchResultAction {
  type: 'set-jira-issues-search-result';
  searchResult:
    | {
        query: string;
        issues: JiraIssue[];
      }
    | undefined;
}

export interface SetChatMemoriesAction {
  type: 'set-chat-memories';
  areEnabled?: boolean;
  items?: ChatMemoryItem[];
}

export interface SetActiveFile {
  type: 'set-active-file';
  activeFile?: ChatAttachedFile;
}

export interface SetIntegrations {
  type: 'set-integrations';
  integrations?: Integrations;
}

export interface SetIsLatestVersionInstalled {
  type: 'set-latest-version-installed';
  value: boolean;
}

export interface SetBannerState {
  type: 'set-banner-state';
  state: BannerState;
}

export interface SetFileSelectForMention {
  type: 'set-file-select-for-mention';
  requestId: string;
  file: ChatAttachedFile;
}

export interface SetShellToolConfigAction {
  type: 'set-shell-tool-config';
  config: ShellToolConfig;
}

export type FeatureFlagVariant = {
  value?: string;
  payload?: unknown;
};

export interface SetFeatureFlagsVariants {
  type: 'set-feature-flags-variants';
  variants: Record<string, FeatureFlagVariant>;
}

export interface SetAgentToolsAction {
  type: 'set-agent-tools';
  payload: {
    tools: Record<string, McpServerInfo>;
    isLoading: boolean;
  };
}
