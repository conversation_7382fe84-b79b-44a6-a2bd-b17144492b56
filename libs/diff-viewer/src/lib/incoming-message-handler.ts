import * as Sentry from '@sentry/react';
import { analytics } from '@src/lib/analytics';
import appStateManager from '@src/lib/app-state.manager';
import { colorTheme } from './color-theme';
import { DiffEditor } from './diff-editor';
import { initSyntaxHighlighting } from './syntax-highlighting';
import { grammars } from './syntax-highlighting/vscode-grammar-registry';
import { ReceivedMessage } from './types';

export const createMessageHandler = (editor: DiffEditor) => (event: MessageEvent) => {
  const message = event.data as ReceivedMessage;
  switch (message.type) {
    case 'set-settings':
      const { settings } = message;
      appStateManager.setSettings(settings);
      Sentry.setUser(settings.userId ? { id: settings.userId } : null);
      analytics.identify(message.settings.userId ?? '');
      break;
    case 'set-diff-model':
      editor.setInitialModel(message);
      break;
    case 'set-original-model':
      editor.setOriginalModel(message.original);
      break;
    case 'set-grammar':
      grammars.setGrammar(message.scopeName, message.grammar);
      break;
    case 'set-language-configuration':
      const { scopeName, configuration } = message;
      initSyntaxHighlighting(scopeName, configuration);
      break;
    case 'set-theme':
      colorTheme.applyVsCodeTheme(message.theme);
      break;
    case 'set-layout-mode':
      editor.setLayoutMode(message.mode);
      break;
    case 'set-metadata':
      appStateManager.setMetadata(message.properties);
      break;
    case 'force-accept':
      editor.performPrimaryAction();
      break;
    case 'force-reject':
      editor.rejectDiff();
      break;
    default:
      console.warn(`Unknown message type ${JSON.stringify(message)}`);
  }
};
