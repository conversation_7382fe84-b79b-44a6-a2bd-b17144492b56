import { RudderAnalytics } from '@rudderstack/analytics-js';
import * as Sentry from '@sentry/react';
import { WebviewAnalyticsOptions, WebviewSentryOptions } from './settings.model';

export const analytics = new RudderAnalytics();

const dataset = document.getElementById('webview-settings')?.dataset;

if (dataset) {
  const sentryOptions = dataset.sentry_options
    ? (JSON.parse(dataset.sentry_options) as WebviewSentryOptions)
    : null;
  const analyticsOptions = dataset.analytics_options
    ? (JSON.parse(dataset.analytics_options) as WebviewAnalyticsOptions)
    : null;

  if (sentryOptions && !Sentry.isInitialized()) {
    Sentry.init({
      ...sentryOptions,
      beforeSend: (event, hint) => {
        if (shouldFilterSentryEvent(event, hint)) {
          return null;
        }
        return event;
      },
    });
  }

  if (analyticsOptions?.isEnabled && analyticsOptions.writeKey && analyticsOptions.dataPlaneURL) {
    // do it first to avoid cleaning up user id for current user
    analytics.identify(analyticsOptions.userId ?? '');

    analytics.load(analyticsOptions.writeKey, analyticsOptions.dataPlaneURL);
    analytics.setAnonymousId(analyticsOptions.anonymousId);
  }
}

/*
 Monaco Editor throws some errors, that don't seem to affect user.
 Let's filter them out.
 */
function shouldFilterSentryEvent(event: Sentry.ErrorEvent, hint: Sentry.EventHint) {
  if (hint.originalException instanceof Error) {
    if (
      hint.originalException.message.includes(
        "Cannot read properties of undefined (reading 'charAt')"
      )
    ) {
      return true;
    }
    // Error from wasm module. Text converts to "invalid pattern in look-behind"
    if (
      hint.originalException.message.includes(
        '105,110,118,97,108,105,100,32,112,97,116,116,101,114,110,32,105,110,32,108,111,111,107,45,98,101,104,105,110,100'
      )
    ) {
      return true;
    }
  }

  return false;
}
