import { IColors, ITokenThemeRule, VsCodeColors, VsCodeTheme, VsCodeTokenSettings } from './types';

export const themeTokensToMonacoRules = (theme: VsCodeTheme): ITokenThemeRule[] =>
  (theme.settings || theme.tokenColors).flatMap(tokenColorToMonacoRule);

export const themeColorsToMonacoColors = (themeColors: VsCodeColors): IColors => {
  const pairs = Object.entries(themeColors || {}).map(([key, value]) => [
    key,
    `#${normalizeColorValue(value)}`,
  ]);
  return Object.fromEntries(pairs);
};

const parseScopes = (scopes: string): string[] => scopes.split(',').map((s) => s.trim());

const tokenColorToMonacoRule = ({ scope, settings }: VsCodeTokenSettings): ITokenThemeRule[] => {
  if (!scope) return [];
  if (!settings.foreground && !settings.background && !settings.fontStyle) return [];

  const scopes = Array.isArray(scope) ? scope : parseScopes(scope);
  return scopes.map((s) => {
    const foreground = normalizeColorValue(settings.foreground);
    const background = normalizeColorValue(settings.background);
    return {
      token: s,
      fontStyle: settings.fontStyle,
      ...(foreground && { foreground }),
      ...(background && { background }),
    };
  });
};

function normalizeColorValue(color: string | undefined): string | undefined {
  if (!color) return color;

  color = (color.charCodeAt(0) === 35 ? color.slice(1) : color).toLowerCase();
  if (color.length === 3 || color.length === 4) {
    color = color
      .split('')
      .map((c) => c + c)
      .join('');
  }
  return color;
}
