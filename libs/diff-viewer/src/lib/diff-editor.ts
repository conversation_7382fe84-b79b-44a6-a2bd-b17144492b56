import { analytics } from '@src/lib/analytics';
import { ideApi } from '@src/lib/ide-api';
import { AppState, DiffViewerMode, LayoutMode } from '@src/lib/types';
import * as monaco from 'monaco-editor-core';
import { LineRangeMapping } from 'monaco-editor-core/esm/vs/editor/common/diff/rangeMapping';
import appStateManager from './app-state.manager';

export class DiffEditor {
  private disposables: monaco.IDisposable[] = [];

  constructor(private readonly editor: monaco.editor.IStandaloneDiffEditor) {
    this.disposables.push(this.editor.onDidUpdateDiff(() => this.onDiffUpdated()));
    this.subscribeToViewChanges(this.editor.getOriginalEditor());
    this.subscribeToViewChanges(this.editor.getModifiedEditor());
  }

  public restoreAppState(state: AppState) {
    const { originalText, modifiedText, viewState, layoutMode, mode } = state;
    this.initModel({ original: originalText, modified: modifiedText });

    this.editor.updateOptions({
      readOnly: mode !== 'applyDiff',
    });

    if (viewState) {
      this.editor.restoreViewState(viewState);
      if (viewState.focusedEditor === 'modified') {
        this.editor.getModifiedEditor().focus();
      } else if (viewState.focusedEditor === 'original') {
        this.editor.getOriginalEditor().focus();
      }
    }
    if (!viewState?.original?.cursorState && !viewState?.modified?.cursorState) {
      this.editor.revealFirstDiff();
    }

    this.setLayoutMode(layoutMode);
  }

  public setInitialModel({
    original,
    modified,
    mode,
  }: {
    original: string;
    modified: string;
    mode: DiffViewerMode;
  }) {
    this.initModel({ original, modified });
    this.persistModel({ original, modified });
    appStateManager.setDiffViewerMode({ mode });
    this.editor.updateOptions({
      readOnly: mode !== 'applyDiff',
    });

    this.editor.revealFirstDiff();
  }

  public setOriginalModel(original: string) {
    const languageId = appStateManager.state?.languageId;
    const originalModel = monaco.editor.createModel(original, languageId);
    const modifiedModel =
      this.editor.getModel()?.modified ?? monaco.editor.createModel('', languageId);
    const viewState = this.editor.saveViewState();
    this.editor.setModel({
      original: originalModel,
      modified: modifiedModel,
    });
    this.editor.restoreViewState(viewState);
    this.persistModel({ original, modified: modifiedModel.getValue() });
  }

  public goToDiff(target: 'next' | 'previous') {
    this.editor.goToDiff(target);
  }

  public setLayoutMode(mode: LayoutMode) {
    appStateManager.setLayoutMode(mode);
    const isOriginalModelEmpty = appStateManager.state.originalText === '';
    if (isOriginalModelEmpty) {
      mode = 'inline';
    }
    switch (mode) {
      case 'side-by-side':
        this.editor.updateOptions({
          renderSideBySide: true,
          useInlineViewWhenSpaceIsLimited: false,
        });
        break;
      case 'inline':
        this.editor.updateOptions({
          renderSideBySide: false,
          useInlineViewWhenSpaceIsLimited: false,
        });
        break;
      case 'auto':
        this.editor.updateOptions({
          renderSideBySide: true,
          useInlineViewWhenSpaceIsLimited: true,
        });
        break;
    }
  }

  public handleRevertBlock(mapping: LineRangeMapping) {
    const { original, modified } = mapping;
    analytics.track('Diff codeblock reverted', {
      ...(appStateManager.state.metadata || {}),
      codeblock_lines_removed: original.endLineNumberExclusive - original.startLineNumber,
      codeblock_lines_added: modified.endLineNumberExclusive - modified.startLineNumber,
    });
  }

  public performPrimaryAction() {
    const mode = appStateManager.state.mode;
    if (mode === 'viewOnly') {
      // expected situation - user can call keyboard shortcuts in read-only mode
      return;
    }
    if (!mode) {
      throw new Error('Unexpected diff viewer mode');
    }

    const model = this.editor.getModel();
    if (!model) return;
    const { original, modified } = model;
    ideApi.postMessage({
      type: 'perform-primary-action',
      originalText: original.getValue(),
      fullModifiedText: modified.getValue(),
      addedBlocks: this.getAddedBlocks(),
    });
  }

  public rejectDiff() {
    ideApi.postMessage({
      type: 'reject-diff',
      addedBlocks: this.getAddedBlocks(),
    });
  }

  /**
   * Calculate added text to send to feedback service
   */
  private getAddedBlocks(): string[] {
    const model = this.editor.getModel();
    const lineChanges = this.editor.getLineChanges();
    if (!model || !lineChanges?.length) return [];

    const { modified } = model;

    return lineChanges.map((change) =>
      modified.getValueInRange({
        startLineNumber: change.modifiedStartLineNumber,
        startColumn: 0,
        endLineNumber: change.modifiedEndLineNumber,
        endColumn: Number.MAX_SAFE_INTEGER,
      })
    );
  }

  private initModel({ original, modified }: { original: string; modified: string }) {
    const originalModel = monaco.editor.createModel(original, appStateManager.languageId);
    const modifiedModel = monaco.editor.createModel(modified, appStateManager.languageId);
    this.editor.setModel({
      original: originalModel,
      modified: modifiedModel,
    });
  }

  private persistModel(model: { original: string; modified: string }) {
    appStateManager.setModels(model);
    this.setLayoutMode(appStateManager.state.layoutMode);
  }

  private onDiffUpdated() {
    const model = this.editor.getModel();
    appStateManager.setModels({
      original: model?.original.getValue() ?? '',
      modified: model?.modified.getValue() ?? '',
    });
  }

  private subscribeToViewChanges(editor: monaco.editor.IStandaloneCodeEditor) {
    this.disposables.push(
      editor.onDidChangeCursorPosition(this.onViewChange, this),
      editor.onDidChangeCursorSelection(this.onViewChange, this),
      editor.onDidLayoutChange(this.onViewChange, this),
      editor.onDidScrollChange(this.onViewChange, this)
    );
  }

  private onViewChange() {
    // NB: It doesn't persist undo history.
    // https://github.com/microsoft/monaco-editor/issues/239 for more details.
    const viewState = this.editor.saveViewState();

    let focusedEditor: 'original' | 'modified' | undefined;
    if (
      this.editor.getOriginalEditor().hasTextFocus() ||
      this.editor.getOriginalEditor().hasWidgetFocus()
    ) {
      focusedEditor = 'original';
    } else if (
      this.editor.getModifiedEditor().hasTextFocus() ||
      this.editor.getModifiedEditor().hasWidgetFocus()
    ) {
      focusedEditor = 'modified';
    }

    appStateManager.setViewState(viewState ? { ...viewState, focusedEditor } : undefined);
  }

  dispose() {
    this.disposables.forEach((d) => d.dispose());
    this.disposables = [];
    this.editor.dispose();
  }
}
