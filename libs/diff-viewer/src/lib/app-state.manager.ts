import { ideApi } from '@src/lib/ide-api';
import { Settings } from '@src/lib/settings.model';
import { AppState, DiffViewerMode, SyntaxHighlightingState } from '@src/lib/types';
import { IStandaloneThemeData } from './color-theme/types';

class AppStateManager {
  private _state = ideApi.getState() ?? createInitialState();

  private changeListeners: Array<(newState: AppState) => void> = [];

  get state() {
    return this._state;
  }

  get languageId() {
    return this._state.languageId;
  }

  public registerChangeListener(listener: (newState: AppState) => void) {
    this.changeListeners.push(listener);
    return () => this.changeListeners.splice(this.changeListeners.indexOf(listener), 1);
  }

  private updateState(newState: Partial<AppState>) {
    this._state = { ...this._state, ...newState };
    ideApi.setState(this.state);
    for (let listener of this.changeListeners) {
      listener(this.state);
    }
  }

  public setSettings(settings: Settings) {
    this.updateState({ settings });
  }

  public setSyntaxHighlighting(syntaxHighlighting: SyntaxHighlightingState) {
    this.updateState({ syntaxHighlighting });
  }

  setTheme(colorTheme: IStandaloneThemeData) {
    this.updateState({ colorTheme });
  }

  setLanguage(languageId: string) {
    this.updateState({ languageId });
  }

  public setMetadata(metadata: Record<string, unknown>) {
    this.updateState({ metadata });
  }

  public setModels({ original, modified }: { original: string; modified: string }) {
    this.updateState({
      originalText: original,
      modifiedText: modified,
    });
  }

  public setLayoutMode(layoutMode: AppState['layoutMode']) {
    this.updateState({ layoutMode });
  }

  public setViewState(viewState: AppState['viewState']) {
    this.updateState({ viewState });
  }

  public setDiffViewerMode({ mode }: { mode: DiffViewerMode }) {
    this.updateState({ mode });
  }
}

const createInitialState = (): AppState => {
  const languageId = document.getElementById('root')?.getAttribute('data-language-id');
  if (!languageId) throw Error('Missing data-language-id attribute in root element');
  return {
    languageId,
    originalText: '',
    modifiedText: '',
    layoutMode: 'auto',
    metadata: {},
  };
};

const instance = new AppStateManager();
export default instance;
