import { Settings } from '@src/lib/settings.model';
import * as monaco from 'monaco-editor-core';
import { IGrammarDefinition } from 'monaco-textmate';
import { IStandaloneThemeData, VsCodeTheme } from './color-theme/types';

export type ReceivedMessage =
  | {
      readonly type: 'set-settings';
      readonly settings: Settings;
    }
  | {
      readonly type: 'set-diff-model';
      readonly original: string;
      readonly modified: string;
      readonly mode: DiffViewerMode;
    }
  | {
      readonly type: 'set-original-model';
      readonly original: string;
    }
  | {
      readonly type: 'set-language-configuration';
      readonly scopeName: string;
      readonly configuration?: SerializedLanguageConfiguration;
    }
  | {
      readonly type: 'set-grammar';
      readonly scopeName: string;
      readonly grammar: IGrammarDefinition | null;
    }
  | { readonly type: 'set-theme'; readonly theme: VsCodeTheme }
  | {
      readonly type: 'set-layout-mode';
      readonly mode: LayoutMode;
    }
  | {
      readonly type: 'set-metadata';
      readonly properties: Record<string, unknown>;
    }
  | {
      readonly type: 'force-accept';
    }
  | {
      readonly type: 'force-reject';
    };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SerializedLanguageConfiguration = Record<string, any>;

export type LayoutMode = 'side-by-side' | 'inline' | 'auto';
export type DiffViewerMode = 'applyDiff' | 'revertDiff' | 'removeFile' | 'viewOnly';

/**
 * Whole Diff Viewer WebView state.
 * Webview itself is destroyed when user navigates to another page.
 * Applications needs to be able to restore its state.
 */
export interface AppState {
  readonly languageId: string;
  readonly originalText: string;
  readonly modifiedText: string;
  readonly mode?: DiffViewerMode;
  readonly viewState?: monaco.editor.IDiffEditorViewState & {
    focusedEditor?: 'modified' | 'original';
  };
  readonly layoutMode: LayoutMode;
  readonly syntaxHighlighting?: SyntaxHighlightingState;
  readonly colorTheme?: IStandaloneThemeData;
  readonly settings?: Settings;
  readonly metadata?: Record<string, unknown>;
}

export interface SyntaxHighlightingState {
  readonly scopeName: string;
  readonly configuration?: SerializedLanguageConfiguration;
}
