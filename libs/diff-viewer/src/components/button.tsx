import { Button as BaseButton } from '@headlessui/react';
import { ComponentProps, forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';

interface ButtonProps extends ComponentProps<'button'> {}

const Button = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <BaseButton
      className={twMerge(
        'text-sm',
        'font-semibold',
        'leading-4',
        'py-1',
        'px-2',
        'rounded',
        'select-none',
        'data-[disabled]:opacity-100',
        'focus:outline-none',
        'focus:ring',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const PrimaryButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-primary-foreground',
        'bg-primary-background',
        'data-[hover]:bg-primary-hover',
        'data-[hover]:data-[active]:bg-primary-active',
        'data-[disabled]:bg-transparent',
        'data-[disabled]:text-disabled-foreground',
        'data-[disabled]:shadow-disabled-box',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const DestructiveButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-destructive-foreground',
        'bg-destructive-background',
        'data-[hover]:bg-destructive-hover',
        'data-[hover]:data-[active]:bg-destructive-active',
        'data-[disabled]:bg-transparent',
        'data-[disabled]:text-disabled-foreground',
        'data-[disabled]:shadow-disabled-box',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});

export const LinkButton = forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  const { className, ...rest } = props;
  return (
    <Button
      className={twMerge(
        'text-foreground',
        'bg-transparent',
        'border',
        'border-transparent',
        'data-[hover]:bg-hover-background',
        'data-[hover]:data-[active]:bg-hover-background',
        'data-[disabled]:text-disabled-foreground',
        className
      )}
      {...rest}
      ref={ref}
    />
  );
});
