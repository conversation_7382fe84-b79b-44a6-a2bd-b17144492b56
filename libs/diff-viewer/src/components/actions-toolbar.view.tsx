import { DestructiveButton, LinkButton, PrimaryButton } from '@src/components/button';
import { Tooltip, useTooltipId } from '@src/components/tooltip';
import { useAppState } from '@src/hooks/use-app-state';
import ArrowDownIcon from '@src/icons/arrow-down.svg';
import ArrowUpIcon from '@src/icons/arrow-up.svg';
import { getDiffEditorInstance } from '@src/lib/init-diff-editor';

export function ActionsToolbarView() {
  const idPrev = useTooltipId();
  const idNext = useTooltipId();
  const appState = useAppState();
  const { settings, mode } = appState ?? {};

  const applyKeyboardShortcut = !!settings ? (
    <>
      {' '}
      <span className={'opacity-50'}>{settings.os === 'macOS' ? '⌘⏎' : 'Ctrl+Enter'}</span>
    </>
  ) : null;

  const handlePrimaryActionClick = () => {
    getDiffEditorInstance()?.performPrimaryAction();
  };

  return (
    <div className={'actions-toolbar'}>
      <div className={'left-bar'}>
        {mode === 'applyDiff' && (
          <PrimaryButton onClick={handlePrimaryActionClick}>
            <span>Accept</span>
            {applyKeyboardShortcut}
          </PrimaryButton>
        )}
        {mode === 'removeFile' && (
          <DestructiveButton onClick={handlePrimaryActionClick}>
            <span>Delete</span>
            {applyKeyboardShortcut}
          </DestructiveButton>
        )}
        {mode === 'revertDiff' && (
          <DestructiveButton onClick={handlePrimaryActionClick}>
            <span>Revert</span>
            {applyKeyboardShortcut}
          </DestructiveButton>
        )}
        {mode && (
          <LinkButton
            onClick={() => {
              getDiffEditorInstance()?.rejectDiff();
            }}
          >
            <span>{mode === 'applyDiff' ? 'Reject' : 'Ignore'}</span>{' '}
            <span className={'opacity-50'}>Esc</span>
          </LinkButton>
        )}
      </div>
      <div className={'right-bar'}>
        <LinkButton
          className={'px-1'}
          onClick={() => {
            getDiffEditorInstance()?.goToDiff('previous');
          }}
          data-tooltip-id={idPrev}
        >
          <ArrowUpIcon />
        </LinkButton>
        <Tooltip id={idPrev}>Go to previous change</Tooltip>
        <LinkButton
          className={'px-1'}
          onClick={() => {
            getDiffEditorInstance()?.goToDiff('next');
          }}
          data-tooltip-id={idNext}
        >
          <ArrowDownIcon />
        </LinkButton>
        <Tooltip id={idNext}>Go to next change</Tooltip>
      </div>
    </div>
  );
}
