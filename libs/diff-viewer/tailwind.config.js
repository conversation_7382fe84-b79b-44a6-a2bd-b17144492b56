module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx}', './src/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        'secondary-foreground': 'var(--z-secondary-foreground)',
        'hover-background': 'var(--z-hover-background)',
        'secondary-background': 'var(--z-secondary-background)',
        'error-background': 'var(--z-error-background)',
        'editor-widget-border': 'var(--z-editor-widget-border)',
        'primary-background': 'var(--z-primary-background)',
        'primary-foreground': 'var(--z-primary-foreground)',
        'primary-hover': 'var(--z-primary-hover)',
        'primary-active': 'var(--z-primary-active)',
        'destructive-background': 'var(--z-destructive-background)',
        'destructive-foreground': 'var(--z-destructive-foreground)',
        'destructive-hover': 'var(--z-destructive-hover)',
        'destructive-active': 'var(--z-destructive-active)',
        'disabled-foreground': 'var(--z-disabled-foreground)',
        'editor-background': 'var(--z-editor-background)',
        'editor-foreground': 'var(--z-editor-foreground)',
        'error-foreground': 'var(--z-error-foreground)',
        'warning-foreground': 'var(--z-warning-foreground)',
        foreground: 'var(--z-foreground)',
        'input-background': 'var(--z-input-background)',
        'input-foreground': 'var(--z-input-foreground)',
        'input-placeholder-foreground': 'var(--z-input-placeholder-foreground)',
        background: 'var(--z-background)',
      },
      fontSize: {
        xs: ['10px', '1.2'],
        sm: ['12px', '1.3'],
        base: ['13px', '1.4'],
        lg: ['16px', '1.4'],
        xl: ['20px', '1.4'],
      },
      boxShadow: {
        tooltip: '0 2px 16px 0 var(--z-editor-background)',
      },
    },
  },
};
