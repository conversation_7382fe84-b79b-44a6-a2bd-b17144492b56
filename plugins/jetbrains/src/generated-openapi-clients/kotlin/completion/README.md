# ai.zencoder.generated.client.completion - Kotlin client library for code-completion-service

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

## Overview

This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project. By using the [openapi-spec](https://github.com/OAI/OpenAPI-Specification) from a remote server, you can easily generate an API client.

- API version: 0.0.7
- Package version:
- Generator version: 7.10.0
- Build package: org.openapitools.codegen.languages.KotlinClientCodegen

## Requires

- Kotlin 1.7.21
- Gradle 7.5

## Build

First, create the gradle wrapper script:

```
gradle wrapper
```

Then, run:

```
./gradlew check assemble
```

This runs all tests and packages the library.

## Features/Implementation Notes

- Supports JSON inputs/outputs, File inputs, and Form inputs.
- Supports collection formats for query parameters: csv, tsv, ssv, pipes.
- Some Kotlin and Java types are fully qualified to avoid conflicts with types defined in OpenAPI definitions.
- Implementation of ApiClient is intended to reduce method counts, specifically to benefit Android targets.

<a id="documentation-for-models"></a>

## Documentation for Models

- [ai.zencoder.generated.client.completion.models.CodeCompletionContext](docs/CodeCompletionContext.md)
- [ai.zencoder.generated.client.completion.models.CodeCompletionRequest](docs/CodeCompletionRequest.md)
- [ai.zencoder.generated.client.completion.models.CodeCompletionResponse](docs/CodeCompletionResponse.md)
- [ai.zencoder.generated.client.completion.models.CursorPosition](docs/CursorPosition.md)
- [ai.zencoder.generated.client.completion.models.EditPredictionRequest](docs/EditPredictionRequest.md)
- [ai.zencoder.generated.client.completion.models.EditPredictionResponse](docs/EditPredictionResponse.md)
- [ai.zencoder.generated.client.completion.models.ExternalSignature](docs/ExternalSignature.md)
- [ai.zencoder.generated.client.completion.models.FileState](docs/FileState.md)
- [ai.zencoder.generated.client.completion.models.HTTPValidationError](docs/HTTPValidationError.md)
- [ai.zencoder.generated.client.completion.models.ValidationError](docs/ValidationError.md)

<a id="documentation-for-authorization"></a>

## Documentation for Authorization

Authentication schemes defined for the API:
<a id="OAuth2"></a>

### OAuth2

- **Type**: OAuth
- **Flow**: accessCode
- **Authorization URL**: https://example.com/oauth/authorize
- **Scopes**:
  - read: Grants read access
  - write: Grants write access
