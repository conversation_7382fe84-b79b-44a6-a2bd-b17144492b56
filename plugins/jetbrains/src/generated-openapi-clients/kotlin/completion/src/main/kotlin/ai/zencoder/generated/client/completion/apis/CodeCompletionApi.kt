/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ai.zencoder.generated.client.completion.apis

import java.io.IOException
import okhttp3.Call
import okhttp3.HttpUrl
import java.util.concurrent.Future
import ai.zencoder.plugin.api.exception.trackRateLimit
import ai.zencoder.plugin.api.exception.RateLimitException
import kotlin.reflect.full.declaredMemberProperties

import ai.zencoder.generated.client.completion.models.CodeCompletionRequest
import ai.zencoder.generated.client.completion.models.CodeCompletionResponse
import ai.zencoder.generated.client.completion.models.EditPredictionRequest
import ai.zencoder.generated.client.completion.models.EditPredictionResponse
import ai.zencoder.generated.client.completion.models.HTTPValidationError

import com.fasterxml.jackson.annotation.JsonProperty
import okhttp3.OkHttpClient
import okhttp3.Callback

import ai.zencoder.generated.client.completion.infrastructure.ApiClient
import ai.zencoder.generated.client.completion.infrastructure.ApiResponse
import ai.zencoder.generated.client.completion.infrastructure.ClientException
import ai.zencoder.generated.client.completion.infrastructure.ClientError
import ai.zencoder.generated.client.completion.infrastructure.ServerException
import ai.zencoder.generated.client.completion.infrastructure.ServerError
import ai.zencoder.generated.client.completion.infrastructure.MultiValueMap
import ai.zencoder.generated.client.completion.infrastructure.PartConfig
import ai.zencoder.generated.client.completion.infrastructure.RequestConfig
import ai.zencoder.generated.client.completion.infrastructure.RequestMethod
import ai.zencoder.generated.client.completion.infrastructure.ResponseType
import ai.zencoder.generated.client.completion.infrastructure.Success
import ai.zencoder.generated.client.completion.infrastructure.toMultiValue

class CodeCompletionApi(basePath: kotlin.String = defaultBasePath, client: OkHttpClient = ApiClient.defaultClient) : ApiClient(basePath, client) {
    companion object {
        @JvmStatic
        val defaultBasePath: String by lazy {
            System.getProperties().getProperty(ApiClient.BASE_URL_KEY, "http://localhost/code-completion-service")
        }
    }

    /**
     * Fireworks
     * 
     * @param codeCompletionRequest 
     * @return CodeCompletionResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
    @Deprecated(message = "This operation is deprecated.")
    fun fireworksFireworksPost(codeCompletionRequest: CodeCompletionRequest) : CodeCompletionResponse {
        @Suppress("DEPRECATION")
        val localVarResponse = fireworksFireworksPostWithHttpInfo(codeCompletionRequest = codeCompletionRequest)
        val requestId = codeCompletionRequest::class.declaredMemberProperties.firstOrNull { it.name == "requestId" || it.name == "operationId" }?.getter?.call(codeCompletionRequest) as? String

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as CodeCompletionResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                if (localVarError.statusCode == 429) {
                    val retryAfter = localVarError.headers[RETRY_AFTER]?.firstOrNull()?.toLongOrNull()
                    val quotaKey = localVarError.headers[QUOTA_KEY]?.firstOrNull()
                    trackRateLimit(baseUrl, retryAfter, quotaKey)

                    throw RateLimitException(retryAfter)
                }
                val clientErrorMessage = buildString {
                    append("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}")
                    requestId?.let { append(" Request ID: $it") }
                }
                throw ClientException(clientErrorMessage, localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }


    /**
    * Asynchronous version of fireworksFireworksPost
    * Fireworks
    * 
    * @param codeCompletionRequest 
    * @return CodeCompletionResponse
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    * @throws UnsupportedOperationException If the API returns an informational or redirection response
    * @throws ClientException If the API returns a client error response
    * @throws ServerException If the API returns a server error response
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
        @Deprecated(message = "This operation is deprecated.")
    fun fireworksFireworksPostAsync(codeCompletionRequest: CodeCompletionRequest) :   Future<CodeCompletionResponse> {
            @Suppress("DEPRECATION")
        val localVarResponse = fireworksFireworksPostWithHttpInfoAsync(codeCompletionRequest = codeCompletionRequest)
        return localVarResponse
    }

    /**
     * Fireworks
     * 
     * @param codeCompletionRequest 
     * @return ApiResponse<CodeCompletionResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    @Deprecated(message = "This operation is deprecated.")
    fun fireworksFireworksPostWithHttpInfo(codeCompletionRequest: CodeCompletionRequest) : ApiResponse<CodeCompletionResponse?> {
        @Suppress("DEPRECATION")
        val localVariableConfig = fireworksFireworksPostRequestConfig(codeCompletionRequest = codeCompletionRequest)

        return request<CodeCompletionRequest, CodeCompletionResponse>(
            localVariableConfig
        )
    }

    /**
    * Asynchronous version of fireworksFireworksPostWithHttpInfo
    * Fireworks
    * 
    * @param codeCompletionRequest 
    * @return ApiResponse<CodeCompletionResponse?>
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
        @Deprecated(message = "This operation is deprecated.")
    fun fireworksFireworksPostWithHttpInfoAsync(codeCompletionRequest: CodeCompletionRequest) : Future<CodeCompletionResponse> {
        @Suppress("DEPRECATION")
    val localVariableConfig = fireworksFireworksPostRequestConfig(codeCompletionRequest = codeCompletionRequest)

    return requestAsync<CodeCompletionRequest, CodeCompletionResponse>(
    localVariableConfig
        )
    }



    /**
     * To obtain the request config of the operation fireworksFireworksPost
     *
     * @param codeCompletionRequest 
     * @return RequestConfig
     */
    @Deprecated(message = "This operation is deprecated.")
    fun fireworksFireworksPostRequestConfig(codeCompletionRequest: CodeCompletionRequest) : RequestConfig<CodeCompletionRequest> {
        val localVariableBody = codeCompletionRequest
        val localVariableQuery: MultiValueMap = mutableMapOf()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders[CONTENT_TYPE] = "application/json"
        localVariableHeaders[ACCEPT] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/fireworks",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Inference
     * 
     * @param sentryTrace 
     * @param codeCompletionRequest 
     * @return CodeCompletionResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
    fun inferenceInferencePost(sentryTrace: kotlin.String, codeCompletionRequest: CodeCompletionRequest) : CodeCompletionResponse {
        val localVarResponse = inferenceInferencePostWithHttpInfo(sentryTrace = sentryTrace, codeCompletionRequest = codeCompletionRequest)
        val requestId = codeCompletionRequest::class.declaredMemberProperties.firstOrNull { it.name == "requestId" || it.name == "operationId" }?.getter?.call(codeCompletionRequest) as? String

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as CodeCompletionResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                if (localVarError.statusCode == 429) {
                    val retryAfter = localVarError.headers[RETRY_AFTER]?.firstOrNull()?.toLongOrNull()
                    val quotaKey = localVarError.headers[QUOTA_KEY]?.firstOrNull()
                    trackRateLimit(baseUrl, retryAfter, quotaKey)

                    throw RateLimitException(retryAfter)
                }
                val clientErrorMessage = buildString {
                    append("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}")
                    requestId?.let { append(" Request ID: $it") }
                }
                throw ClientException(clientErrorMessage, localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }


    /**
    * Asynchronous version of inferenceInferencePost
    * Inference
    * 
    * @param sentryTrace 
    * @param codeCompletionRequest 
    * @return CodeCompletionResponse
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    * @throws UnsupportedOperationException If the API returns an informational or redirection response
    * @throws ClientException If the API returns a client error response
    * @throws ServerException If the API returns a server error response
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
    fun inferenceInferencePostAsync(sentryTrace: kotlin.String, codeCompletionRequest: CodeCompletionRequest) :   Future<CodeCompletionResponse> {
        val localVarResponse = inferenceInferencePostWithHttpInfoAsync(sentryTrace = sentryTrace, codeCompletionRequest = codeCompletionRequest)
        return localVarResponse
    }

    /**
     * Inference
     * 
     * @param sentryTrace 
     * @param codeCompletionRequest 
     * @return ApiResponse<CodeCompletionResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun inferenceInferencePostWithHttpInfo(sentryTrace: kotlin.String, codeCompletionRequest: CodeCompletionRequest) : ApiResponse<CodeCompletionResponse?> {
        val localVariableConfig = inferenceInferencePostRequestConfig(sentryTrace = sentryTrace, codeCompletionRequest = codeCompletionRequest)

        return request<CodeCompletionRequest, CodeCompletionResponse>(
            localVariableConfig
        )
    }

    /**
    * Asynchronous version of inferenceInferencePostWithHttpInfo
    * Inference
    * 
    * @param sentryTrace 
    * @param codeCompletionRequest 
    * @return ApiResponse<CodeCompletionResponse?>
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun inferenceInferencePostWithHttpInfoAsync(sentryTrace: kotlin.String, codeCompletionRequest: CodeCompletionRequest) : Future<CodeCompletionResponse> {
    val localVariableConfig = inferenceInferencePostRequestConfig(sentryTrace = sentryTrace, codeCompletionRequest = codeCompletionRequest)

    return requestAsync<CodeCompletionRequest, CodeCompletionResponse>(
    localVariableConfig
        )
    }



    /**
     * To obtain the request config of the operation inferenceInferencePost
     *
     * @param sentryTrace 
     * @param codeCompletionRequest 
     * @return RequestConfig
     */
    fun inferenceInferencePostRequestConfig(sentryTrace: kotlin.String, codeCompletionRequest: CodeCompletionRequest) : RequestConfig<CodeCompletionRequest> {
        val localVariableBody = codeCompletionRequest
        val localVariableQuery: MultiValueMap = mutableMapOf()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        sentryTrace.apply { localVariableHeaders["sentry-trace"] = this.toString() }
        localVariableHeaders[CONTENT_TYPE] = "application/json"
        localVariableHeaders[ACCEPT] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/inference",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Next Edit Prediction
     * 
     * @param editPredictionRequest 
     * @return EditPredictionResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
    fun nextEditPredictionPost(editPredictionRequest: EditPredictionRequest) : EditPredictionResponse {
        val localVarResponse = nextEditPredictionPostWithHttpInfo(editPredictionRequest = editPredictionRequest)
        val requestId = editPredictionRequest::class.declaredMemberProperties.firstOrNull { it.name == "requestId" || it.name == "operationId" }?.getter?.call(editPredictionRequest) as? String

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as EditPredictionResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                if (localVarError.statusCode == 429) {
                    val retryAfter = localVarError.headers[RETRY_AFTER]?.firstOrNull()?.toLongOrNull()
                    val quotaKey = localVarError.headers[QUOTA_KEY]?.firstOrNull()
                    trackRateLimit(baseUrl, retryAfter, quotaKey)

                    throw RateLimitException(retryAfter)
                }
                val clientErrorMessage = buildString {
                    append("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}")
                    requestId?.let { append(" Request ID: $it") }
                }
                throw ClientException(clientErrorMessage, localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }


    /**
    * Asynchronous version of nextEditPredictionPost
    * Next Edit Prediction
    * 
    * @param editPredictionRequest 
    * @return EditPredictionResponse
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    * @throws UnsupportedOperationException If the API returns an informational or redirection response
    * @throws ClientException If the API returns a client error response
    * @throws ServerException If the API returns a server error response
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, RateLimitException::class,ServerException::class)
    fun nextEditPredictionPostAsync(editPredictionRequest: EditPredictionRequest) :   Future<EditPredictionResponse> {
        val localVarResponse = nextEditPredictionPostWithHttpInfoAsync(editPredictionRequest = editPredictionRequest)
        return localVarResponse
    }

    /**
     * Next Edit Prediction
     * 
     * @param editPredictionRequest 
     * @return ApiResponse<EditPredictionResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun nextEditPredictionPostWithHttpInfo(editPredictionRequest: EditPredictionRequest) : ApiResponse<EditPredictionResponse?> {
        val localVariableConfig = nextEditPredictionPostRequestConfig(editPredictionRequest = editPredictionRequest)

        return request<EditPredictionRequest, EditPredictionResponse>(
            localVariableConfig
        )
    }

    /**
    * Asynchronous version of nextEditPredictionPostWithHttpInfo
    * Next Edit Prediction
    * 
    * @param editPredictionRequest 
    * @return ApiResponse<EditPredictionResponse?>
    * @throws IllegalStateException If the request is not correctly configured
    * @throws IOException Rethrows the OkHttp execute method exception
    */
        @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun nextEditPredictionPostWithHttpInfoAsync(editPredictionRequest: EditPredictionRequest) : Future<EditPredictionResponse> {
    val localVariableConfig = nextEditPredictionPostRequestConfig(editPredictionRequest = editPredictionRequest)

    return requestAsync<EditPredictionRequest, EditPredictionResponse>(
    localVariableConfig
        )
    }



    /**
     * To obtain the request config of the operation nextEditPredictionPost
     *
     * @param editPredictionRequest 
     * @return RequestConfig
     */
    fun nextEditPredictionPostRequestConfig(editPredictionRequest: EditPredictionRequest) : RequestConfig<EditPredictionRequest> {
        val localVariableBody = editPredictionRequest
        val localVariableQuery: MultiValueMap = mutableMapOf()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders[CONTENT_TYPE] = "application/json"
        localVariableHeaders[ACCEPT] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/predict-next-edit",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    private fun encodeURIComponent(uriComponent: kotlin.String): kotlin.String =
        HttpUrl.Builder().scheme("http").host("localhost").addPathSegment(uriComponent).build().encodedPathSegments[0]

    fun withAuth(token: String?): CodeCompletionApi {
        ApiClient.accessToken = token ?: ""
        return this
    }
}
