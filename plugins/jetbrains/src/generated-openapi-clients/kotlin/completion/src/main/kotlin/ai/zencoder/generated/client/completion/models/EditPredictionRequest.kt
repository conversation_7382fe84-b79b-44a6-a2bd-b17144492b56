/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ai.zencoder.generated.client.completion.models

import ai.zencoder.generated.client.completion.models.CursorPosition
import ai.zencoder.generated.client.completion.models.FileState

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param requestId 
 * @param fileStates 
 * @param fileContent 
 * @param language 
 * @param filePath 
 * @param cursorPosition 
 */


data class EditPredictionRequest (

    @get:JsonProperty("requestId")
    val requestId: kotlin.String,

    @get:JsonProperty("fileStates")
    val fileStates: kotlin.collections.List<FileState>,

    @get:JsonProperty("fileContent")
    val fileContent: kotlin.String,

    @get:JsonProperty("language")
    val language: kotlin.String,

    @get:JsonProperty("filePath")
    val filePath: kotlin.String,

    @get:JsonProperty("cursorPosition")
    val cursorPosition: CursorPosition? = null

) {


}

