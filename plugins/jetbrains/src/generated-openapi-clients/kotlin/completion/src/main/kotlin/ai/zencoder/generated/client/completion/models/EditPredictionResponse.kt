/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ai.zencoder.generated.client.completion.models

import ai.zencoder.generated.client.completion.models.CursorPosition

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Response model for the next edit prediction feature.
 *
 * @param originalStr 
 * @param modifiedStr 
 * @param cursorPosition 
 */


data class EditPredictionResponse (

    @get:JsonProperty("original_str")
    val originalStr: kotlin.String,

    @get:JsonProperty("modified_str")
    val modifiedStr: kotlin.String,

    @get:JsonProperty("cursor_position")
    val cursorPosition: CursorPosition

) {


}

