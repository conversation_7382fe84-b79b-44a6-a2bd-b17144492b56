/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ai.zencoder.generated.client.completion.models


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param row 
 * @param column 
 */


data class CursorPosition (

    @get:JsonProperty("row")
    val row: kotlin.Int,

    @get:JsonProperty("column")
    val column: kotlin.Int

) {


}

