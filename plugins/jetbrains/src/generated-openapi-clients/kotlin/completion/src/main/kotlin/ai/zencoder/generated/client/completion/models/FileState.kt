/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ai.zencoder.generated.client.completion.models


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Represents a file state with its content and cursor position.
 *
 * @param timestamp 
 * @param content 
 */


data class FileState (

    @get:JsonProperty("timestamp")
    val timestamp: kotlin.Long,

    @get:JsonProperty("content")
    val content: kotlin.String

) {


}

