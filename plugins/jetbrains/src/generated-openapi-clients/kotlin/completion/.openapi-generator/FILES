.openapi-generator-ignore
README.md
build.gradle
docs/CodeCompletionContext.md
docs/CodeCompletionRequest.md
docs/CodeCompletionResponse.md
docs/CursorPosition.md
docs/EditPredictionRequest.md
docs/EditPredictionResponse.md
docs/ExternalSignature.md
docs/FileState.md
docs/HTTPValidationError.md
docs/ValidationError.md
gradle/wrapper/gradle-wrapper.jar
gradle/wrapper/gradle-wrapper.properties
gradlew
gradlew.bat
settings.gradle
src/main/kotlin/ai/zencoder/generated/client/completion/apis/CodeCompletionApi.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/ApiAbstractions.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/ApiClient.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/ApiResponse.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/Errors.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/PartConfig.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/RequestConfig.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/RequestMethod.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/ResponseExtensions.kt
src/main/kotlin/ai/zencoder/generated/client/completion/infrastructure/Serializer.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/CodeCompletionContext.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/CodeCompletionRequest.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/CodeCompletionResponse.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/CursorPosition.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/EditPredictionRequest.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/EditPredictionResponse.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/ExternalSignature.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/FileState.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/HTTPValidationError.kt
src/main/kotlin/ai/zencoder/generated/client/completion/models/ValidationError.kt
