package ai.zencoder.plugin.api.thirdparty.jira.auth

import ai.zencoder.plugin.auth.AbstractOAuthService
import ai.zencoder.plugin.auth.AuthInfo
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.auth.OAuthRefreshTokenRequest
import ai.zencoder.plugin.auth.OAuthRefreshTokenResponse
import ai.zencoder.plugin.auth.OAuthTokenExchangeResponse
import ai.zencoder.plugin.config.TokenStorage
import ai.zencoder.plugin.obfuscation.DoNotObfuscate
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.settings.ZencoderSettings
import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import kotlinx.serialization.Serializable
import okhttp3.Request

@Service(Service.Level.APP)
class JiraAuthService : AbstractOAuthService() {

    override val providerTitle: String
        get() = "Jira"

    override val authorizationUrl: String = service<ZencoderSettings>().signInUrl
    override val signUpUrl: String? = null
    override val redirectUrlPath: String
        get() = "/jira-callback"
    override val tokenExchangeUrl: String
        get() = "$authorizationUrl/api/oauth/third-party/token"
    override val tokenRefreshUrl: String
        get() = "$authorizationUrl/api/oauth/third-party/refresh-token"
    override val clientRedirectUrl: String
        get() = "$authorizationUrl/extension/third-party/auth-success"

    override fun getTokenStorage(): TokenStorage = service<JiraTokensStorage>()

    override fun generateLoginUrl(redirectUri: String, codeChallenge: String?): String =
        "$authorizationUrl/extension/third-party/signin?provider_id=jira&redirect_uri=$redirectUri"

    override fun generateSignUpUrl(redirectUri: String, codeChallenge: String?): String =
        throw UnsupportedOperationException("JIRA authentication doesn't require sign up.")

    override fun createRefreshTokenRequest(
        refreshToken: String,
        grantType: String?,
        providerType: String
    ): OAuthRefreshTokenRequest = JiraRefreshTokenRequest(
        refreshToken = refreshToken,
        providerType = providerType
    )

    override fun parseRefreshTokenResponse(responseBody: String): OAuthRefreshTokenResponse =
        json.decodeFromString<JiraRefreshTokenResponse>(responseBody)

    override fun serializeRefreshTokenRequest(request: OAuthRefreshTokenRequest): String =
        json.encodeToString(JiraRefreshTokenRequest.serializer(), request as JiraRefreshTokenRequest)

    override fun parseTokenExchangeResponse(responseBody: String): OAuthTokenExchangeResponse =
        objectMapper.readValue(responseBody, JiraTokenExchangeResponse::class.java)

    override fun postLoginActions(result: AuthInfo) {
        track("User signed in", mapOf("provider" to "jira"))
        service<JiraAuthObserver>().signIn(result)
    }
    override fun refreshRequestBuilderModifier(builder: Request.Builder) {
        builder.header("Authorization", "Bearer ${service<AuthService>().accessToken}")
    }
}

@DoNotObfuscate
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@Serializable
data class JiraRefreshTokenRequest(
    override val refreshToken: String,
    override val grantType: String = "refresh_token",
    val providerType: String
) : OAuthRefreshTokenRequest

@DoNotObfuscate
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@Serializable
data class JiraRefreshTokenResponse(
    override val accessToken: String,
    override val refreshToken: String
) : OAuthRefreshTokenResponse

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
private data class JiraTokenExchangeResponse(
    val accessToken: String,
    val refreshToken: String,
    val expiresIn: Int? = null,
    val tokenType: String? = null
) : OAuthTokenExchangeResponse {
    override fun accessToken(): String = accessToken

    override fun refreshToken(): String = refreshToken
}
