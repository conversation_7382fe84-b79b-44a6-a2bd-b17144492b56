package ai.zencoder.plugin.auth

import ai.zencoder.plugin.observability.AnalyticsService
import ai.zencoder.plugin.settings.ZencoderSettings
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import io.sentry.Sentry
import io.sentry.SentryEvent
import io.sentry.SentryLevel
import io.sentry.protocol.Message
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.TimeUnit

@Service(Service.Level.APP)
class AuthOperationsTrackerService(val scope: CoroutineScope) {

    private val authenticationCallsLog: Cache<Long, String> = CacheBuilder.newBuilder()
        .expireAfterWrite(1, TimeUnit.HOURS)
        .maximumSize(20)
        .build()
    private val authLogOperationsMutex by lazy { Mutex() }

    fun info(message: String) {
        thisLogger().info(message)
        trackMessage(message)
    }

    fun warn(message: String, e: Exception? = null) {
        thisLogger().warn(message, e)
        trackMessage(message)
    }

    fun error(message: String, e: Exception? = null) {
        thisLogger().error(message, e)
        trackMessage(message)
    }

    private fun trackMessage(message: String) {
        scope.launch {
            authLogOperationsMutex.withLock {
                authenticationCallsLog.put(System.currentTimeMillis(), message.maskFilteredWords())
            }
        }
    }

    fun captureExceptionWithContext(exception: Throwable) {
        thisLogger().error(exception)
        scope.launch {
            authLogOperationsMutex.withLock {
                val operationalContext = getOperationalContext()

                Sentry.withScope { scope ->
                    scope.setTag("context_type", "auth_operations")
                    scope.setTag("authServiceBaseUrl", service<ZencoderSettings>().authBaseUrl)
                    scope.setTag("anonymousId", service<AnalyticsService>().anonymousId)

                    operationalContext.forEach { (key, value) ->
                        scope.setExtra("au_context.$key", value)
                    }

                    val event = SentryEvent(exception).apply {
                        this.level = SentryLevel.ERROR
                        this.message = Message().apply {
                            this.message = "[Authentication error] ${exception.message?.maskFilteredWords()}"
                        }
                    }

                    Sentry.captureEvent(event)
                }
            }
        }
    }

    private fun getOperationalContext(): Map<String, String> {
        val allEvents = authenticationCallsLog.asMap().entries.sortedBy { it.key }
        val log = allEvents
            .mapIndexed { index, entry -> index to entry.value }
            .associate { (key, value) -> "log.$key" to value }
        val resultMap = mutableMapOf(
            "operations_total_count" to allEvents.size.toString(),
            "context_captured_at" to System.currentTimeMillis().toString()
        )
        resultMap.putAll(log)
        return resultMap
    }

    private fun String.maskFilteredWords(): String = replace("auth", "au_th")
        .replace("Auth", "Au_th")
        .replace("token", "to_ken")
        .replace("Token", "To_ken")
}
