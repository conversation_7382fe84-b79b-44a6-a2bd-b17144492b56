package ai.zencoder.plugin.settings

import ai.zencoder.generated.client.all.models.ZencoderWorkflowCommandConfirmationPolicy
import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.auth.AuthenticateButtonListener
import ai.zencoder.plugin.auth.ResetAuthenticationButtonListener
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.migration.applicationAppliedPatchesIds
import ai.zencoder.plugin.migration.appliedPatchesIds
import ai.zencoder.plugin.ui.settings.ListSettingsPanel
import ai.zencoder.plugin.utils.ZencoderApp
import ai.zencoder.plugin.utils.onTextChange
import ai.zencoder.plugin.utils.otherAppliedRunOnceActivitiesIds
import com.intellij.openapi.components.service
import com.intellij.openapi.options.ConfigurationException
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.ComboBox
import com.intellij.ui.TitledSeparator
import com.intellij.ui.components.JBCheckBox
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBPasswordField
import com.intellij.ui.components.JBSlider
import com.intellij.ui.components.JBTextField
import com.intellij.ui.components.panels.HorizontalLayout
import com.intellij.ui.dsl.builder.Align
import com.intellij.ui.dsl.builder.panel
import com.intellij.ui.layout.selected
import com.intellij.util.ui.FormBuilder
import com.intellij.util.ui.UIUtil
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.FlowLayout
import java.awt.event.ItemEvent
import java.util.Hashtable
import javax.swing.*

class ZencoderSettingsComponent(private val project: Project) {

    // the flags are used to track changes of the api token changes
    // we do it this way to avoid unnecessary calls to the password store, which are slow
    var wasOpenApiKeyModified = false
    var wasAnthropicApiKeyModified = false

    private val zencoderSettings get() = service<ZencoderSettings>()

    private val mainPanel: JPanel
    private val isOwnApiKeyEnabled = JBCheckBox(
        ZencoderBundle["settings.chat.llmChoice.ownApiKey"],
        zencoderSettings.isOwnApiKeyEnabled
    )
    private val chatLLMChoice = ComboBox(ChatLLMChoice.entries.map { it.value }.toTypedArray())
    private val openApiKey = JBPasswordField()
    private val anthropicApiKey = JBPasswordField()

    private val authenticationButton = JButton().apply {
        text = "Authenticate"
        addActionListener(AuthenticateButtonListener())
    }
    private val resetAuthenticationButton = JButton().apply {
        text = "Reset Authentication"
        addActionListener(ResetAuthenticationButtonListener())
    }

    private val shellToolConfirmationChoice = ComboBox(CommandConfirmationPolicy.entries.map { it.value }.toTypedArray())
    private val shellToolAllowedCommands =
        ListSettingsPanel(
            ZencoderBundle["settings.shellTool.commandsList.comment"],
            ZencoderBundle["settings.shellTool.commandsList.addMessage"]
        )

    private val codebaseIndexing =
        JBCheckBox(ZencoderBundle["settings.repoGrokking.codebaseIndexing"], zencoderSettings.codebaseIndexing)

    private val excludeByGitignore =
        JBCheckBox(ZencoderBundle["settings.repoGrokking.excludeByGitignore"], zencoderSettings.excludeByGitignore)

    private val excludeCustomFilesPatterns = ListSettingsPanel(
        ZencoderBundle["settings.repoGrokking.excludeCustomFiles"],
        ZencoderBundle["settings.repoGrokking.excludeCustomFiles.onAddMessage"]
    )

    private val enableInlineCodeCompletionCheckbox =
        JBCheckBox("Enable code completion", zencoderSettings.enableInlineCodeCompletion)
    private val enableMultiLineCompletionCheckbox =
        JBCheckBox("Enable Multi-Line completion", zencoderSettings.enableMultiLineCompletion)
    private val enableNextEditPredictionCheckbox =
        JBCheckBox("Enable Next Edit Prediction (beta)", zencoderSettings.enableNextEditPrediction)
    private val codeCompletionDebounceDelayModes: IntArray = intArrayOf(100, 200, 300, 400, 500, 600, 700, 800, 900, 1000)
    private val codeCompletionDebounceSlider = createCodeCompletionDebounceDelaySlider()
    private val isInlineSuggestionsEnabledCheckbox =
        JBCheckBox(ZencoderBundle.get("settings.editor.enableInlineSuggestions"), zencoderSettings.isInlineSuggestionsEnabled)

    private val enableShellToolCheckbox = JBCheckBox(
        "Enable the Bash tool for AI agents for execute shell commands",
        zencoderSettings.enableShellTool
    )
    private val isRequirementsToolEnabledCheckbox = JBCheckBox(
        "(Beta) Enable the requirements tool for AI agents to ask clarifying questions",
        zencoderSettings.isRequirementsToolEnabled
    )

    private val displayDebugInfoCheckbox = JBCheckBox(
        ZencoderBundle["settings.advanced.debug.info.title"],
        zencoderSettings.displayDebugInfo
    )

    private val isSentryEnabledCheckbox =
        JBCheckBox("Enable sentry", zencoderSettings.isSentryEnabled)
    private val isAnalyticsEnabledCheckbox =
        JBCheckBox("Enable analytics", zencoderSettings.isAnalyticsEnabled)

    private val applicationPatches = ListSettingsPanel("Application patches")
        .apply { setItems(applicationAppliedPatchesIds) }
    private val projectPatches = ListSettingsPanel("Project patches")
        .apply { setItems(project.appliedPatchesIds) }
    private val otherRunOnceActivities = ListSettingsPanel("Others")
        .apply { setItems(otherAppliedRunOnceActivitiesIds) }

    private val switchToDevButton = JButton().apply {
        text = "Switch to DEV"
        addActionListener { urlsConfigurator.switchToDev() }
    }
    private val switchToProdButton = JButton().apply {
        text = "Switch to PROD"
        addActionListener { urlsConfigurator.switchToProd() }
    }
    private val dynamicStagingIPTextField = JBTextField()
    private val switchToDynEnvButton = JButton().apply {
        text = "Switch to IP"
        addActionListener { urlsConfigurator.switchToDynamic(dynamicStagingIPTextField.text) }
    }

    private val authBaseUrl = JBTextField()
    private val apiBaseUrl = JBTextField()
    private val loginUrlTextField = JBTextField()
    private val signUpUrlTextField = JBTextField()
    private val agentsUrlTextField = JBTextField()
    private val codeGenUrlTextField = JBTextField()
    private val codeRepairUrlTextField = JBTextField()
    private val unitTestUrlTextField = JBTextField()
    private val feedbackServiceUrlTextField = JBTextField()
    private val chatServiceUrlTextField = JBTextField()
    private val repoUploadServiceUrlTextField = JBTextField()
    private val codeCompletionServiceUrlTextField = JBTextField()
    private val ablyUrlTextField = JBTextField()
    private val workflowServiceTextField = JBTextField()
    private val defaultServiceTextField = JBTextField()
    private val cloudStorageUrlTextField = JBTextField()
    // If you add additional URL here do not forget to update URlConfigurator

    private val urlsConfigurator = URlConfigurator()

    init {
        val mainPanelBuilder = FormBuilder.createFormBuilder()

        // Authentication section
        val authPanel = JPanel(HorizontalLayout(0, 1))
        authPanel.add(authenticationButton)
        authPanel.add(resetAuthenticationButton)
        mainPanelBuilder.addLabeledComponent(JBLabel("Authentication"), authPanel, 1, true)

        // Editor section
        mainPanelBuilder.addComponent(TitledSeparator("Editor"))
        mainPanelBuilder.addComponent(isInlineSuggestionsEnabledCheckbox)

        // Chat Assistant section
        mainPanelBuilder.addComponent(
            panel {
                group(ZencoderBundle["settings.chat.section.title"]) {
                    row { comment(ZencoderBundle["settings.chat.llmChoice.comment"]) }
                    row(ZencoderBundle["settings.chat.llmChoice.label"]) {
                        cell(chatLLMChoice).align(Align.FILL)
                    }
                    row { cell(isOwnApiKeyEnabled) }
                    group {
                        row { comment(ZencoderBundle["settings.chat.llmChoice.apiKey.comment"]) }
                        row(ZencoderBundle["settings.chat.llmChoice.openai.label"]) {
                            cell(openApiKey)
                                .align(Align.FILL)
                                .applyToComponent {
                                    onTextChange {
                                        wasOpenApiKeyModified = true
                                    }
                                }
                        }
                        row(ZencoderBundle["settings.chat.llmChoice.anthropic.label"]) {
                            cell(anthropicApiKey)
                                .align(Align.FILL)
                                .applyToComponent {
                                    onTextChange {
                                        wasAnthropicApiKeyModified = true
                                    }
                                }
                        }
                    }.visibleIf(isOwnApiKeyEnabled.selected)
                }
            }
        )

        mainPanelBuilder.addComponent(TitledSeparator(ZencoderBundle["settings.repoGrokking.title"]))
        mainPanelBuilder.addComponent(codebaseIndexing)
        mainPanelBuilder.addComponent(excludeByGitignore)
        mainPanelBuilder.addComponent(excludeCustomFilesPatterns)
        codebaseIndexing.addItemListener { event ->
            excludeByGitignore.isVisible = event.stateChange == ItemEvent.SELECTED
            excludeCustomFilesPatterns.isVisible = event.stateChange == ItemEvent.SELECTED
        }
        excludeByGitignore.isVisible = codebaseIndexing.isSelected
        excludeCustomFilesPatterns.isVisible = codebaseIndexing.isSelected

        mainPanelBuilder.addComponent(TitledSeparator("Code Completion"))
        mainPanelBuilder.addComponent(enableInlineCodeCompletionCheckbox)
        mainPanelBuilder.addComponent(enableMultiLineCompletionCheckbox)
        if (zencoderFeatureFlags.enableNextEditPredictions) {
            mainPanelBuilder.addComponent(enableNextEditPredictionCheckbox)
        }
        mainPanelBuilder.addLabeledComponent(
            ZencoderBundle["settings.codeCompletion.debounce.label"],
            JPanel(BorderLayout())
                .apply {
                    val sliderPanel = JPanel(FlowLayout(FlowLayout.LEFT))
                    sliderPanel.add(codeCompletionDebounceSlider)
                    codeCompletionDebounceSlider.preferredSize = Dimension(350, codeCompletionDebounceSlider.preferredSize.height)
                    sliderPanel.preferredSize = Dimension(400, sliderPanel.preferredSize.height)
                    add(sliderPanel, BorderLayout.WEST)
                }
        )

        mainPanelBuilder.addComponent(TitledSeparator("Tools Options"))
        mainPanelBuilder.addComponent(enableShellToolCheckbox)
        if (zencoderFeatureFlags.enableRequirementsTool) {
            mainPanelBuilder.addComponent(isRequirementsToolEnabledCheckbox)
        }

        mainPanelBuilder.addComponentFillVertically(JPanel(), 0)
        mainPanel = mainPanelBuilder.panel

        enableInlineCodeCompletionCheckbox.addItemListener { event ->
            enableMultiLineCompletionCheckbox.isVisible = event.stateChange == ItemEvent.SELECTED
            codeCompletionDebounceSlider.isVisible = event.stateChange == ItemEvent.SELECTED
            enableNextEditPredictionCheckbox.isVisible =
                event.stateChange == ItemEvent.SELECTED && zencoderFeatureFlags.enableNextEditPredictions
        }

        enableMultiLineCompletionCheckbox.isVisible = enableInlineCodeCompletionCheckbox.isSelected
        codeCompletionDebounceSlider.isVisible = enableInlineCodeCompletionCheckbox.isSelected
        enableNextEditPredictionCheckbox.isVisible =
            enableInlineCodeCompletionCheckbox.isSelected && zencoderFeatureFlags.enableNextEditPredictions

        mainPanelBuilder.addComponent(
            panel {
                group(ZencoderBundle["settings.shellTool.title"]) {
                    row { comment(ZencoderBundle["settings.shellTool.confirmationPolicy.comment"]) }
                    row(ZencoderBundle["settings.shellTool.confirmationPolicy.label"]) {
                        cell(shellToolConfirmationChoice).align(Align.FILL)
                    }
                    row {
                        cell(shellToolAllowedCommands).align(Align.FILL)
                    }
                }
            }
        )

        mainPanelBuilder.addComponent(
            panel {
                group(ZencoderBundle["settings.advanced.title"]) {
                    row { cell(displayDebugInfoCheckbox) }
                    row { comment(ZencoderBundle["settings.advanced.debug.info.description"]) }
                }
            }
        )

        // Dev settings section
        if (ZencoderApp.isDevMode) {
            mainPanelBuilder.addComponent(
                panel {
                    group("Dev Settings") {
                        row { cell(isSentryEnabledCheckbox) }
                        row { cell(isAnalyticsEnabledCheckbox) }

                        group("Run Once Activities") {
                            row { cell(applicationPatches).align(Align.FILL) }
                            row { cell(projectPatches).align(Align.FILL) }
                            row { cell(otherRunOnceActivities).align(Align.FILL) }
                        }

                        group("URL Management") {
                            val urlControlPanel = JPanel(HorizontalLayout(5, 0)).apply {
                                add(switchToDevButton)
                                add(switchToProdButton)
                                add(switchToDynEnvButton)
                                add(JLabel("Enter IP:"))
                                add(dynamicStagingIPTextField)
                            }
                            row { cell(urlControlPanel) }

                            row("Auth base URL") { cell(authBaseUrl).align(Align.FILL) }
                            row("API base URL") { cell(apiBaseUrl).align(Align.FILL) }
                            row("Sign in page") { cell(loginUrlTextField).align(Align.FILL) }
                            row("Sign up page") { cell(signUpUrlTextField).align(Align.FILL) }
                            row("Agents") { cell(agentsUrlTextField).align(Align.FILL) }
                            row("Code-gen") { cell(codeGenUrlTextField).align(Align.FILL) }
                            row("Code-repair") { cell(codeRepairUrlTextField).align(Align.FILL) }
                            row("Unit test") { cell(unitTestUrlTextField).align(Align.FILL) }
                            row("Feedback") { cell(feedbackServiceUrlTextField).align(Align.FILL) }
                            row("Chat") { cell(chatServiceUrlTextField).align(Align.FILL) }
                            row("Repo upload") { cell(repoUploadServiceUrlTextField).align(Align.FILL) }
                            row("Code completion") { cell(codeCompletionServiceUrlTextField).align(Align.FILL) }
                            row("Ably") { cell(ablyUrlTextField).align(Align.FILL) }
                            row("Workflow service") { cell(workflowServiceTextField).align(Align.FILL) }
                            row("General service") { cell(defaultServiceTextField).align(Align.FILL) }
                            row("Cloud storage") { cell(cloudStorageUrlTextField).align(Align.FILL) }
                        }
                    }
                }
            )
        }
    }

    fun createCodeCompletionDebounceDelaySlider(): JSlider = JBSlider().apply {
        UIUtil.setSliderIsFilled(this, true)
        paintLabels = true
        paintTicks = true
        paintTrack = true
        minimum = 0
        maximum = codeCompletionDebounceDelayModes.size - 1
        minorTickSpacing = 1
        majorTickSpacing = 1

        // Create custom labels with more space between them
        val labels = Hashtable<Int, JLabel>()
        codeCompletionDebounceDelayModes.indices.forEach { index ->
            val label = JLabel(codeCompletionDebounceDelayModes[index].toString())
            labels[index] = label
        }
        labelTable = labels
    }

    @Suppress("HttpUrlsUsage", "SpellCheckingInspection")
    inner class URlConfigurator {

        val configurableUrls = listOf(
            ServiceUrlConfigurable(
                "https://auth.zencoder.ai",
                "https://dev.auth.zencoder.ai",
                "https://dev.auth.zencoder.ai",
                authBaseUrl
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                apiBaseUrl
            ),
            ServiceUrlConfigurable(
                "https://auth.zencoder.ai",
                "https://dev.auth.zencoder.ai",
                "https://dev.auth.zencoder.ai",
                loginUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://auth.zencoder.ai/signup",
                "https://dev.auth.zencoder.ai/signup",
                "https://dev.auth.zencoder.ai/signup",
                signUpUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                agentsUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/codeedit",
                "https://dev.api.zencoder.ai/codeedit",
                "http://{ip}/codeedit",
                codeGenUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/repair",
                "https://dev.api.zencoder.ai/repair",
                "http://{ip}/repair",
                codeRepairUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/unittests",
                "https://dev.api.zencoder.ai/unittests",
                "http://{ip}/unittests",
                unitTestUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/feedback-service",
                "https://dev.api.zencoder.ai/feedback-service",
                "http://{ip}/feedback-service",
                feedbackServiceUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/chat",
                "https://dev.api.zencoder.ai/chat",
                "http://{ip}/chat",
                chatServiceUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/repo-upload-service",
                "https://dev.api.zencoder.ai/repo-upload-service",
                "http://{ip}/repo-upload-service",
                repoUploadServiceUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/code-completion-service",
                "https://dev.api.zencoder.ai/code-completion-service",
                "http://{ip}/code-completion-service",
                codeCompletionServiceUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai/transport-bus/auth_callback",
                "https://dev.api.zencoder.ai/transport-bus/auth_callback",
                "http://{ip}/transport-bus/auth_callback",
                ablyUrlTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                "http://{ip}",
                workflowServiceTextField
            ),
            ServiceUrlConfigurable(
                "https://api.zencoder.ai",
                "https://dev.api.zencoder.ai",
                "http://{ip}",
                defaultServiceTextField
            )
        )

        fun switchToProd() = configurableUrls.forEach { configurableUrl ->
            configurableUrl.uiFieldReference.text = configurableUrl.prodUrl
        }

        fun switchToDev() = configurableUrls.forEach { configurableUrl ->
            configurableUrl.uiFieldReference.text = configurableUrl.devUrl
        }

        fun switchToDynamic(ipAddress: String?) = configurableUrls.forEach { configurableUrl ->
            configurableUrl.uiFieldReference.text = configurableUrl.dynamicUrl.replace("{ip}", ipAddress.orEmpty())
        }
    }

    class ServiceUrlConfigurable(
        val prodUrl: String,
        val devUrl: String,
        val dynamicUrl: String,
        val uiFieldReference: JTextField
    )

    fun getPreferredFocusedComponent(): JComponent = authenticationButton

    fun getMainPanel(): JPanel {
        return mainPanel
    }

    fun getAuthBaseUrl(): String = authBaseUrl.text

    fun setAuthBaseUrl(url: String) {
        authBaseUrl.text = url
    }

    fun getApiBaseUrl(): String = apiBaseUrl.text

    fun setApiBaseUrl(url: String) {
        apiBaseUrl.text = url
    }

    fun getSignInUrl(): String = loginUrlTextField.text

    fun setSignInUrl(url: String) {
        loginUrlTextField.text = url
    }

    fun getSignUpUrl(): String = signUpUrlTextField.text

    fun setSignUpUrl(url: String) {
        signUpUrlTextField.text = url
    }

    fun getAgentsUrl(): String = agentsUrlTextField.text

    fun setAgentsUrl(url: String) {
        agentsUrlTextField.text = url
    }

    fun getCodeGenUrl(): String = codeGenUrlTextField.text

    fun setCodeGenUrl(url: String) {
        codeGenUrlTextField.text = url
    }

    fun getCodeRepairUrl(): String = codeRepairUrlTextField.text

    fun setCodeRepairUrl(url: String) {
        codeRepairUrlTextField.text = url
    }

    fun getUnitTestUrl(): String = unitTestUrlTextField.text

    fun setUnitTestUrl(url: String) {
        unitTestUrlTextField.text = url
    }

    fun getFeedbackUrl(): String = feedbackServiceUrlTextField.text

    fun setFeedbackUrl(url: String) {
        feedbackServiceUrlTextField.text = url
    }

    fun getChatUrl(): String = chatServiceUrlTextField.text

    fun setChatUrl(url: String) {
        chatServiceUrlTextField.text = url
    }

    fun getRepoUploadUrl(): String = repoUploadServiceUrlTextField.text

    fun setRepoUploadUrl(url: String) {
        repoUploadServiceUrlTextField.text = url
    }

    fun getCodeCompletionUrl(): String = codeCompletionServiceUrlTextField.text

    fun setCodeCompletionUrl(url: String) {
        codeCompletionServiceUrlTextField.text = url
    }

    fun getAblyUrl(): String = ablyUrlTextField.text

    fun setAblyUrl(url: String) {
        ablyUrlTextField.text = url
    }

    fun getCloudStorageUrl(): String = cloudStorageUrlTextField.text

    fun setCloudStorageUrl(url: String) {
        cloudStorageUrlTextField.text = url
    }

    fun enabledRepoIndexing() = codebaseIndexing.isSelected

    fun setEnabledRepoIndexing(mode: Boolean) {
        codebaseIndexing.isSelected = mode
    }

    fun isExcludedByGitIgnore() = excludeByGitignore.isSelected

    fun setExcludeByGitignore(mode: Boolean) {
        excludeByGitignore.isSelected = mode
    }

    fun getExcludeFilePatterns(): Set<String> {
        return excludeCustomFilesPatterns.getItems().toSet()
    }

    fun setExcludeFilePatterns(items: Set<String>) {
        return excludeCustomFilesPatterns.setItems(items.toList())
    }

    fun enabledInlineCodeCompletion() = enableInlineCodeCompletionCheckbox.isSelected

    fun setEnabledInlineCodeCompletion(mode: Boolean) {
        enableInlineCodeCompletionCheckbox.isSelected = mode
    }

    fun enabledMultiLineCompletion() = enableMultiLineCompletionCheckbox.isSelected

    fun setEnabledMultilineCompletion(mode: Boolean) {
        enableMultiLineCompletionCheckbox.isSelected = mode
    }

    fun enabledNextEditPrediction() = enableNextEditPredictionCheckbox.isSelected

    fun setEnabledNextEditPrediction(mode: Boolean) {
        enableNextEditPredictionCheckbox.isSelected = mode
    }

    fun isInlineSuggestionsEnabled() = isInlineSuggestionsEnabledCheckbox.isSelected

    fun setInlineSuggestionsEnabled(isSelected: Boolean) {
        isInlineSuggestionsEnabledCheckbox.isSelected = isSelected
    }

    fun isSentryEnabled() = isSentryEnabledCheckbox.isSelected

    fun setSentryEnabled(isSelected: Boolean) {
        isSentryEnabledCheckbox.isSelected = isSelected
    }

    fun isAnalyticsEnabled() = isAnalyticsEnabledCheckbox.isSelected

    fun setAnalyticsEnabled(isSelected: Boolean) {
        isAnalyticsEnabledCheckbox.isSelected = isSelected
    }

    fun enabledShellTool() = enableShellToolCheckbox.isSelected

    fun setEnabledShellTool(isSelected: Boolean) {
        enableShellToolCheckbox.isSelected = isSelected
    }

    fun isRequirementsToolEnabled() = isRequirementsToolEnabledCheckbox.isSelected

    fun setRequirementsToolEnabled(isSelected: Boolean) {
        isRequirementsToolEnabledCheckbox.isSelected = isSelected
    }

    fun getWorkflowServiceUrl() = workflowServiceTextField.text

    fun setWorkflowServiceUrl(url: String) {
        workflowServiceTextField.text = url
    }

    fun getDefaultServiceUrl() = defaultServiceTextField.text

    fun setDefaultServiceUrl(url: String) {
        defaultServiceTextField.text = url
    }

    fun isOwnApiKeyEnabled() = isOwnApiKeyEnabled.isSelected

    fun setOwnApiKeyEnabled(isSelected: Boolean) {
        isOwnApiKeyEnabled.isSelected = isSelected
    }

    fun getChatLLMChoice(): ChatLLMChoice {
        return ChatLLMChoice.fromValue(chatLLMChoice.selectedItem as String)
    }

    fun setChatLLMChoice(llmChoice: ChatLLMChoice) {
        chatLLMChoice.selectedItem = llmChoice.value
    }

    fun getOpenApiKey(): String {
        return String(openApiKey.password)
    }

    fun setOpenApiKey(apiKey: String) {
        openApiKey.text = apiKey
    }

    fun getAnthropicApiKey(): String {
        return String(anthropicApiKey.password)
    }

    fun setAnthropicApiKey(apiKey: String) {
        anthropicApiKey.text = apiKey
    }

    fun setAllowedShellToolCommands(commands: Set<String>) {
        shellToolAllowedCommands.setItems(commands.toList())
    }

    fun getShellToolAllowedCommands(): Set<String> {
        return shellToolAllowedCommands.getItems().toSet()
    }

    fun setShellToolConfirmationPolicy(policy: CommandConfirmationPolicy) {
        shellToolConfirmationChoice.selectedItem = policy.value
    }

    fun getShellToolConfirmationPolicy(): CommandConfirmationPolicy {
        return CommandConfirmationPolicy.fromValue(shellToolConfirmationChoice.selectedItem as String)
    }

    fun getDisplayDebugInfo() = displayDebugInfoCheckbox.isSelected

    fun setDisplayDebugInfo(enabled: Boolean) {
        displayDebugInfoCheckbox.isSelected = enabled
    }

    fun getApplicationPatches() = applicationPatches.getItems()

    fun setApplicationPatches(items: List<String>) {
        applicationPatches.setItems(items)
    }

    fun getProjectPatches() = projectPatches.getItems()

    fun setProjectPatches(items: List<String>) {
        projectPatches.setItems(items)
    }

    fun getOtherRunOnceActivities() = otherRunOnceActivities.getItems()

    fun setOtherRunOnceActivities(items: List<String>) {
        otherRunOnceActivities.setItems(items)
    }

    fun getCodeCompletionDebounceDelay(): Int = codeCompletionDebounceDelayModes[codeCompletionDebounceSlider.value]

    fun setCodeCompletionDebounceDelay(delay: Int) {
        codeCompletionDebounceSlider.value = codeCompletionDebounceDelayModes.indexOf(delay)
    }

    @Throws(ConfigurationException::class)
    fun validate() {
    }
}

enum class CommandConfirmationPolicy(val value: String) {
    ALWAYS_CONFIRM("Always ask"),
    NEVER_CONFIRM("Never ask"),
    AS_CONFIGURED("Don’t ask for commands below");

    companion object {
        fun fromValue(value: String): CommandConfirmationPolicy {
            return CommandConfirmationPolicy.entries.firstOrNull { it.value == value } ?: ALWAYS_CONFIRM
        }
    }
}

fun CommandConfirmationPolicy.toZencoderPolicy(): ZencoderWorkflowCommandConfirmationPolicy {
    return when (this) {
        CommandConfirmationPolicy.ALWAYS_CONFIRM -> ZencoderWorkflowCommandConfirmationPolicy.ALWAYS_ASK
        CommandConfirmationPolicy.NEVER_CONFIRM -> ZencoderWorkflowCommandConfirmationPolicy.NEVER_ASK
        CommandConfirmationPolicy.AS_CONFIGURED -> ZencoderWorkflowCommandConfirmationPolicy.DO_NOT_ASK_FOR_SPECIFIED
    }
}

enum class ChatLLMChoice(val value: String) {
    DEFAULT("Zencoder custom"),
    GPT_4O("GPT-4o"),
    CLAUDE_3_5("Claude 3.5"),
    CLAUDE_3_7("Claude 3.7");

    companion object {
        fun fromValue(value: String): ChatLLMChoice {
            return entries.firstOrNull { it.value == value } ?: default()
        }

        fun default() = DEFAULT
    }
}
