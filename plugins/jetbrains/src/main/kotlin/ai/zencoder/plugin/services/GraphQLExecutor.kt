package ai.zencoder.plugin.services

import ai.zencoder.plugin.agents.AgentTool
import ai.zencoder.plugin.agents.CustomAgentsManager
import ai.zencoder.plugin.context.ContextGatherService
import ai.zencoder.plugin.context.FileContentAndPaths
import ai.zencoder.plugin.context.NodeType
import ai.zencoder.plugin.context.SimpleASTNode
import ai.zencoder.plugin.diagnostics.BackupService
import ai.zencoder.plugin.diagnostics.DiagnosticsRunner
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.graphql.generated.api.MutationResolver
import ai.zencoder.plugin.graphql.generated.api.QueryResolver
import ai.zencoder.plugin.graphql.generated.models.AstNode
import ai.zencoder.plugin.graphql.generated.models.AstNodeFilter
import ai.zencoder.plugin.graphql.generated.models.AstNodeKind
import ai.zencoder.plugin.graphql.generated.models.ChatMessage
import ai.zencoder.plugin.graphql.generated.models.Context
import ai.zencoder.plugin.graphql.generated.models.DiagnosticsFile
import ai.zencoder.plugin.graphql.generated.models.DiagnosticsRequest
import ai.zencoder.plugin.graphql.generated.models.DiagnosticsResult
import ai.zencoder.plugin.graphql.generated.models.FileContent
import ai.zencoder.plugin.graphql.generated.models.FileMetadata
import ai.zencoder.plugin.graphql.generated.models.FileOperationResult
import ai.zencoder.plugin.graphql.generated.models.MessageContext
import ai.zencoder.plugin.graphql.generated.models.SourceFile
import ai.zencoder.plugin.mcp.MCPMessageDispatcher
import ai.zencoder.plugin.model.path.RelativePathFromProjectRoot
import ai.zencoder.plugin.observers.graphql.AgentCommunicationsService
import ai.zencoder.plugin.rag.VCSManager
import ai.zencoder.plugin.settings.zencoderSettings
import ai.zencoder.plugin.utils.calculateRelativePath
import ai.zencoder.plugin.utils.findFileByRelativePath
import ai.zencoder.plugin.utils.resolve
import ai.zencoder.plugin.utils.uuid
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.chat.ChatWebviewManager
import ai.zencoder.plugin.webview.model.ChatAttachedFile
import ai.zencoder.plugin.webview.model.ChatMessageContext
import ai.zencoder.plugin.webview.model.ChatMessageRole
import ai.zencoder.plugin.webview.model.ChatModel
import ai.zencoder.plugin.webview.model.MessagePart
import ai.zencoder.plugin.webview.model.PostedMessage.SetActiveChat
import com.fasterxml.jackson.databind.node.ObjectNode
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.application.writeAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VfsUtilCore
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.util.application
import graphql.ExecutionInput
import graphql.ExecutionResult
import graphql.GraphQL
import graphql.kickstart.tools.SchemaParserBuilder
import graphql.scalars.ExtendedScalars
import graphql.schema.GraphQLScalarType
import graphql.schema.GraphQLSchema
import io.sentry.Sentry
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.put
import java.io.BufferedReader
import java.io.File
import ai.zencoder.plugin.webview.model.ChatMessage as WebviewChatMessage

@Service(PROJECT)
class GraphQLExecutor(val project: Project) {

    private val graphQL: GraphQL

    init {
        val builder = SchemaParserBuilder()
        builder.scalars(
            GraphQLScalarType.newScalar(ExtendedScalars.Json)
                .name("JSONObject")
                .build()
        )
        val schemaContent = javaClass.getResourceAsStream("/graphql/schema.graphql")
            .bufferedReader()
            .use(BufferedReader::readText)
        builder.schemaString(schemaContent)

        val resolvers = mutableListOf(
            project.service<AgentOperationsResolver>()
        )
        val schemaParser = builder.resolvers(resolvers).build()
        val graphQLSchema: GraphQLSchema = schemaParser.makeExecutableSchema()
        graphQL = GraphQL.newGraphQL(graphQLSchema).build()
    }

    fun execute(query: String): ExecutionResult = graphQL.execute(query)

    fun execute(executionInput: ExecutionInput): ExecutionResult = graphQL.execute(executionInput)
}

@Service(PROJECT)
class AgentOperationsResolver(val project: Project) :
    QueryResolver,
    MutationResolver {

    private val contextGatherService get() = service<ContextGatherService>()
    private val chatManager get() = project.service<ChatManager>()
    private val chatWebviewManager get() = project.service<ChatWebviewManager>()
    private val vcsManager get() = project.service<VCSManager>()
    private val agentCommunicationsService get() = project.service<AgentCommunicationsService>()
    private val chatCodeChangesTracker = project.service<ChatCodeChangesTracker>()
    private val lastChatMessageReferences = mutableMapOf<String, WebviewChatMessage>()

    override suspend fun context(operationId: String): Context? {
        val context = agentCommunicationsService.ongoingOperationsCache.getIfPresent(operationId)?.initialOperationIdeState
        if (context == null) {
            Sentry.captureException(Exception("Chat is null for operation id $operationId"))
            thisLogger().error("Chat is null for operation id $operationId")
        }
        return context
    }

    override suspend fun astNodes(astNodeFilters: List<AstNodeFilter>): List<AstNode> {
        return astNodeFilters.flatMap { filter ->
            if (filter.offset != null) {
                val psiElement = contextGatherService.getCodeSymbol(project, filter)
                val astNode = runReadAction {
                    psiElement?.run {
                        AstNode(
                            path = filter.path,
                            name = "Unknown",
                            kind = AstNodeKind.Unknown,
                            body = psiElement.text,
                            offset = psiElement.textOffset,
                            children = emptyList()
                        )
                    }
                }
                listOfNotNull(astNode)
            } else {
                contextGatherService
                    .gatherAstFile(project, project.calculateRelativePath(filter.path))
                    .map { it.node.toAstNode(filter.path) }
            }
        }
    }

    override suspend fun diagnostics(diagnosticsRequest: DiagnosticsRequest): List<DiagnosticsResult> {
        val backupService = project.service<BackupService>()
        val filePaths = diagnosticsRequest.files.map { RelativePathFromProjectRoot(it.path) }
        val backup = backupService.backupFiles(filePaths)
        try {
            return diagnosticsRequest.files.flatMap { fileToDiagnose ->
                fileDiagnostics(fileToDiagnose)
            }
        } finally {
            backupService.restoreBackup(backup)
        }
    }

    override suspend fun listTools(): List<String> {
        return sequenceOf(
            ToolConfig(AgentTool.SHELL, isEnabledByUser = zencoderSettings.enableShellTool),
            ToolConfig(AgentTool.DIAGNOSTICS, isEnabledByFlag = zencoderFeatureFlags.diagnosticsTool),
            ToolConfig(
                AgentTool.REQUIREMENTS_TOOL,
                isEnabledByFlag = zencoderFeatureFlags.enableRequirementsTool,
                isEnabledByUser = zencoderSettings.isRequirementsToolEnabled
            )
        )
            .filter { it.isEnabledByFlag ?: true }
            .filter { it.isEnabledByUser ?: true }
            .map { it.tool.name }
            .toList()
    }

    private data class ToolConfig(
        val tool: AgentTool,
        val isEnabledByFlag: Boolean? = null,
        val isEnabledByUser: Boolean? = null
    )

    private suspend fun fileDiagnostics(fileToDiagnose: DiagnosticsFile): List<DiagnosticsResult> {
        val fileService = project.service<FileService>()
        val file = File(fileToDiagnose.path)
        val psiFile = fileService.refreshFileAndWaitSilentlyForActualUpdateWithNio(
            RelativePathFromProjectRoot(file.path),
            fileToDiagnose.content
        )
        val startLine = fileToDiagnose.range?.startLine ?: 0
        val endLine = fileToDiagnose.range?.endLine ?: fileToDiagnose.content.lines().size
        return DiagnosticsRunner.of(project, psiFile).runDiagnostics()
            .filter { it.range.startLine >= startLine }
            .filter { it.range.endLine <= endLine }
            .toList()
    }

    override suspend fun findFiles(pattern: String): List<SourceFile> {
        val filteredFiles = mutableSetOf<VirtualFile>()
        LocalFileSystem.getInstance().findFileByPath(project.basePath!!)?.let { baseDir ->
            VfsUtilCore.iterateChildrenRecursively(
                baseDir,
                { virtualFile -> virtualFile.path.contains(pattern, ignoreCase = true) },
                { virtualFile ->
                    filteredFiles.add(virtualFile)
                    true
                }
            )
        }
        return filteredFiles.mapNotNull {
            contextGatherService.gatherFilePathsAndContent(project, project.calculateRelativePath(it.path))
                ?.toSourceFile()
        }
    }

    override suspend fun files(paths: List<String>): List<SourceFile> {
        return paths.mapNotNull { path ->
            contextGatherService
                .gatherFilePathsAndContent(project, RelativePathFromProjectRoot(path))
                ?.toSourceFile()
        }.toList()
    }

    override suspend fun mcp(request: String): String {
        return project.service<MCPMessageDispatcher>().dispatch(request)
    }

    override suspend fun fileMetadata(path: String): FileMetadata? {
        val fileMetadata = project.findFileByRelativePath(RelativePathFromProjectRoot(path))?.let {
            FileMetadata(
                path = path,
                isDirectory = it.isDirectory
            )
        }
        return fileMetadata
    }

    override suspend fun listFiles(directoryPath: String, depth: Int): List<FileMetadata>? {
        val directory = project.findFileByRelativePath(RelativePathFromProjectRoot(directoryPath)) ?: return null
        return vcsManager.vcsService.listDirectoryFiltered(directory, depth)
            .map<VirtualFile, FileMetadata> {
                FileMetadata(
                    directory.toNioPath().relativize(it.toNioPath()).toString(),
                    it.isDirectory
                )
            }.toList()
    }

    private fun SimpleASTNode.toAstNode(path: String): AstNode = AstNode(
        path = path,
        name = name,
        kind = when (type) {
            NodeType.Class -> AstNodeKind.Class
            NodeType.Method -> AstNodeKind.Method
            NodeType.Property -> AstNodeKind.Property
            NodeType.Function -> AstNodeKind.Function
        },
        offset = application.runReadAction<Int> { psiElement.textOffset },
        children = children.map { it.toAstNode(path) },
        body = application.runReadAction<String> { psiElement.text.orEmpty() }
    )

    override suspend fun createMessage(
        operationId: String,
        message: String,
        messageContext: MessageContext?
    ): String {
        val chatId = findChatForOperationId(operationId) ?: return ""
        val chat: ChatModel = chatManager.getChatById(chatId)
        val context = ChatMessageContext(author = chat.author()).merge(messageContext?.toDomain(project))
        val messageObj = textMessageOf(message, context)

        saveLastMessageIfNotNullOrJustRefreshChat(operationId)
        addMessageAndRememberLastState(
            operationId = operationId,
            chat = chat.copy(messages = chat.messages.toMutableList()),
            message = messageObj,
            sendChat = { chatModel, _ ->
                chatWebviewManager.send(
                    SetActiveChat(
                        chat = chatModel,
                        isStreaming = true,
                        navigateTo = null
                    )
                )
            },
            isPermanent = false
        )
        return messageObj.id.orEmpty()
    }

    override suspend fun updateMessage(
        operationId: String,
        messageId: String,
        message: String
    ): String {
        val chatId = findChatForOperationId(operationId) ?: return ""
        val chat: ChatModel = chatManager.getChatById(chatId)
        val lastMessage = lastChatMessageReferences[operationId] ?: return ""
        addMessageAndRememberLastState(
            operationId = operationId,
            chat = chat.copy(messages = chat.messages.toMutableList()),
            message = lastMessage.copy(content = listOf(MessagePart.Text(message))),
            sendChat = { chatModel, _ ->
                chatWebviewManager.send(
                    SetActiveChat(
                        chat = chatModel,
                        isStreaming = true,
                        navigateTo = null
                    )
                )
            },
            isPermanent = false
        )
        return messageId
    }

    override suspend fun updateMessageContext(
        operationId: String,
        messageId: String,
        messageContext: MessageContext
    ): String {
        val chatId = findChatForOperationId(operationId) ?: return ""
        val chat: ChatModel = chatManager.getChatById(chatId)
        val lastMessage = lastChatMessageReferences[operationId] ?: return ""
        addMessageAndRememberLastState(
            operationId = operationId,
            chat = chat.copy(messages = chat.messages.toMutableList()),
            message = lastMessage.copy(
                context = lastMessage.context?.merge(messageContext.toDomain(project))
                    ?: messageContext.toDomain(project)
            ),
            sendChat = { chatModel, _ ->
                chatWebviewManager.send(
                    SetActiveChat(
                        chat = chatModel,
                        isStreaming = true,
                        navigateTo = null
                    )
                )
            },
            isPermanent = false
        )
        return messageId
    }

    override suspend fun renderComponent(
        operationId: String,
        componentName: String,
        props: ObjectNode
    ): String {
        saveLastMessageIfNotNullOrJustRefreshChat(operationId)
        val chatId = findChatForOperationId(operationId) ?: return ""
        val chat: ChatModel = chatManager.getChatById(chatId)
        val message = uiComponentMessageOf(
            componentName,
            Json.parseToJsonElement(props.toString()).jsonObject,
            ChatMessageContext(
                author = chat.author()
            )
        )
        addMessageAndRememberLastState(
            operationId = operationId,
            chat = chat.copy(messages = chat.messages.toMutableList()),
            message = message,
            sendChat = { chatModel, _ ->
                chatWebviewManager.send(
                    SetActiveChat(
                        chat = chatModel,
                        isStreaming = true,
                        navigateTo = null
                    )
                )
            },
            isPermanent = true
        )
        return message.id.orEmpty()
    }

    override suspend fun updateComponent(
        operationId: String,
        messageId: String,
        componentName: String,
        props: ObjectNode
    ): String {
        val chatId = findChatForOperationId(operationId) ?: return ""
        val chat: ChatModel = chatManager.getChatById(chatId)
        val message = uiComponentMessageOf(
            componentName,
            Json.parseToJsonElement(props.toString()).jsonObject,
            ChatMessageContext(
                author = chat.author()
            )
        )
        addMessageAndRememberLastState(
            operationId = operationId,
            chat = chat.copy(messages = chat.messages.toMutableList()),
            message = message,
            sendChat = { chatModel, _ ->
                chatWebviewManager.send(
                    SetActiveChat(
                        chat = chatModel,
                        isStreaming = true,
                        navigateTo = null
                    )
                )
            },
            isPermanent = false
        )
        return messageId
    }

    @Deprecated("update createOrUpdateFilesV2 instead")
    override suspend fun createOrUpdateFiles(files: List<FileContent>): List<FileOperationResult> {
        val fileService = project.service<FileService>()
        val fileOperationResults = files.map { fileContent ->
            val filePath = project.calculateRelativePath(fileContent.path)
            try {
                val updatedFile = fileService.refreshFileAndWaitSilentlyForActualUpdateWithNio(
                    filePath,
                    fileContent.content
                )
                writeAction {
                    FileEditorManager.getInstance(project).openFile(updatedFile.virtualFile)
                }
                FileOperationResult(fileContent.path, true, null)
            } catch (e: Exception) {
                FileOperationResult(fileContent.path, false, e.message)
            }
        }
        invokeLater {
            FileDocumentManager.getInstance().saveAllDocuments() // to make local history memorize changes, required for rollback
        }
        return fileOperationResults
    }

    @Suppress("DuplicatedCode")
    override suspend fun createOrUpdateFilesV2(operationId: String, files: List<FileContent>): List<FileOperationResult> {
        val fileService = project.service<FileService>()
        val fileOperationResults = files.map { fileContent ->
            val filePath = project.calculateRelativePath(fileContent.path)
            try {
                val chatId = findChatForOperationId(operationId)
                chatId?.let {
                    val forChat = chatCodeChangesTracker.forChat(it)
                    if (!forChat.isFileAlreadyTracked(fileContent.path)) {
                        val relativePath = project.calculateRelativePath(fileContent.path)
                        val currentContent = project.service<FileService>().getFileContentAsString(relativePath)
                        val isNew = currentContent == null || currentContent.isBlank()
                        forChat.trackInitialFileContent(fileContent.path, currentContent, isNew)
                    }
                }
                val updatedFile = fileService.refreshFileAndWaitSilentlyForActualUpdateWithNio(
                    filePath,
                    fileContent.content
                )
                chatId?.let {
                    chatCodeChangesTracker
                        .forChat(it)
                        .trackLatestFileContent(fileContent.path, fileContent.content)
                }
                invokeLater {
                    FileEditorManager.getInstance(project).openFile(updatedFile.virtualFile)
                }
                FileOperationResult(fileContent.path, true, null)
            } catch (e: Exception) {
                FileOperationResult(fileContent.path, false, e.message)
            }
        }
        invokeLater {
            FileDocumentManager.getInstance().saveAllDocuments() // to make local history memorize changes, required for rollback
        }
        return fileOperationResults
    }

    @Deprecated("Does nothing", replaceWith = ReplaceWith("AgentCommunicationsService")) // For removal
    override suspend fun endOperation(operationId: String, errorMessage: String?): Boolean {
        return true
    }

    private fun textMessageOf(text: String, messageContext: ChatMessageContext? = null) = WebviewChatMessage(
        id = uuid(),
        role = ChatMessageRole.ASSISTANT,
        content = listOf(MessagePart.Text(text)),
        isInternal = false,
        createdAt = System.currentTimeMillis(),
        origin = null,
        context = messageContext
    )

    private fun uiComponentMessageOf(
        componentName: String,
        props: JsonObject,
        messageContext: ChatMessageContext? = null
    ) = WebviewChatMessage(
        id = uuid(),
        role = ChatMessageRole.ASSISTANT,
        content = listOf(
            MessagePart.UiComponent(
                MessagePart.Component(
                    name = componentName,
                    props = props
                )
            )
        ),
        isInternal = false,
        createdAt = System.currentTimeMillis(),
        origin = null,
        context = messageContext
    )

    private fun saveLastMessageIfNotNullOrJustRefreshChat(operationId: String) {
        val lastMessage = lastChatMessageReferences[operationId]
        val chatId = findChatForOperationId(operationId) ?: return // already stopped
        val chat: ChatModel = chatManager.getChatById(chatId)
        if (lastMessage != null) {
            chatManager.addMessageToChat(
                chat = chat.copy(messages = chat.messages.toMutableList()),
                message = lastMessage,
                sendChat = { chatModel, _ ->
                    chatWebviewManager.send(
                        SetActiveChat(
                            chat = chatModel,
                            isStreaming = false,
                            navigateTo = null
                        )
                    )
                },
                isPermanent = true
            )
            lastChatMessageReferences.remove(operationId)
        } else {
            chatWebviewManager.send(
                SetActiveChat(
                    chat = chat,
                    isStreaming = false,
                    navigateTo = null
                )
            )
        }
    }

    private fun findChatForOperationId(operationId: String): String? = chatManager.findChatIdForOperationId(operationId)

    private fun addMessageAndRememberLastState(
        operationId: String,
        chat: ChatModel,
        message: WebviewChatMessage,
        sendChat: (ChatModel, Boolean) -> Unit,
        isPermanent: Boolean = true,
        isTypingDisabled: Boolean = false
    ) {
        lastChatMessageReferences[operationId] = message
        chatManager.addMessageToChat(chat, message, sendChat, isPermanent, isTypingDisabled)
    }
}

fun MessageContext.toDomain(project: Project): ChatMessageContext {
    return ChatMessageContext(
        usedFilePaths = usedFilePaths?.map { usedFile ->
            ChatAttachedFile(usedFile.path, project.resolve(usedFile.path).toString(), emptyList())
        }
    )
}

fun ChatAttachedFile.toSourceFile(): SourceFile = SourceFile(
    path = this.path,
    fsPath = this.fsPath ?: "",
    content = this.content.orEmpty(),
    language = null
)

fun WebviewChatMessage.asChatHistoryMessage(): ChatMessage? {
    val content = this.content.mapNotNull { it.historyText }.filter { it.isNotBlank() }.joinToString("\n")
    return if (content.isEmpty()) {
        null
    } else {
        val attachments = this.context?.attachedFiles?.map { it.toSourceFile() }.orEmpty().toMutableList()
        if (this.context?.currentFile != null) {
            attachments += this.context!!.currentFile!!.toSourceFile()
        }
        ChatMessage(
            messageId = id,
            content = content,
            role = this.role.name.lowercase(),
            attachments = attachments,
            codebaseEnabled = this.context?.codebaseEnabled
        )
    }
}

fun MessagePart.ToolCall.toResult(): String? {
    return localResult?.let {
        val json = buildJsonObject {
            put("toolCallId", toolCallId)
            put("localResult", localResult)
            put("status", status.name)
        }
        return "<tool_call_result>\n$json\n</tool_call_result>"
    }
}

private const val AGENT_THOUGHTS_UI_COMPONENT = "AgentThoughts"

private val MessagePart.historyText: String?
    get() {
        when (this) {
            is MessagePart.Text -> return this.text
            is MessagePart.CodeSnippet -> return "```${this.language ?: "plaintext"}\n${this.text}\n```"
            // TODO: This is a hack to extract AI assistant messages from the AgentThoughts and GroupedChecklist component
            // It will go away once we have a better way to represent agent's thoughts (tbd shortly)
            is MessagePart.UiComponent -> {
                when (this.component.name) {
                    AGENT_THOUGHTS_UI_COMPONENT -> {
                        try {
                            val thoughts = this.component.props["thoughts"]?.jsonArray
                            return thoughts?.mapNotNull { it.jsonObject["text"]?.jsonPrimitive?.content }?.joinToString("\n")
                        } catch (e: Exception) {
                            thisLogger().warn("Unable to parse AgentThoughts component props", e)
                            return null
                        }
                    }

                    else -> return null
                }
            }

            is MessagePart.ToolCall -> return this.toResult()
            is MessagePart.SlashCommand -> {
                val agent = customAgentId?.let { service<CustomAgentsManager>().findBy(it) }
                if (agent != null) {
                    return "/custom_agent ${agent.command}"
                }
                if (commandName == "unittests") {
                    return "/unittests"
                }
                return commandName
            }

            is MessagePart.JiraIssue -> return "Jira issue: ${this.summary} Issue description:${this.description}"
            is MessagePart.FileMention -> return "File: ${this.path}"
            is MessagePart.GroupedChecklist,
            is MessagePart.ShellCommand,
            is MessagePart.Component,
            is MessagePart.ErrorText,
            is MessagePart.WarningText,
            is MessagePart.LoadingMarker,
            is MessagePart.Memory,
            is MessagePart.ApplyMessage,
            is MessagePart.CodePatch,
            is MessagePart.LoadingPart,
            is MessagePart.CodeHunk,
            is MessagePart.Unknown -> return null
        }
    }

fun FileContentAndPaths.toSourceFile(): SourceFile = SourceFile(
    path = this.relativePath,
    fsPath = this.absolutePath,
    content = this.content,
    language = this.language
)
