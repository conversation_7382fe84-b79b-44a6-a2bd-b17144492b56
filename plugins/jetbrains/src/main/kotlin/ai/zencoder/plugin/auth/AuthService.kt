package ai.zencoder.plugin.auth

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNames
import java.util.*

class NoAuthInfoException(message: String? = null) : RuntimeException(message)

class TemporaryNoAuthInfoException : RuntimeException()

val AuthService.userId get() = userDataOrNull()?.id
val AuthService.isFreePlanUser get() = userDataOrNull()?.isFreePlan ?: true

interface AuthService {
    @get:Throws(NoAuthInfoException::class)
    val accessToken
        get() = authInfo.accessToken

    @get:Throws(NoAuthInfoException::class)
    val authInfo: AuthInfo
        get() = authInfoOrNull() ?: throw NoAuthInfoException("Auth info not found")

    fun authInfoOrNull(): AuthInfo?

    fun userDataOrNull() = authInfoOrNull()?.userData

    fun signIn()

    fun signUp()

    fun isAuthenticated(): Boolean

    fun resetAuthentication()

    fun refreshAuthentication(expiredAccessToken: String): AuthInfo
}

@FunctionalInterface
interface AuthInfoReceiver {
    fun receive(authInfo: AuthInfo)
}

data class AuthInfo(
    val accessToken: String,
    val refreshToken: String
) {
    val userData: UserData

    init {
        userData = try {
            val decoder = Base64.getUrlDecoder()
            val chunks = accessToken.split('.')
            if (chunks.size >= 2) {
                val jsonString = String(decoder.decode(chunks[1]))
                PARTIAL_JSON.decodeFromString<UserData>(jsonString)
            } else {
                UserData("", "", "", null)
            }
        } catch (e: Exception) {
            UserData("", "", "", null)
        }
    }
}

const val FREE_PLAN_NAME = "Free" // to be renamed, then replace for enum

@OptIn(ExperimentalSerializationApi::class)
@Serializable
data class UserData(
    @JsonNames("sub") val id: String,
    @JsonNames("name") val name: String,
    @JsonNames("email") val email: String,
    @JsonNames("permissions") val permissions: List<String>? = null,
    @JsonNames("roles") val zencoderRoles: List<String>? = null,
    val customClaims: CustomClaims? = null
) {
    @Serializable
    data class CustomClaims(
        @JsonNames("plan") val plan: String? = null,
        @JsonNames("isTrial") val isOnTrial: Boolean? = null,
        @JsonNames("permissions") val permissions: List<String>? = null
    )

    val plan: String? get() = customClaims?.plan
    val isFreePlan get() = plan == FREE_PLAN_NAME
    val isOnTrial: Boolean? get() = customClaims?.isOnTrial

    // Computed properties to match the TypeScript implementation
    val zencoderPermissions: List<String>
        get() {
            val allPermissions = mutableListOf<String>()

            if (permissions != null && permissions.isNotEmpty()) {
                allPermissions.addAll(permissions)
            }

            if (customClaims?.permissions != null && customClaims.permissions.isNotEmpty()) {
                allPermissions.addAll(customClaims.permissions)
            }

            return allPermissions
        }
}

val PARTIAL_JSON = Json { ignoreUnknownKeys = true }
