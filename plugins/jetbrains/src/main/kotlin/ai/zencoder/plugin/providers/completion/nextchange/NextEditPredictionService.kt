package ai.zencoder.plugin.providers.completion.nextchange

import ai.zencoder.generated.client.completion.models.CursorPosition
import ai.zencoder.generated.client.completion.models.EditPredictionRequest
import ai.zencoder.generated.client.completion.models.EditPredictionResponse
import ai.zencoder.generated.client.completion.models.FileState
import ai.zencoder.plugin.api.CodeCompletionService
import ai.zencoder.plugin.utils.calculateRelativePath
import com.intellij.history.core.collectChanges
import com.intellij.history.integration.LocalHistoryImpl
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiDocumentManager
import com.intellij.psi.PsiFile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@Service(Level.PROJECT)
class NextEditPredictionService(private val scope: CoroutineScope) {

    private data class FileChange(
        val timestamp: Long,
        val content: Any
    )

    private fun extractFileStatesFromLocalHistory(editor: Editor): List<FileState> {
        val project = editor.project ?: return emptyList()
        val projectId = project.name
        val virtualFile = editor.virtualFile ?: return emptyList()
        val filePath = virtualFile.path

        val localHistoryFacade = LocalHistoryImpl.getInstanceImpl().facade

        if (localHistoryFacade == null) {
            thisLogger().warn("Local history facade is null")
            return emptyList()
        }

        val fileChanges = mutableListOf<FileChange>()

        val changeProcessorBase = ZencoderCodeChangeProcessor(projectId, null) { changeSet ->
            val timestamp = changeSet.timestamp

            changeSet.changes.forEach { change ->
                if (change.contentsToPurge.isEmpty()) {
                    fileChanges.add(FileChange(timestamp, ""))
                } else {
                    fileChanges.add(FileChange(timestamp, change.contentsToPurge[0]))
                }
            }
        }

        localHistoryFacade.collectChanges(filePath, changeProcessorBase)

        return fileChanges.sortedBy { it.timestamp }
            .takeLast(10)
            .map { fileChange ->
                FileState(timestamp = fileChange.timestamp, content = fileChange.content.toString())
            }
    }

    private fun createEditPredictionRequest(
        editor: Editor,
        project: Project,
        requestId: String
    ): EditPredictionRequest? {
        val virtualFile = editor.virtualFile
        if (virtualFile == null) {
            thisLogger().warn("Failed to create edit prediction request: Virtual file is null")
            return null
        }

        val transformedChanges = extractFileStatesFromLocalHistory(editor)

        val cursorPosition = ApplicationManager.getApplication().runReadAction<LogicalPosition> {
            val offset = editor.caretModel.offset
            editor.offsetToLogicalPosition(offset)
        }

        val psiFile = ApplicationManager.getApplication().runReadAction<PsiFile?> {
            PsiDocumentManager.getInstance(project).getPsiFile(editor.document)
        }

        if (psiFile == null) {
            thisLogger().warn("Failed to create NEP request: Could not get PSI file for document")
            return null
        }

        val filePath = project.calculateRelativePath(virtualFile.path).actualPath

        val currentFileContent = editor.document.text
        val language = psiFile.language.toString()

        return EditPredictionRequest(
            requestId = requestId,
            fileStates = transformedChanges,
            fileContent = currentFileContent,
            language = language,
            filePath = filePath,
            cursorPosition = CursorPosition(row = cursorPosition.line, column = cursorPosition.column)
        )
    }

    fun requestNextEditPrediction(
        editor: Editor,
        requestId: String,
        callback: (EditPredictionResponse?) -> Unit
    ) {
        val project = editor.project ?: run {
            callback(null)
            return
        }

        scope.launch(Dispatchers.IO) {
            try {
                val request = createEditPredictionRequest(editor, project, requestId)
                if (request == null) {
                    withContext(Dispatchers.Main) {
                        callback(null)
                    }
                    return@launch
                }

                val response = service<CodeCompletionService>().requestNextEditPrediction(request)

                withContext(Dispatchers.Main) {
                    callback(response)
                }
            } catch (ex: Exception) {
                thisLogger().error("Error communicating with server", ex)
                withContext(Dispatchers.Main) {
                    callback(null)
                }
            }
        }
    }
}
