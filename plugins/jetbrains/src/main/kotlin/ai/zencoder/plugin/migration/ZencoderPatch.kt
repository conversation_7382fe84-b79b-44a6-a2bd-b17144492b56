package ai.zencoder.plugin.migration

import ai.zencoder.plugin.migration.ZencoderAppPatch.AppContext
import ai.zencoder.plugin.migration.ZencoderPatch.Context
import ai.zencoder.plugin.migration.ZencoderProjectPatch.ProjectContext
import ai.zencoder.plugin.utils.*
import ai.zencoder.plugin.webview.chat.ChatManager
import com.intellij.conversion.ConversionContext
import com.intellij.conversion.impl.ConversionContextImpl
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import java.nio.file.Path

const val ZENCODER_PATCH_PREFIX = "zencoder-patch"

sealed interface ZencoderPatch<C : Context> {
    /**
     * Unique identifier suffix of the patch.
     *
     * The id suffix must be immutable and must never be renamed after the patch is deployed.
     * It is used as a historical reference and must remain constant.
     *
     * Example of a valid id suffix: "add-user-index"
     */
    val idSuffix: String

    val id get() = "$ZENCODER_PATCH_PREFIX-$idSuffix"

    fun isNeeded(ctx: C): Boolean

    fun doPatch(ctx: C)

    fun applyPatch(ctx: C) {
        if (ctx.runOnceStorage.isMarkedAsRunOnce(id)) {
            thisLogger().info("Patch $id has been already applied")
            return
        }

        thisLogger().info("Running patch $id")
        val start = System.currentTimeMillis()

        if (!isNeeded(ctx)) {
            thisLogger().info("Patch $id is not needed, marked as run")
            showInformationIfDevMode("Patch $id is not needed, marked as run")
            ctx.runOnceStorage.markAsRunOnce(id)
            return
        }

        try {
            doPatch(ctx)
        } catch (t: Throwable) {
            thisLogger().warn("Error while applying patch $id", t)
            showInformationIfDevMode("Error while applying patch $id")
            return
        }
        val durationMs = System.currentTimeMillis() - start
        thisLogger().info("Patch $id was completed successfully, marked as run, took ${durationMs}ms")
        showInformationIfDevMode("Patch $id was completed successfully, marked as run, took ${durationMs}ms")
        ctx.runOnceStorage.markAsRunOnce(id)
    }

    interface Context {
        val runOnceStorage: RunOnceStorage
    }
}

interface ZencoderAppPatch : ZencoderPatch<AppContext> {
    data object AppContext : Context {
        override val runOnceStorage get() = appRunOnceStorage
    }
}

interface ZencoderProjectPatch : ZencoderPatch<ProjectContext> {
    data class ProjectContext(
        val project: Project,
        val conversionContext: ConversionContext
    ) : Context {
        constructor(project: Project, projectPath: Path) : this(
            project,
            ConversionContextImpl(projectPath)
        )

        override val runOnceStorage get() = project.runOnceStorage
    }
}

val ProjectContext.chatManager get() = project.service<ChatManager>()
val ProjectContext.chatSessions get() = chatManager.state.chatSessions
