package ai.zencoder.plugin.mcp.sdk.client

import ai.zencoder.plugin.mcp.sdk.JSONRPCMessage
import ai.zencoder.plugin.mcp.sdk.shared.ReadBuffer
import ai.zencoder.plugin.mcp.sdk.shared.Transport
import ai.zencoder.plugin.mcp.sdk.shared.serializeMessage
import com.intellij.openapi.diagnostic.thisLogger
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.atomicfu.AtomicBoolean
import kotlinx.atomicfu.atomic
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.consumeEach
import kotlinx.io.*
import kotlin.coroutines.CoroutineContext

/**
 * A transport implementation for JSON-RPC communication that leverages standard input and output streams.
 *
 * This class reads from an input stream to process incoming JSON-RPC messages and writes JSON-RPC messages
 * to an output stream.
 *
 * @param input The input stream where messages are received.
 * @param output The output stream where messages are sent.
 */
class StdioClientTransport(
    private val input: Source,
    private val output: Sink,
    private val cs: CoroutineScope
) : Transport {
    private val logger = KotlinLogging.logger {}
    private val ioCoroutineContext: CoroutineContext = Dispatchers.IO
    private val scope by lazy {
        CoroutineScope(cs.coroutineContext + ioCoroutineContext + SupervisorJob())
    }
    private var job: Job? = null
    private val initialized: AtomicBoolean = atomic(false)
    private val sendChannel = Channel<JSONRPCMessage>(Channel.UNLIMITED)
    private val readBuffer = ReadBuffer()

    override var onClose: (() -> Unit)? = null
    override var onError: ((Throwable) -> Unit)? = null
    override var onMessage: (suspend ((JSONRPCMessage) -> Unit))? = null

    override suspend fun start() {
        if (!initialized.compareAndSet(false, true)) {
            error("StdioClientTransport already started!")
        }

        logger.debug { "Starting StdioClientTransport..." }

        val outputStream = output.buffered()

        val coroutineHandler = CoroutineExceptionHandler { _, exception ->
            thisLogger().warn("Error in StdioClientTransport: ${exception.message}")
            thisLogger().debug(exception)
            onError?.invoke(exception)
        }

        job = scope.launch(CoroutineName("StdioClientTransport.IO#${hashCode()}") + coroutineHandler) {
            val readJob = this.launch {
                logger.debug { "Read coroutine started." }
                try {
                    input.use {
                        while (isActive) {
                            val buffer = Buffer()
                            val bytesRead = input.readAtMostTo(buffer, 8192)
                            if (bytesRead == -1L) break
                            if (bytesRead > 0L) {
                                readBuffer.append(buffer.readByteArray())
                                processReadBuffer()
                            }
                        }
                    }
                } catch (e: Exception) {
                    onError?.invoke(e)
                    logger.error(e) { "Error reading from input stream" }
                }
            }

            val writeJob = this.launch {
                logger.debug { "Write coroutine started." }
                try {
                    sendChannel.consumeEach { message ->
                        val json = serializeMessage(message)
                        outputStream.writeString(json)
                        outputStream.flush()
                    }
                } catch (e: Throwable) {
                    if (isActive) {
                        onError?.invoke(e)
                        logger.error(e) { "Error writing to output stream" }
                    }
                } finally {
                    output.close()
                }
            }

            readJob.join()
            writeJob.cancelAndJoin()
        }
        job?.invokeOnCompletion { throwable ->
            if (throwable == null) {
                onClose?.invoke()
            }
        }
    }

    override suspend fun send(message: JSONRPCMessage) {
        if (!initialized.value) {
            error("Transport not started")
        }

        sendChannel.send(message)
    }

    override suspend fun close() {
        if (!initialized.compareAndSet(true, false)) {
            error("Transport is already closed")
        }
        job?.cancelAndJoin()
        input.close()
        output.close()
        readBuffer.clear()
        sendChannel.close()
        onClose?.invoke()
    }

    private suspend fun processReadBuffer() {
        while (true) {
            try {
                val msg = readBuffer.readMessage() ?: break
                onMessage?.invoke(msg)
            } catch (e: Throwable) {
                onError?.invoke(e)
                logger.error(e) { "Error processing message." }
            }
        }
    }
}
