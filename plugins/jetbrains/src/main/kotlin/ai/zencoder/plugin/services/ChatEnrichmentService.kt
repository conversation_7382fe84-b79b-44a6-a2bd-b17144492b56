package ai.zencoder.plugin.services

import ai.zencoder.plugin.webview.model.*
import ai.zencoder.plugin.webview.model.MessagePart.CodePatch.ClientDiff
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project

/*
 ChatModel that we persist on file system and the one we use to render the UI are two different models in fact.
 This service will take care of enriching the model that we use to render the UI with information coming from the persisted model.
 */

@Service(Service.Level.PROJECT)
class ChatEnrichmentService(val project: Project) {

    private val rollbackCachedService get() = project.service<RollbackCachedService>()

    fun enrichChatModel(model: ChatModel): ChatModel {
        var result = model.enrichChatWithRollbackInfo()
        result = result.enrichRevertibleAssistantMessage()
        return result
    }

    private fun ChatModel.enrichChatWithRollbackInfo(): ChatModel {
        val messagesWithRollbackInfo: Array<ChatMessage?> = arrayOfNulls(this.messages.size)
        var diffsAppliedAfterCurrentMessage = listOf<ClientDiff>()
        var areThereAppliedChangesInAnswer = false

        for (i in this.messages.indices.reversed()) {
            val currentMessage = this.messages[i]
            val createdAt = currentMessage.createdAt
            if (currentMessage.isGhost == true) {
                messagesWithRollbackInfo[i] = currentMessage.copyWithEmptyRollbackInfo()
                continue
            }
            val diffsAppliedInCurrentMessage = currentMessage.content
                .filterIsInstance<MessagePart.CodePatch>()
                .flatMap { patch -> patch.diffs }
                .filter { diff -> diff.isApplied ?: false }
            val allDiffsThatShouldBeRolledBack = diffsAppliedAfterCurrentMessage + diffsAppliedInCurrentMessage

            if (diffsAppliedInCurrentMessage.isNotEmpty()) {
                areThereAppliedChangesInAnswer = true
            }

            // Update for next iteration
            diffsAppliedAfterCurrentMessage = allDiffsThatShouldBeRolledBack

            // we should show rollback block only for user messages
            if (currentMessage.role != ChatMessageRole.USER) {
                messagesWithRollbackInfo[i] = currentMessage.copyWithEmptyRollbackInfo()
                continue
            }

            // Skip if no diffs to roll back, relevant to current user request
            if (allDiffsThatShouldBeRolledBack.isEmpty() || !areThereAppliedChangesInAnswer) {
                messagesWithRollbackInfo[i] = currentMessage.copyWithEmptyRollbackInfo()
                continue
            }

            val rollbackInfo = RollbackInfo(
                messageId = currentMessage.id,
                timestamp = createdAt,
                files = mergeDiffsToRollback(allDiffsThatShouldBeRolledBack, createdAt)
            )
            val contextWithRollbackInfo = currentMessage.context?.copy(
                rollbackInfo = rollbackInfo
            ) ?: ChatMessageContext(rollbackInfo = rollbackInfo)
            messagesWithRollbackInfo[i] = currentMessage.copy(context = contextWithRollbackInfo)

            areThereAppliedChangesInAnswer = false
        }
        return this.copy(
            messages = messagesWithRollbackInfo.asSequence()
                .filterNotNull()
                .toMutableList()
        )
    }

    private fun ChatMessage.copyWithEmptyRollbackInfo() =
        this.copy(context = this.context?.copy(rollbackInfo = null, revertAppliedDiffsInfo = null))

    private fun ChatModel.enrichRevertibleAssistantMessage(): ChatModel {
        val revertibleMessage = findRevertibleAssistantMessage()
        if (revertibleMessage == null) {
            return this
        }

        val appliedDiffs = revertibleMessage.content
            .filterIsInstance<MessagePart.CodePatch>()
            .flatMap { patch -> patch.diffs }
            .filter { diff -> diff.isApplied ?: false }

        val messages = this.messages.toMutableList()
        val enrichedMessage = revertibleMessage
            .copyWithEmptyRollbackInfo()
            .copyWithRevertInfo(appliedDiffs)
        val messageIndex = messages.indexOf(revertibleMessage)
        if (messageIndex >= 0) {
            messages[messageIndex] = enrichedMessage
        }

        return this.copy(messages = messages)
    }

    private fun ChatMessage.copyWithRevertInfo(diffs: List<ClientDiff>): ChatMessage {
        if (diffs.isEmpty()) {
            return this
        }

        val revertInfo = RollbackInfo(
            messageId = this.id,
            timestamp = this.createdAt,
            files = mergeDiffsToRollback(diffs, this.createdAt)
        )

        val contextWithRevertInfo = this.context?.copy(
            revertAppliedDiffsInfo = revertInfo
        ) ?: ChatMessageContext(revertAppliedDiffsInfo = revertInfo)

        return this.copy(context = contextWithRevertInfo)
    }

    private fun mergeDiffsToRollback(diffs: List<ClientDiff>, createdAt: Long): List<RollbackFilePath> {
        return diffs
            .groupBy { it.path }
            .map { (path, diffsWithSamePath) ->
                val isToRemove = diffsWithSamePath.any { it.isNew }
                val isMissingSnapshot = rollbackCachedService.wasFileCreatedSinceTimestamp(createdAt, path)
                RollbackFilePath(
                    path = path,
                    isToRemove = isToRemove,
                    isMissingSnapshot = isMissingSnapshot && !isToRemove
                )
            }
    }
}

/*
 * Annotation to indicate that a field should be populated by [ChatEnrichmentService].
 */
annotation class PopulatedByChatEnrichmentService
