package ai.zencoder.plugin.ui.tools.services

import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.model.ToolCategory
import ai.zencoder.plugin.mcp.model.ToolStatus

/**
 * Service for filtering tools based on category and search query.
 * Follows the Single Responsibility Principle by focusing only on filtering logic.
 */
interface ToolsFilterService {
    /**
     * Filters tools based on category and search query.
     *
     * @param tools The map of tools to filter
     * @param category The category to filter by
     * @param searchQuery The search query to filter by
     * @return A list of filtered tool entries sorted by name
     */
    fun filterTools(
        tools: Map<String, McpServerInfo>,
        category: ToolCategory,
        searchQuery: String
    ): List<Pair<String, McpServerInfo>>
}

/**
 * Default implementation of ToolsFilterService.
 */
class DefaultToolsFilterService : ToolsFilterService {

    override fun filterTools(
        tools: Map<String, McpServerInfo>,
        category: ToolCategory,
        searchQuery: String
    ): List<Pair<String, McpServerInfo>> {
        return tools.filter { (_, tool) ->
            // Filter by category
            when (category) {
                ToolCategory.ALL -> tool.status.status == ToolStatus.LIBRARY
                ToolCategory.ZENCODER -> tool.author != null && tool.author.startsWith("ZENCODER")
                ToolCategory.CUSTOM -> tool.author == null
                ToolCategory.LIBRARY -> tool.author != null && !tool.author.startsWith("ZENCODER")
            }
        }.filter { (_, tool) ->
            // Filter by search query
            if (searchQuery.isEmpty()) {
                true
            } else {
                val query = searchQuery.lowercase()
                tool.name?.lowercase()?.contains(query) ?:
                    (tool.description?.lowercase()?.contains(query) ?: false)
            }
        }.toList().sortedBy { (_, tool) -> tool.name }
    }
}
