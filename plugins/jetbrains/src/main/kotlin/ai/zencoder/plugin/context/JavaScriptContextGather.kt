package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.JavaScriptNodeResolver
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import com.intellij.lang.Language
import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration
import com.intellij.lang.ecmascript6.psi.JSExportAssignment
import com.intellij.lang.javascript.JavascriptLanguage
import com.intellij.lang.javascript.documentation.JSDocumentationUtils
import com.intellij.lang.javascript.psi.JSFunction
import com.intellij.lang.javascript.psi.JSFunctionExpression
import com.intellij.lang.javascript.psi.JSReferenceExpression
import com.intellij.lang.javascript.psi.ecmal4.JSClass
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.*
import com.intellij.psi.util.PsiTreeUtil

class JavaScriptContextGather : ContextGather {

    private val resolver = JavaScriptNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean {
        return language.isKindOf(JavascriptLanguage.INSTANCE)
    }

    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? {
        val docString = if (extractDocComment) {
            runReadAction { JSDocumentationUtils.findDocComment(psiElement)?.text }
        } else {
            null
        }
        return when (psiElement) {
            is JSFunction -> {
                val name = psiElement.name ?: return null
                ContextSymbolHeader(
                    name = name,
                    kind = NodeType.Method,
                    docstring = docString
                )
            }

            is JSClass -> {
                val name = psiElement.name ?: return null
                ContextSymbolHeader(
                    name = name,
                    kind = NodeType.Class,
                    docstring = docString
                )
            }

            is JSExportAssignment -> {
                val actualFunction = psiElement.children.first { it is JSFunctionExpression } as JSFunction
                val name = actualFunction.name ?: return null

                ContextSymbolHeader(
                    name = name,
                    kind = NodeType.Method,
                    docstring = docString
                )
            }

            else -> null
        }
    }

    override fun readImportsSection(psiFile: PsiFile): String =
        PsiTreeUtil.findChildrenOfType(psiFile, ES6ImportDeclaration::class.java).joinToString(separator = "\n") {
            runReadAction { it.text }
        }

    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        val importSignatures = mutableSetOf<ImportSignature>()
        val psiFile = runReadAction { element.containingFile }
            ?: return emptyList()
        for (importStmt in runReadAction { PsiTreeUtil.findChildrenOfType(psiFile, ES6ImportDeclaration::class.java) }) {
            for (importElement in runReadAction { importStmt.importSpecifiers }) {
                val reference = runReadAction { importElement.reference }
                runReadAction {
                    resolver.toResolvedASTNode(reference?.resolve(), zencoderFeatureFlags.enableContextExternalReferenceGathering)
                        ?.let {
                            importSignatures.add(
                                ImportSignature(importStatement = importStmt.text, signature = it)
                            )
                        }
                }
            }
        }
        return importSignatures.toList()
    }

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? JSFunction ?: runReadAction { PsiTreeUtil.getParentOfType(element, JSFunction::class.java) }
        val classElement = element as? JSClass ?: runReadAction { PsiTreeUtil.getParentOfType(element, JSClass::class.java) }
        val methodTypes = runReadAction { PsiTreeUtil.collectElementsOfType(methodElement, JSReferenceExpression::class.java) }
        val fieldTypes = if (classElement == null) {
            emptyList()
        } else {
            runReadAction { classElement.fields.toList().mapNotNull { PsiTreeUtil.findChildOfType(it, JSReferenceExpression::class.java) } }
        }

        val resolved = (methodTypes + fieldTypes).asSequence()
            .mapNotNull { runReadAction { it.resolve() } }
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.node?.startOffset) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }.toList()
        return resolved
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)
}
