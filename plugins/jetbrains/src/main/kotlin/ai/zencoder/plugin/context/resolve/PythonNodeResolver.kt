package ai.zencoder.plugin.context.resolve

import ai.zencoder.plugin.context.SimpleASTNode
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.PsiElement
import com.jetbrains.python.psi.*
import kotlin.reflect.KClass

class PythonNodeResolver : ASTNodeResolver<PyClass, PyFunction, PyTypeDeclarationStatement, PyFile> {
    override fun classElement(): KClass<PyClass> = PyClass::class

    override fun functionElement(): KClass<PyFunction> = PyFunction::class

    override fun fieldElement(): KClass<PyTypeDeclarationStatement> = PyTypeDeclarationStatement::class
    override fun fileElement(): KClass<PyFile> = PyFile::class

    override fun importsElement(): KClass<out PsiElement> = PyImportStatementBase::class

    override fun extractClassSignature(element: PyClass): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val classLine = runReadAction { "class ${element.presentation?.presentableText}:" }

        return SimpleASTNode.forClassHeader(
            psiElement = element,
            className = element.name,
            signature = classLine
        )
    }

    override fun extractMethodSignature(element: PyFunction): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val signature = StringBuilder("def ${element.name}(")

        element.parameterList.parameters.forEach { param ->
            runReadAction { signature.append("${param.text}, ") }
        }

        if (signature.endsWith(", ")) {
            signature.setLength(signature.length - 2)
        }

        signature.append(")")

        val returnType = runReadAction { element.annotation }
        returnType?.let {
            runReadAction { signature.append(" ${it.text}") }
        }

        return SimpleASTNode.forMethod(
            psiElement = element,
            methodName = element.name,
            signature = signature.toString()
        )
    }

    override fun extractFieldSignature(element: PyTypeDeclarationStatement): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        return SimpleASTNode.forProperty(
            element,
            element.name.orEmpty(),
            element.text
        )
    }
}
