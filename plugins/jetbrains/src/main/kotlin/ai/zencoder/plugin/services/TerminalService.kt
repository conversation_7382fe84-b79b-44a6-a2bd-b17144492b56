package ai.zencoder.plugin.services

import ai.zencoder.plugin.services.lang.LangSpecificTerminalActionsService
import ai.zencoder.plugin.utils.calculateRelativePath
import com.intellij.execution.ExecutionException
import com.intellij.execution.ExecutionManager
import com.intellij.execution.RunManager
import com.intellij.execution.executors.DefaultRunExecutor
import com.intellij.execution.process.ScriptRunnerUtil
import com.intellij.execution.runners.ExecutionEnvironmentBuilder
import com.intellij.execution.runners.ProgramRunner
import com.intellij.execution.ui.RunContentDescriptor
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.sh.run.ShConfigurationType
import com.intellij.sh.run.ShRunConfiguration
import kotlinx.coroutines.CompletableDeferred
import org.jetbrains.kotlin.idea.util.application.executeInBackgroundWithProgress
import org.jetbrains.plugins.terminal.TerminalProjectOptionsProvider
import java.io.File
import java.nio.file.Path

data class CommandResult(
    val exitCode: Int,
    val outputPath: String,
    val output: String?
)

const val COMMAND_EXECUTION_TIMEOUT_MS = 180 * 1000L
const val TIMEOUT_EXIT_CODE = 124
const val FAILED_EXIT_CODE = 1
val TERMINAL_NAME_KEY: Key<String> = Key.create("ZEN_TERMINAL")

@Service(Service.Level.PROJECT)
class TerminalService(val project: Project) {

    fun sendCommand(terminalName: String, command: String): Boolean {
        runInEdt {
            try {
                runtimeBuilder(terminalName, command).buildAndExecute()
            } catch (e: Exception) {
                thisLogger().error("Failed to execute command", e)
            }
        }
        return true
    }

    fun cancelCommand(terminalName: String) {
        executeInBackgroundWithProgress(project, "Cancelling command...") {
            try {
                ExecutionManager.getInstance(project).getRunningProcesses().firstOrNull { it ->
                    it.getUserData(TERMINAL_NAME_KEY) == terminalName
                }?.destroyProcess()
            } catch (e: Exception) {
                thisLogger().error("Failed to cancel command", e)
            }
        }
    }

    private fun runtimeBuilder(
        terminalName: String,
        command: String,
        outputFilePath: Path? = null
    ): ExecutionEnvironmentBuilder {
        val configurationSettings = RunManager.getInstance(project).createConfiguration(terminalName, ShConfigurationType::class.java)
        val configuration = configurationSettings.configuration as ShRunConfiguration

        try {
            val terminalService = project.service<LangSpecificTerminalActionsService>()
            terminalService.patchShConfiguration(project, terminalName, command, configuration)
        } catch (e: Exception) {
            thisLogger().debug("Failed to patch sh configuration", e)
        }

        configuration.isExecuteInTerminal = false
        configuration.scriptWorkingDirectory = project.basePath
        configuration.isAllowRunningInParallel = false
        if (outputFilePath != null) {
            configuration.isSaveOutputToFile = true
            configuration.setFileOutputPath(outputFilePath.toString())
        }
        val executor = DefaultRunExecutor.getRunExecutorInstance()
        return ExecutionEnvironmentBuilder.create(executor, configuration)
    }

    suspend fun runCommandWithResult(terminalName: String, command: String): Result<CommandResult> {
        val fileService = project.service<FileService>()
        val outputFileName = "$terminalName.log"
        val outputFilePath = File(fileService.findZencoderDirectory().virtualFile.path, outputFileName).toPath()
        val builder = runtimeBuilder(terminalName, command, outputFilePath)
        val processStartedDeferred = CompletableDeferred<RunContentDescriptor?>(null)
        val env = builder.build(
            object : ProgramRunner.Callback {
                override fun processStarted(descriptor: RunContentDescriptor) {
                    processStartedDeferred.complete(descriptor)
                }

                override fun processNotStarted(error: Throwable?) {
                    processStartedDeferred.complete(null)
                }
            }
        )
        runInEdt {
            try {
                env.runner.execute(env)
            } catch (_: Exception) {
                processStartedDeferred.complete(null)
            }
        }
        val result = processStartedDeferred.await() ?: return Result.failure(Exception("Failed to execute command"))
        val processHandler = result.processHandler ?: return Result.failure(Exception("Failed to execute command"))
        processHandler.putUserData(TERMINAL_NAME_KEY, terminalName)
        var commandResult: String? = null
        var exitCode: Int
        try {
            commandResult = ScriptRunnerUtil.getProcessOutput(
                processHandler,
                ScriptRunnerUtil.STDOUT_OR_STDERR_OUTPUT_KEY_FILTER,
                COMMAND_EXECUTION_TIMEOUT_MS
            )
            exitCode = processHandler.exitCode ?: FAILED_EXIT_CODE
        } catch (_: ExecutionException) {
            exitCode = TIMEOUT_EXIT_CODE
        } catch (e: Exception) {
            thisLogger().error("Failure during command execution", e)
            exitCode = FAILED_EXIT_CODE
        }
        return Result.success(
            CommandResult(
                exitCode = exitCode,
                outputPath = project.calculateRelativePath(outputFilePath.toString()).actualPath,
                output = commandResult
            )
        )
    }

    fun showTerminal(terminalName: String) {
        runInEdt {
            val toolWindow = ToolWindowManager.getInstance(project).getToolWindow(DefaultRunExecutor.getRunExecutorInstance().toolWindowId)
                ?: return@runInEdt
            if (!toolWindow.isVisible) {
                toolWindow.show()
            }
            toolWindow.contentManager.contents.firstOrNull { it.tabName == terminalName }
                ?.let { toolWindow.contentManager.setSelectedContent(it) }
        }
    }

    fun currentShell(): String? = File(project.service<TerminalProjectOptionsProvider>().shellPath).name
}
