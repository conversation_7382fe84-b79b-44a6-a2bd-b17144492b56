package ai.zencoder.plugin.log

import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.log.SentryWebviewOptions.InitialScope
import ai.zencoder.plugin.log.SentryWebviewOptions.InitialScope.User
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.ZencoderApp
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationNamesInfo
import com.intellij.openapi.application.ex.ApplicationInfoEx
import com.intellij.openapi.components.service
import com.intellij.openapi.util.SystemInfo
import io.sentry.Sentry.OptionsConfiguration
import io.sentry.SentryOptions
import kotlinx.serialization.Serializable

object SentryConfiguration : OptionsConfiguration<SentryOptions> {
    const val DEFAULT_OPERATION = "default"
    private const val SENTRY_WEBVIEW_DSN = "https://<EMAIL>/4508246706815057"
    private const val SENTRY_DSN = "https://<EMAIL>/4508160975110224"
    private const val SAMPLE_RATE = 0.1
    private const val NORMALIZE_DEPTH = 6

    val webviewOptions
        get() = SentryWebviewOptions(
            enabled = ZencoderApp.isProduction || service<ZencoderSettings>().isSentryEnabled,
            dsn = SENTRY_WEBVIEW_DSN,
            release = ZencoderApp.plugin.version,
            sampleRate = if (ZencoderApp.isDevMode) 1.0 else SAMPLE_RATE,
            tracesSampleRate = if (ZencoderApp.isDevMode) 1.0 else SAMPLE_RATE,
            debug = ZencoderApp.isDevMode,
            normalizeDepth = NORMALIZE_DEPTH,
            initialScope = InitialScope(
                user = service<AuthService>().userDataOrNull()?.let { User(id = it.id) }
            ),
            environment = "jetbrains-webview:${ZencoderApp.environmentName}"
        )

    override fun configure(options: SentryOptions): Unit = with(options) {
        dsn = SENTRY_DSN
        isAttachStacktrace = true
        isAttachServerName = false
        enableTracing = true

        setTag("OS Name", SystemInfo.OS_NAME)
        setTag("Java version", SystemInfo.JAVA_VERSION)
        setTag("Java vendor", SystemInfo.JAVA_VENDOR)

        ApplicationNamesInfo.getInstance().let {
            environment = it.productName
            setTag("IDE Name", it.productName)
            setTag("IDE Full Name", it.fullProductNameWithEdition)
        }

        ApplicationInfo.getInstance().let {
            setTag("IDE Version", it.fullVersion)
            setTag("IDE Build", it.build.asString())
            setTag("Is EAP", "${(it as ApplicationInfoEx).isEAP}")
        }

        ZencoderApp.plugin.also {
            setTag("Plugin", it.name)
            setTag("Version", it.version)
            release = it.version
        }
    }
}

@Serializable
data class SentryWebviewOptions(
    val enabled: Boolean,
    val dsn: String,
    val release: String,
    val sampleRate: Double,
    val tracesSampleRate: Double,
    val debug: Boolean,
    val normalizeDepth: Int,
    val initialScope: InitialScope,
    val environment: String
) {
    @Serializable
    data class InitialScope(val user: User?) {
        @Serializable
        data class User(val id: String)
    }
}
