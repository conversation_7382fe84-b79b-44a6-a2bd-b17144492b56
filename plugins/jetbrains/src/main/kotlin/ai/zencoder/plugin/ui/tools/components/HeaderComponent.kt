package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import java.awt.BorderLayout
import java.awt.Component
import java.awt.Dimension
import javax.swing.*

/**
 * Component for displaying a header with title and description.
 * Follows JetBrains UI guidelines for typography and spacing.
 */
class HeaderComponent(
    private val title: String,
    private val description: String
) : UIComponent {

    override fun createComponent(): JComponent {
        return JPanel(BorderLayout()).apply {
            // Increase padding for better visual appearance
            border = UIStyleUtils.Borders.emptyBorder(
                UIStyleUtils.Spacing.LARGE,
                UIStyleUtils.Spacing.LARGE,
                UIStyleUtils.Spacing.MEDIUM,
                UIStyleUtils.Spacing.LARGE
            )
            background = JBColor.background()

            // Create a panel for the title and description with vertical spacing
            val headerContentPanel = JPanel().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                isOpaque = false
            }

            // Title with proper typography and left alignment
            val titleLabel = JBLabel(title).apply {
                font = UIStyleUtils.Typography.TITLE_FONT
                foreground = JBColor.foreground()
                alignmentX = Component.LEFT_ALIGNMENT
            }
            headerContentPanel.add(titleLabel)

            // Add vertical spacing between title and description
            headerContentPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.MEDIUM))

            // Description with word wrap and left alignment
            val descriptionLabel = JBLabel(
                "<html><div width='500'>$description</div></html>"
            ).apply {
                foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
                font = UIStyleUtils.Typography.MEDIUM_FONT
                alignmentX = Component.LEFT_ALIGNMENT
            }
            headerContentPanel.add(descriptionLabel)

            // Add a separator line below the header for visual separation
            val separator = JSeparator().apply {
                foreground = JBColor.border()
                alignmentX = Component.LEFT_ALIGNMENT
                maximumSize = Dimension(Integer.MAX_VALUE, 1)
            }

            // Add the separator with proper spacing
            val separatorPanel = JPanel().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                isOpaque = false
                border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.LARGE, 0, 0, 0)
                add(separator)
            }

            // Assemble the header panel
            val topPanel = JPanel(BorderLayout()).apply {
                isOpaque = false
                add(headerContentPanel, BorderLayout.NORTH)
                add(separatorPanel, BorderLayout.SOUTH)
            }

            add(topPanel, BorderLayout.NORTH)
        }
    }
}
