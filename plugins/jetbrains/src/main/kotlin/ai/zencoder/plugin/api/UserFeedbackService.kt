package ai.zencoder.plugin.api

import ai.zencoder.generated.client.feedback.apis.FeedbackApi
import ai.zencoder.generated.client.feedback.models.Response
import ai.zencoder.generated.client.feedback.models.UserFeedbackRequest
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.settings.ZencoderFeedbackServiceUrlChangeListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.ZencoderSettingsEvents
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.util.application

@Service(Level.APP)
class UserFeedbackService {
    private val authService get() = service<AuthService>()
    private var client = createClient()

    init {
        application.messageBus
            .connect()
            .subscribe(
                ZencoderSettingsEvents.FEEDBACK_SERVICE_URL_CHANGED,
                object : ZencoderFeedbackServiceUrlChangeListener {
                    override fun feedbackServiceUrlChanged() {
                        client = createClient()
                    }
                }
            )
    }

    fun submitUserFeedback(request: UserFeedbackRequest): Response {
        return client
            .withAuth(authService.accessToken)
            .userFeedbackUserFeedbackPost(request)
    }

    fun submitUserFeedbackDeliberate(request: UserFeedbackRequest): Response {
        return client
            .withAuth(authService.accessToken)
            .userFeedbackDeliberateUserFeedbackDeliberatePost(request)
    }

    private fun createClient() = FeedbackApi(basePath = service<ZencoderSettings>().feedbackServiceUrl)
}
