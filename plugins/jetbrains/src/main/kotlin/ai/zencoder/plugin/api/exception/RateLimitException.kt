package ai.zencoder.plugin.api.exception

import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.utils.formatter.ZenDateTimeFormatter
import kotlin.math.round

open class RateLimitException(
    val retryAfter: Long?,
    val retryTime: String? = ZenDateTimeFormatter.formatTimeAfterSeconds(retryAfter, "MMMM dd 'at' HH:mm"),
    override val message: String = ZencoderBundle["notification.exceptions.rateLimitReached"].format(retryTime),
    cause: Throwable? = null
) : Exception(message, cause)

fun trackRateLimit(
    url: String,
    retryAfter: Long?,
    quotaKey: String?
) {
    if (retryAfter != null && quotaKey != null) {
        track(
            "Advanced requests limit reached",
            "time_to_limits_reset" to toHours(retryAfter)
        )
    } else if (retryAfter != null) {
        track(
            "Requests limit reached",
            "url_path" to url,
            "time_to_limits_reset" to toHours(retryAfter)
        )
    } else {
        track(
            "Requests limit reached",
            "url_path" to url
        )
    }
}

fun toHours(retryAfter: Long) = round(retryAfter / 3600.0)
