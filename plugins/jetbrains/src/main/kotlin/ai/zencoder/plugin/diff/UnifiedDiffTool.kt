package ai.zencoder.plugin.diff

import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.model.path.RelativePathFromProjectRoot
import ai.zencoder.plugin.services.FileService
import ai.zencoder.plugin.settings.Shortcuts
import ai.zencoder.plugin.ui.ZenButton
import ai.zencoder.plugin.utils.Os
import ai.zencoder.plugin.utils.getOs
import com.intellij.diff.DiffContentFactory
import com.intellij.diff.chains.DiffRequestChain
import com.intellij.diff.chains.SimpleDiffRequestChain
import com.intellij.diff.contents.EmptyContent
import com.intellij.diff.contents.FileDocumentContentImpl
import com.intellij.diff.editor.ChainDiffVirtualFile
import com.intellij.diff.editor.DiffEditorTabFilesManager
import com.intellij.diff.requests.SimpleDiffRequest
import com.intellij.diff.util.DiffUserDataKeys
import com.intellij.diff.util.DiffUserDataKeysEx
import com.intellij.ide.util.PropertiesComponent
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Document
import com.intellij.openapi.editor.impl.DocumentImpl
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileTypes.FileType
import com.intellij.openapi.fileTypes.FileTypeRegistry
import com.intellij.openapi.fileTypes.PlainTextFileType
import com.intellij.openapi.fileTypes.UnknownFileType
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import com.intellij.openapi.util.text.StringUtilRt
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.findDocument
import com.intellij.testFramework.LightVirtualFile
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.launch
import java.awt.Dimension
import java.awt.FlowLayout
import java.lang.AssertionError
import java.nio.file.Path
import java.util.*
import javax.swing.BoxLayout
import javax.swing.JPanel

/**
 * Represents the different view modes for the diff viewer, similar to VSCode's DiffViewerMode.
 */
enum class DiffViewerMode {
    /**
     * Apply diff mode - allows editing and applying changes to the original file.
     */
    APPLY_DIFF,

    /**
     * Revert diff mode - allows reverting changes back to the original state.
     */
    REVERT_DIFF,

    /**
     * Remove file mode - for removing/deleting files.
     */
    REMOVE_FILE,

    /**
     * View only mode - read-only view of the diff without any action buttons.
     */
    VIEW_ONLY
}

/**
 * A unified tool to display built-in diff in a tab or separate window depending on user preferences.
 *
 * This class provides a comprehensive diff viewer that supports various modes such as applying diffs,
 * reverting changes, removing files, and view-only mode. It integrates with the IntelliJ platform's
 * diff viewer and allows for custom actions and callbacks based on the selected mode.
 *
 * @param project the current project context.
 * @param projectPath the relative path from the project root.
 * @param virtualFile the virtual file representing the original content, can be null.
 * @param originalContentText the original content text to compare, can be null except when viewMode is REVERT_DIFF where it's mandatory.
 * @param generatedContentText the generated content to compare against the original.
 * @param mode the view mode that determines the behavior and available actions.
 * @param afterApply a callback function to execute after applying changes.
 * @param afterCancel a callback function to execute after canceling changes.
 * @param title the title of the tab/window.
 * @param uniqueDiffFileName unique identifier for the diff file, defaults to a random UUID.
 * @param scope the coroutine scope for asynchronous operations.
 */
class UnifiedDiffTool(
    private val project: Project,
    private val projectPath: RelativePathFromProjectRoot,
    private val virtualFile: VirtualFile?,
    private val originalContentText: String?,
    private val generatedContentText: String,
    private val mode: DiffViewerMode = DiffViewerMode.APPLY_DIFF,
    private val afterApply: (ApplyResult) -> Unit,
    private val afterCancel: () -> Unit,
    private val title: String,
    private val uniqueDiffFileName: String = UUID.randomUUID().toString(),
    val scope: CoroutineScope
) {
    private val originalDocument: Document?
    private val lineSeparatorInOriginalFile: String?
    private val generatedFileType: FileType
    private val fileAbsolutePath: String

    init {
        originalDocument = runReadAction { if (virtualFile?.exists() ?: false) virtualFile.findDocument() else null }
        generatedFileType =
            virtualFile?.fileType ?: FileTypeRegistry.getInstance().getFileTypeByFileName(projectPath.actualPath.substringAfterLast("/"))
        lineSeparatorInOriginalFile = virtualFile?.detectedLineSeparator
        fileAbsolutePath = virtualFile?.path ?: Path.of("${project.basePath}/${projectPath.actualPath}").toString()

        require(mode != DiffViewerMode.REVERT_DIFF || originalContentText != null) {
            "originalContentText is mandatory when mode is REVERT_DIFF"
        }
        require(mode != DiffViewerMode.REVERT_DIFF || virtualFile != null) {
            "document should exist when mode is REVERT_DIFF"
        }
    }

    companion object {
        const val BOTTOM_PANEL_PREFERABLE_WIDTH = 400
        const val BOTTOM_PANEL_PREFERABLE_HEIGHT = 50
        const val BOTTOM_COMPONENT_SPLITTER_PROPORTION_PROPERTY_NAME = "DiffRequestProcessor.BottomComponentSplitter"
        const val BOTTOM_COMPONENT_SPLITTER_PROPORTION_DESIRED_VALUE = 0.94f
        const val BOTTOM_COMPONENT_SPLITTER_PROPORTION_DEFAULT_VALUE = 0.8f
        val PATH_KEY = Key<String>("unified.diff.tool.path.key")
        val FS_PATH_KEY = Key<String>("unified.diff.tool.fs.path.key")
    }

    private val propertiesComponent = service<PropertiesComponent>()
    private val diffTracker = service<DiffTracker>()
    private val fileService = project.service<FileService>()

    fun showDiff() {
        // Determine titles based on view mode
        val originalContentTitle = if (mode == DiffViewerMode.APPLY_DIFF) {
            ZencoderBundle["unified.diff.tool.originalContentTitle"]
        } else {
            ZencoderBundle["unified.diff.tool.originalContentForRevertTitle"]
        }
        val generatedContentTitle = ZencoderBundle["unified.diff.tool.generatedContentTitle"]

        val splitterProportionUserSettingsValue = propertiesComponent.getFloat(
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_PROPERTY_NAME,
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_DEFAULT_VALUE
        )
        propertiesComponent.setValue(
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_PROPERTY_NAME,
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_DESIRED_VALUE,
            splitterProportionUserSettingsValue
        )

        val contentFactory = DiffContentFactory.getInstance()
        val originalContent = when {
            // For APPLY_DIFF mode, use originalDocument if available
            mode == DiffViewerMode.APPLY_DIFF -> {
                if (originalDocument == null || originalDocument.text.isBlank()) {
                    EmptyContent()
                } else {
                    contentFactory.create(project, originalDocument).apply {
                        putUserData(DiffUserDataKeys.FORCE_READ_ONLY, true)
                    }
                }
            }
            // For other view modes, use originalContentText
            else -> {
                if (originalContentText.isNullOrBlank()) {
                    EmptyContent()
                } else {
                    contentFactory.create(
                        project,
                        LightVirtualFile(
                            "Original",
                            if (generatedFileType is UnknownFileType) PlainTextFileType.INSTANCE else generatedFileType,
                            originalContentText
                        )
                    ).apply {
                        putUserData(DiffUserDataKeys.FORCE_READ_ONLY, true)
                    }
                }
            }
        }
        // For some unknown reason diff viewer shows data properly only if the right side
        // has the \n line separators. No matter the OS or original file line separators.
        val generatedContentLineSeparator = generatedContentText.detectLineSeparator()
        val generatedContentTextWithSlashNSeparator = if (generatedContentLineSeparator != null && generatedContentLineSeparator != "\n") {
            StringUtilRt.convertLineSeparators(generatedContentText, "\n")
        } else {
            generatedContentText
        }

        // Determine if the generated content should be read-only based on view mode
        val isGeneratedContentReadOnly = (mode != DiffViewerMode.APPLY_DIFF)

        val generatedContent = contentFactory.create(
            project,
            LightVirtualFile(
                generatedContentTitle,
                // We use plain text file type because unknown file types are not supported by built-in diff
                if (generatedFileType is UnknownFileType) PlainTextFileType.INSTANCE else generatedFileType,
                generatedContentTextWithSlashNSeparator
            )
        ).apply {
            putUserData(DiffUserDataKeys.FORCE_READ_ONLY, isGeneratedContentReadOnly)
        }

        val simpleDiffRequest =
            SimpleDiffRequest(title, originalContent, generatedContent, originalContentTitle, generatedContentTitle)

        simpleDiffRequest.apply {
            putUserData(DiffUserDataKeysEx.FILE_NAME, uniqueDiffFileName)
        }

        diffTracker.trackDiffOnShow(
            originalDocument?.text.orEmpty(),
            this.generatedContentText,
            generatedFileType.defaultExtension
        )

        val request = SimpleDiffRequestChain(simpleDiffRequest).apply {
            putUserData(DiffUserDataKeysEx.VCS_DIFF_ACCEPT_LEFT_ACTION_TEXT, ZencoderBundle["unified.diff.tool.popupActions.revert"])
            // Only add bottom panel if not in view-only mode
            if (mode != DiffViewerMode.VIEW_ONLY) {
                putUserData(DiffUserDataKeysEx.BOTTOM_PANEL, buildButtonsPanel(simpleDiffRequest, uniqueDiffFileName, afterCancel))
            }
        }

        showDiffBuiltin(
            project,
            request,
            uniqueDiffFileName,
            newFile = originalContent is EmptyContent
        )

        propertiesComponent.setValue(
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_PROPERTY_NAME,
            splitterProportionUserSettingsValue,
            BOTTOM_COMPONENT_SPLITTER_PROPORTION_DESIRED_VALUE
        )
    }

    private fun buildButtonsPanel(
        simpleDiffRequest: SimpleDiffRequest,
        diffFileName: String,
        afterCancel: () -> Unit
    ): JPanel {
        val buttonPanel = DiffBottomPanel(afterCancel)
        buttonPanel.layout = BoxLayout(buttonPanel, BoxLayout.X_AXIS)
        buttonPanel.minimumSize = Dimension(BOTTOM_PANEL_PREFERABLE_WIDTH, BOTTOM_PANEL_PREFERABLE_HEIGHT)
        buttonPanel.maximumSize = Dimension(BOTTOM_PANEL_PREFERABLE_WIDTH, BOTTOM_PANEL_PREFERABLE_HEIGHT)
        buttonPanel.preferredSize = Dimension(BOTTOM_PANEL_PREFERABLE_WIDTH, BOTTOM_PANEL_PREFERABLE_HEIGHT)
        buttonPanel.layout = FlowLayout(FlowLayout.LEFT)

        // Create buttons based on view mode
        when (mode) {
            DiffViewerMode.APPLY_DIFF -> {
                buttonPanel.add(createApplyButton(simpleDiffRequest, diffFileName))
                buttonPanel.add(createRejectButton("Reject", diffFileName))
            }
            DiffViewerMode.REVERT_DIFF -> {
                buttonPanel.add(createRevertButton(diffFileName))
                buttonPanel.add(createRejectButton("Ignore", diffFileName))
            }
            DiffViewerMode.REMOVE_FILE -> {
                if (virtualFile != null) {
                    buttonPanel.add(createRemoveButton(diffFileName))
                }
                buttonPanel.add(createRejectButton("Ignore", diffFileName))
            }
            DiffViewerMode.VIEW_ONLY -> {
                // No buttons for view-only mode
            }
        }

        return buttonPanel
    }

    private fun createApplyButton(simpleDiffRequest: SimpleDiffRequest, diffFileName: String) =
        ZenButton.primary("Apply", shortcut = Shortcuts.acceptDiffAction) {
            val virtualDocumentOfGeneratedContent = simpleDiffRequest.contents
                .filterIsInstance<FileDocumentContentImpl>()
                .first { it.file is LightVirtualFile }
                .document

            val originalContent = originalDocument?.text
            var content = virtualDocumentOfGeneratedContent.text
            content = content.convertLineSeparatorToOriginal(originalContent, lineSeparatorInOriginalFile)

            diffTracker.trackDiffAfterApplyingChanges(
                originalDocument?.text.orEmpty(),
                this.generatedContentText,
                content,
                generatedFileType.defaultExtension,
                mode
            )

            if (getOs() == Os.WINDOWS && originalDocument != null) {
                val documentImpl = originalDocument as DocumentImpl
                if (!documentImpl.acceptsSlashR()) {
                    content = StringUtilRt.convertLineSeparators(content, "\n")
                }
            }

            val result = applyTextToOriginalDocument(content, true)

            scope.launch {
                val doc = result.await()
                afterApply(ApplyResult(doc, content))
                invokeLater { closeDiffTabOrWindow(diffFileName) }
            }
        }

    private fun createRevertButton(diffFileName: String) = ZenButton.destructive("Revert", shortcut = Shortcuts.acceptDiffAction) {
        val originalContent = originalContentText ?: ""

        diffTracker.trackDiffAfterRevertingChanges(
            originalContent,
            this.generatedContentText,
            generatedFileType.defaultExtension,
            mode
        )

        // Revert to original content
        val result = applyTextToOriginalDocument(originalContent, false)
        scope.launch {
            val doc = result.await()
            afterApply(ApplyResult(doc, originalContent))
            invokeLater { closeDiffTabOrWindow(diffFileName) }
        }
    }

    private fun applyTextToOriginalDocument(text: String, createFileIfMissing: Boolean): Deferred<VirtualFile> {
        val deferred = CompletableDeferred<VirtualFile>()

        val fileExists = virtualFile?.exists() ?: false
        if (fileExists) {
            WriteCommandAction.runWriteCommandAction(project) {
                try {
                    originalDocument!!.setText(text)
                    deferred.complete(virtualFile)
                } catch (_: AssertionError) { // Wrong line separators
                    fileService.refreshFileContent(RelativePathFromProjectRoot(projectPath.actualPath), text)
                    deferred.complete(virtualFile)
                }
            }
            FileDocumentManager.getInstance().saveAllDocuments()
        } else if (createFileIfMissing) {
            FileDocumentManager.getInstance().saveAllDocuments()
            WriteCommandAction.runWriteCommandAction(project) {
                scope.launch {
                    val result = fileService.refreshFileAndWaitSilentlyForActualUpdate(
                        projectPath,
                        text
                    )
                    deferred.complete(result.virtualFile)
                }
            }
        }

        return deferred
    }

    private fun createRemoveButton(diffFileName: String) = ZenButton.destructive("Remove File", shortcut = Shortcuts.acceptDiffAction) {
        val originalContent = originalContentText ?: ""

        diffTracker.trackDiffAfterRevertingChanges(
            originalContent,
            this.generatedContentText,
            generatedFileType.defaultExtension,
            mode
        )

        WriteCommandAction.runWriteCommandAction(project) {
            try {
                virtualFile!!.delete(this)
                afterApply(ApplyResult(null, ""))
            } catch (e: Exception) {
                thisLogger().error(e)
            }
        }

        closeDiffTabOrWindow(diffFileName)
    }

    private fun createRejectButton(buttonAction: String, diffFileName: String) =
        ZenButton.secondary(buttonAction, shortcut = Shortcuts.REJECT_DIFF_ACTION) {
            val originalContent = originalDocument?.text ?: originalContentText ?: ""
            diffTracker.trackDiffRejected(
                originalContent,
                this.generatedContentText,
                generatedFileType.defaultExtension,
                mode
            )

            closeDiffTabOrWindow(diffFileName)
        }

    private fun closeDiffTabOrWindow(diffFileName: String) {
        with(project.service<FileEditorManager>()) {
            allEditors.firstOrNull {
                it.file is ChainDiffVirtualFile && it.file.name == diffFileName
            }?.let {
                this.closeFile(it.file)
            }
        }
    }

    /**
     * Copy-pasted from
     * DiffManagerImpl.showDiffBuiltin(@Nullable Project project, @NotNull DiffRequestChain requests)
     * with only minor modification. Takes fileName to distinguish between different diff tab/windows
     *
     * @param project the current project context.
     * @param requests the chain of diff requests to be processed.
     * @param diffFileName the name of the file to be used for the diff.
     */
    private fun showDiffBuiltin(
        project: Project,
        requests: DiffRequestChain,
        diffFileName: String,
        newFile: Boolean
    ) {
        val diffEditorTabFilesManager = DiffEditorTabFilesManager.getInstance(project)
        val diffFile = ChainDiffVirtualFile(requests, diffFileName)
        if (!newFile) {
            diffFile.putUserData(PATH_KEY, projectPath.actualPath)
            diffFile.putUserData(FS_PATH_KEY, fileAbsolutePath)
        }
        diffEditorTabFilesManager.showDiffFile(diffFile, true)
        return
    }
}

data class ApplyResult(
    val virtualFile: VirtualFile?,
    val content: String
)

private class DiffBottomPanel(val onDispose: () -> Unit) :
    JPanel(),
    Disposable {
    override fun dispose() {
        onDispose()
    }
}

private fun String.convertLineSeparatorToOriginal(original: String?, lineSeparatorInOriginalFile: String? = null): String {
    if (original == null) {
        return this
    }
    val originalLineSeparator = lineSeparatorInOriginalFile ?: original.detectLineSeparator() ?: return this
    val thisLineSeparator = this.detectLineSeparator()
    if (thisLineSeparator == null) {
        return this
    }
    if (originalLineSeparator != thisLineSeparator) {
        return StringUtilRt.convertLineSeparators(this, originalLineSeparator)
    }
    return this
}

fun String.detectLineSeparator(): String? {
    val index = this.indexOfFirst { it == '\r' || it == '\n' }
    if (index == -1) return null
    return when {
        this.startsWith("\r\n", index) -> "\r\n"
        this[index] == '\r' -> "\r"
        this[index] == '\n' -> "\n"
        else -> null
    }
}
