package ai.zencoder.plugin.api

import ai.zencoder.generated.client.codegen.apis.CodegenApi
import ai.zencoder.generated.client.codegen.models.CodeEditRequest
import ai.zencoder.generated.client.codegen.models.CodeEditResponse
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.settings.ZencoderCodeGenServiceUrlChangeListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.ZencoderSettingsEvents
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.util.application

@Service(Level.APP)
class CodeGenService {
    private val authService get() = service<AuthService>()
    private var client = createClient()

    init {
        application.messageBus
            .connect()
            .subscribe(
                ZencoderSettingsEvents.CODE_GEN_SERVICE_URL_CHANGED,
                object : ZencoderCodeGenServiceUrlChangeListener {
                    override fun codeGenServiceUrlChanged() {
                        client = createClient()
                    }
                }
            )
    }

    fun requestCodeGeneration(request: CodeEditRequest): CodeEditResponse {
        return client
            .withAuth(authService.accessToken)
            .inferenceRefactorPost(request)
    }

    private fun createClient() = CodegenApi(basePath = service<ZencoderSettings>().codeGenServiceUrl)
}
