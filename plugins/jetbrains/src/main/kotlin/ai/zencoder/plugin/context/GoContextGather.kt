package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.GoNodeResolver
import com.goide.GoLanguage
import com.goide.psi.GoFieldDeclaration
import com.goide.psi.GoFunctionOrMethodDeclaration
import com.goide.psi.GoImportList
import com.goide.psi.GoReferenceExpression
import com.goide.psi.GoTypeDeclaration
import com.goide.psi.GoTypeReferenceExpression
import com.intellij.lang.Language
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.util.PsiTreeUtil

class GoContextGather : ContextGather {

    private val resolver = GoNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean {
        return language.isKindOf(GoLanguage.INSTANCE)
    }

    // todo: implement for unit tests and docstrings
    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? = when (psiElement) {
        is GoFunctionOrMethodDeclaration -> {
            val name = ApplicationManager.getApplication().runReadAction<String> { psiElement.name }
            val docString = if (extractDocComment) {
                null
            } else {
                null
            }
            ContextSymbolHeader(name = name, kind = NodeType.Method, docstring = docString)
        }

        is GoTypeDeclaration -> {
            val name = ApplicationManager.getApplication().runReadAction<String> { psiElement.text }
            val docString = if (extractDocComment) {
                null
            } else {
                null
            }
            ContextSymbolHeader(name = name, kind = NodeType.Class, docstring = docString)
        }
        else -> null
    }

    override fun readImportsSection(psiFile: PsiFile): String =
        PsiTreeUtil.findChildOfType(psiFile, GoImportList::class.java)?.text.orEmpty()

    // go imports are full packages, so we can just resolve external symbols instead
    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        return emptyList()
    }

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? GoFunctionOrMethodDeclaration
            ?: runReadAction { PsiTreeUtil.getParentOfType(element, GoFunctionOrMethodDeclaration::class.java) }
        val classElement =
            element as? GoTypeDeclaration ?: runReadAction { PsiTreeUtil.getParentOfType(element, GoTypeDeclaration::class.java) }
        val fileTypes = if (classElement == null && methodElement == null) {
            runReadAction {
                PsiTreeUtil.collectElementsOfType(element.containingFile, GoTypeReferenceExpression::class.java).mapNotNull { it.resolve() }
                    .map { PsiTreeUtil.getParentOfType(it, GoTypeDeclaration::class.java) }
                    .toSet()
            }
        } else {
            emptySet()
        }

        val methodTypes = runReadAction { PsiTreeUtil.collectElementsOfType(methodElement, GoReferenceExpression::class.java) }.map {
            runReadAction { it.reference.resolve() }
        }
        val fieldTypes = runReadAction { PsiTreeUtil.collectElementsOfType(classElement, GoFieldDeclaration::class.java) }.mapNotNull {
            it.type?.resolve(it.context)
        }
        val resolved = (fileTypes + methodTypes + fieldTypes).asSequence()
            .map { it }
            .filterNotNull()
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.textOffset) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }.toList()
        return resolved
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)
}
