package ai.zencoder.plugin.socket.ably

import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.services.ProjectUniqueIdentifierService
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.ZencoderApp
import ai.zencoder.plugin.utils.auth.ZenRefreshAuthenticator
import ai.zencoder.plugin.utils.showError
import ai.zencoder.plugin.utils.systemSsl
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.jetbrains.rd.util.AtomicReference
import io.ably.lib.realtime.*
import io.ably.lib.rest.Auth
import io.ably.lib.types.ClientOptions
import io.ably.lib.types.ErrorInfo
import io.ably.lib.util.Log
import io.sentry.okhttp.SentryOkHttpEventListener
import io.sentry.okhttp.SentryOkHttpInterceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import org.awaitility.Awaitility
import org.awaitility.Durations
import org.awaitility.core.ConditionTimeoutException
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@Service(Service.Level.APP)
class AblyConnectionManager : Disposable {
    private val logger = thisLogger()

    private val sessionId = "jb-session-${UUID.randomUUID()}"
    private val httpClient: OkHttpClient = OkHttpClient().newBuilder()
        .systemSsl()
        .eventListener(SentryOkHttpEventListener())
        .addInterceptor(SentryOkHttpInterceptor())
        .authenticator(ZenRefreshAuthenticator)
        .readTimeout(150L, TimeUnit.SECONDS)
        .build()

    private val ablyRealtimeRef = AtomicReference<AblyRealtime?>(null)

    init {
        logger.debug("Initialized AblyConnectionManager with session ID: $sessionId")
    }

    fun refresh() {
        logger.info("Refreshing Ably connection")
        if (service<AuthService>().isAuthenticated()) {
            logger.debug("User is authenticated, disposing existing connection and creating a new one")
            dispose()
            getOrCreateAblyRealtime()
        } else {
            logger.debug("User is not authenticated, skipping refresh")
        }
    }

    fun refreshWithNoChannelsDetach() {
        logger.info("Refreshing Ably connection without detaching channels")
        if (service<AuthService>().isAuthenticated()) {
            logger.debug("User is authenticated, closing existing connection and creating a new one")
            ablyRealtimeRef.getAndSet(null)?.close()
            getOrCreateAblyRealtime()
        } else {
            logger.debug("User is not authenticated, skipping refresh")
        }
    }

    private fun getSocketConnection(): AblyRealtime {
        logger.info("Creating new Ably socket connection")
        val options = ClientOptions().apply {
            logLevel = Log.DEBUG
            logger.debug("Setting up Ably log handler")
            logHandler = Log.LogHandler { severity, tag, msg, tr ->
                val logMessage = if (tag.isNotEmpty()) "[$tag] $msg" else msg

                when (severity) {
                    Log.VERBOSE -> logger.trace(logMessage)
                    Log.DEBUG -> logger.debug(logMessage, tr)
                    Log.INFO -> logger.info(logMessage, tr)
                    Log.WARN -> logger.warn(logMessage, tr)
                    Log.ERROR -> logger.error(logMessage, tr)
                    else -> logger.debug(logMessage, tr)
                }
            }

            logger.debug("Setting up Ably auth callback")
            authCallback = Auth.TokenCallback { _ ->
                logger.debug("Ably auth callback triggered")
                val ablyAuthUrl = service<ZencoderSettings>().ablyAuthUrl
                logger.debug("Using Ably auth URL: $ablyAuthUrl")

                val request = Request.Builder()
                    .url(ablyAuthUrl)
                    .header("Authorization", "Bearer ${service<AuthService>().accessToken}")
                    .get()
                    .build()

                logger.debug("Executing Ably auth request")
                val response = httpClient.newCall(request).execute()

                if (response.body == null) {
                    logger.error("Ably authentication failed: server response is empty")
                    showError("Could not authenticate with Ably, server response is empty")
                    return@TokenCallback null
                }

                val tokenRequestJson = response.body?.string()
                logger.debug("Received Ably token response")

                try {
                    logger.debug("Parsing Ably token request")
                    val tokenRequest = Auth.TokenRequest.fromJson(tokenRequestJson)
                    logger.debug("Successfully parsed Ably token request")
                    return@TokenCallback tokenRequest
                } catch (e: Exception) {
                    logger.warn("Ably token parsing failed", e)
                    dispose()
                    return@TokenCallback null
                }
            }
        }

        logger.info("Creating new AblyRealtime instance")
        return AblyRealtime(options)
    }

    @Synchronized
    private fun getOrCreateAblyRealtime(): AblyRealtime {
        logger.debug("Getting or creating Ably realtime connection")
        val currentAbly = ablyRealtimeRef.get()

        if (currentAbly != null) {
            val connectionState = currentAbly.connection.state
            logger.debug("Existing Ably connection found, state: ${connectionState.name}")

            if (connectionState != ConnectionState.connected) {
                logger.info("Existing Ably connection is not in connected state, disposing")
                dispose()
            } else {
                logger.debug("Reusing existing connected Ably instance")
                return currentAbly
            }
        } else {
            logger.debug("No existing Ably connection found")
        }

        logger.info("Creating new Ably realtime connection")
        val newAbly = getSocketConnection()

        val wasSet = ablyRealtimeRef.compareAndSet(null, newAbly)
        logger.debug("Ably reference was set: $wasSet")

        if (ZencoderApp.isDevMode) {
            logger.debug("Dev mode enabled, adding connection state listener")
            newAbly.connection.on(
                ConnectionStateListener { change ->
                    logger.debug("Ably state changed: ${change.previous.name} -> ${change.current.name}")
                }
            )
        }

        logger.debug("Waiting for Ably connection to initialize")
        try {
            Awaitility
                .await()
                .atMost(Durations.FIVE_SECONDS)
                .pollInterval(100L, TimeUnit.MILLISECONDS)
                .until {
                    val state = newAbly.connection.state
                    val ready = state != ConnectionState.connecting && state != ConnectionState.initialized
                    if (ready) {
                        logger.debug("Ably connection ready, state: ${state.name}")
                    }
                    ready
                }
        } catch (e: ConditionTimeoutException) {
            logger.warn("Slow Ably connection startup, continuing anyway")
        }

        logger.info("Ably realtime connection established, state: ${newAbly.connection.state.name}")
        return newAbly
    }

    fun getUserChannel(): Channel {
        logger.debug("Getting user channel")
        val userId = service<AuthService>().authInfoOrNull()?.userData?.id
        logger.debug("User ID: $userId")

        val ablyRealtime = getOrCreateAblyRealtime()
        logger.debug("Retrieving channel for user: $userId")

        return ablyRealtime.channels[userId]
    }

    fun getOperationSessionChannel(project: Project): Channel {
        logger.debug("Getting operation session channel for project: ${project.name}")
        val channelName = getOperationSessionChannelName(project)
        logger.debug("Operation session channel name: $channelName")

        val ablyRealtime = getOrCreateAblyRealtime()
        return ablyRealtime.channels[channelName]
    }

    fun getOperationSessionChannelName(project: Project): String {
        val userId = service<AuthService>().authInfoOrNull()?.userData?.id
        val projectIdentifier = project.service<ProjectUniqueIdentifierService>().identifier()

        val channelName = "$userId:$sessionId-$projectIdentifier"
        logger.debug("Generated operation session channel name: $channelName")

        return channelName
    }

    suspend fun checkAblyAvailability() = suspendCoroutine { continuation ->
        logger.debug("Checking Ably availability")
        service<AblyConnectionManager>().ping(
            onSuccess = {
                logger.debug("Ably ping successful")
                continuation.resume(true)
            },
            onFailure = {
                logger.warn("Ably ping failed")
                continuation.resume(false)
            }
        )
    }

    private fun ping(onSuccess: () -> Unit, onFailure: () -> Unit) {
        logger.debug("Pinging Ably connection")
        getOrCreateAblyRealtime().connection.ping(
            object : CompletionListener {
                override fun onSuccess() {
                    logger.debug("Ably ping completed successfully")
                    onSuccess.invoke()
                }

                override fun onError(reason: ErrorInfo?) {
                    logger.warn("Ably ping failed: ${reason?.message}")
                    onFailure.invoke()
                }
            }
        )
    }

    override fun dispose() {
        logger.info("Disposing Ably connection manager")
        val oldAbly = ablyRealtimeRef.getAndSet(null)

        if (oldAbly != null) {
            logger.debug("Closing existing Ably connection")
            oldAbly.run {
                channels.values().forEach { channel ->
                    logger.trace("Detaching from channel: ${channel.name}")
                    channel.detach()
                }

                logger.debug("Closing Ably connection")
                close()
            }
            logger.info("Ably connection disposed successfully")
        } else {
            logger.debug("No Ably connection to dispose")
        }
    }
}
