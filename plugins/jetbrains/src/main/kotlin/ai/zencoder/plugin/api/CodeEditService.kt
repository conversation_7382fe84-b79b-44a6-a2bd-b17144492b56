package ai.zencoder.plugin.api

import ai.zencoder.generated.client.all.apis.CodeeditApi
import ai.zencoder.generated.client.all.models.CodeeditCodeEditResponse
import ai.zencoder.generated.client.all.models.CodeeditGenerateDescriptionRequest
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.settings.ZencoderCodeGenServiceUrlChangeListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.ZencoderSettingsEvents
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.util.application

@Service(Level.APP)
class CodeEditService {
    private val authService get() = service<AuthService>()
    private var client = createClient()

    init {
        application.messageBus
            .connect()
            .subscribe(
                ZencoderSettingsEvents.CODE_GEN_SERVICE_URL_CHANGED,
                object : ZencoderCodeGenServiceUrlChangeListener {
                    override fun codeGenServiceUrlChanged() {
                        client = createClient()
                    }
                }
            )
    }

    fun requestCodeeditGenerateDescription(request: CodeeditGenerateDescriptionRequest): CodeeditCodeEditResponse {
        return client
            .withAuth(authService.accessToken)
            .codeeditGenerateDescriptionGenerateDescriptionPost(request)
    }

    private fun createClient() = CodeeditApi(
        basePath = service<ZencoderSettings>()
            .codeGenServiceUrl
            .removeSuffix("/codeedit") // CodeeditApi already has /codeedit in path by default
    )
}
