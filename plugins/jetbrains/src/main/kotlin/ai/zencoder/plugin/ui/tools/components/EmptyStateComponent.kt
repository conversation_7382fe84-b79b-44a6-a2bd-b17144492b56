package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import java.awt.BorderLayout
import java.awt.Component
import java.awt.Dimension
import java.awt.event.ActionListener
import javax.swing.*

/**
 * Component for displaying an empty state with title, message, and action.
 * Follows JetBrains UI guidelines for empty states.
 */
class EmptyStateComponent(
    private val title: String,
    private val message: String,
    private val actionText: String
) : InteractiveComponent<ActionListener> {

    private val panel = JPanel()
    private val actionButton = JButton(actionText)
    private var actionHandler: ActionListener? = null

    init {
        setupPanel()
    }

    private fun setupPanel() {
        panel.apply {
            layout = BoxLayout(this, BoxLayout.Y_AXIS)
            border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.XXLARGE)
            isOpaque = false

            // Create a panel to center all content
            val contentPanel = JPanel().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                isOpaque = false
                alignmentX = Component.CENTER_ALIGNMENT
                // Ensure minimum width for text
                minimumSize = Dimension(UIStyleUtils.Spacing.XLARGE * 10, 0)
            }

            // 1. Reason why empty - Title with proper typography
            val titleLabel = JBLabel(title).apply {
                font = UIStyleUtils.Typography.SUBTITLE_FONT
                horizontalAlignment = SwingConstants.CENTER
                alignmentX = Component.CENTER_ALIGNMENT
            }
            contentPanel.add(titleLabel)

            // Standard spacing between title and message
            contentPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))

            // 2. Message explaining the empty state with proper styling
            val messageLabel = JBLabel(message).apply {
                foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
                font = UIStyleUtils.Typography.MEDIUM_FONT
                horizontalAlignment = SwingConstants.CENTER
                alignmentX = Component.CENTER_ALIGNMENT
            }
            contentPanel.add(messageLabel)

            // Standard spacing between message and action
            contentPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.LARGE))

            // 3. Action to fill the area - Add a button styled as a link
            actionButton.apply {
                isBorderPainted = false
                isContentAreaFilled = false
                isFocusPainted = true
                foreground = UIStyleUtils.Colors.LINK_COLOR
                font = UIStyleUtils.Typography.MEDIUM_FONT
                cursor = java.awt.Cursor.getPredefinedCursor(java.awt.Cursor.HAND_CURSOR)
                horizontalAlignment = SwingConstants.CENTER
                alignmentX = Component.CENTER_ALIGNMENT

                // Add the action handler if set
                actionHandler?.let { addActionListener(it) }
            }
            contentPanel.add(actionButton)

            // Add vertical glue to push content to the center
            add(Box.createVerticalGlue())
            add(contentPanel)
            add(Box.createVerticalGlue())
        }
    }

    override fun createComponent(): JComponent = panel

    override fun setEventHandler(handler: ActionListener) {
        actionHandler = handler
        actionButton.addActionListener(handler)
    }

    /**
     * Creates a wrapper panel that centers the empty state component.
     * @return A panel containing the centered empty state
     */
    fun createCenteredComponent(): JComponent {
        return JPanel(BorderLayout()).apply {
            isOpaque = false
            background = JBColor.background()
            add(createComponent(), BorderLayout.CENTER)
        }
    }
}
