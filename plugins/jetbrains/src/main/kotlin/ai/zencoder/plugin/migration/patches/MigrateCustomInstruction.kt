package ai.zencoder.plugin.migration.patches

import ai.zencoder.plugin.migration.ZencoderProjectPatch
import ai.zencoder.plugin.migration.ZencoderProjectPatch.ProjectContext
import ai.zencoder.plugin.webview.chat.CHAT_STATE_NAME
import ai.zencoder.plugin.webview.chat.model.CustomInstructionStateManager
import com.intellij.conversion.WorkspaceSettings
import com.intellij.openapi.components.service
import org.jdom.Element

private const val CUSTOM_INSTRUCTION_ATTRIBUTE = "customInstruction"

object MigrateCustomInstruction : ZencoderProjectPatch {
    private val state get() = service<CustomInstructionStateManager>()

    override val idSuffix = "migrate-custom-instruction"

    override fun isNeeded(ctx: ProjectContext): Boolean {
        val workspaceXml = ctx.conversionContext.workspaceSettings
        return getCustomInstructionElement(workspaceXml) != null
            && state.customInstruction == null
    }

    override fun doPatch(ctx: ProjectContext) {
        val workspaceXml = ctx.conversionContext.workspaceSettings
        val element = getCustomInstructionElement(workspaceXml)
            ?: error("Missing custom instruction element")

        // two attributes: one has value "customInstruction",
        // and another one has the exact value
        state.customInstruction = element.attributes
            .find { it.value != CUSTOM_INSTRUCTION_ATTRIBUTE }
            ?.value
            ?.takeIf { it.isNotEmpty() }
        element.parent.content.remove(element)
    }

    private fun getCustomInstructionElement(workspaceXml: WorkspaceSettings): Element? {
        return workspaceXml.getComponentElement(CHAT_STATE_NAME)
            ?.content
            ?.asSequence()
            ?.mapNotNull { it as? Element }
            ?.filter { it.attributes.any { attr -> attr.value == CUSTOM_INSTRUCTION_ATTRIBUTE } }
            ?.firstOrNull()
    }
}
