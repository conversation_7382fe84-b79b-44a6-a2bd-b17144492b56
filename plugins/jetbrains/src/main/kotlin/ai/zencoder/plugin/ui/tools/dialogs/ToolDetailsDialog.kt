package ai.zencoder.plugin.ui.tools.dialogs

import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.ServerConfig
import ai.zencoder.plugin.mcp.SseServerConfig
import ai.zencoder.plugin.mcp.StdioServerConfig
import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.mcp.service.MCPToolsRegistryService
import ai.zencoder.plugin.ui.tools.components.ToolCardComponent
import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.icons.AllIcons
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.ComboBox
import com.intellij.openapi.ui.DialogWrapper
import com.intellij.openapi.ui.Messages
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import com.intellij.ui.components.JBScrollPane
import com.intellij.ui.components.JBTextArea
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.awt.*
import java.awt.event.ActionEvent
import javax.swing.*
import javax.swing.event.DocumentEvent
import javax.swing.event.DocumentListener

/**
 * Dialog for tool details and configuration.
 * Follows Single Responsibility Principle by focusing only on tool details display and editing.
 */
class ToolDetailsDialog(
    private val project: Project,
    private val toolId: String,
    private val tool: McpServerInfo,
    private val toolsRegistryService: MCPToolsRegistryService,
    private val coroutineScope: CoroutineScope
) : DialogWrapper(project) {

    private val jsonFormatter = Json { prettyPrint = true }
    private val configTextArea = JBTextArea()
    private val configComboBox = ComboBox<String>()
    private var selectedConfigIndex = 0
    private var initialConfig: String
    private var jsonConfig: String

    init {
        title = tool.name
        init()

        // Initialize with the current config
        initialConfig = jsonFormatter.encodeToString(
            tool.status.config ?: (tool.configs?.getOrNull(0) ?: StdioServerConfig(command = ""))
        )
        jsonConfig = initialConfig
        configTextArea.text = jsonConfig
    }

    override fun createCenterPanel(): JComponent {
        val panel = JPanel(BorderLayout()).apply {
            preferredSize = Dimension(600, 500)
        }

        // Tool info panel
        val infoPanel = JPanel().apply {
            layout = BoxLayout(this, BoxLayout.Y_AXIS)
            border = UIStyleUtils.Borders.emptyBorder(0, 0, UIStyleUtils.Spacing.LARGE, 0)
        }

        // Header panel with icon and name
        val headerPanel = JPanel(BorderLayout()).apply {
            isOpaque = false
            alignmentX = Component.LEFT_ALIGNMENT
            border = UIStyleUtils.Borders.emptyBorder(0, 0, UIStyleUtils.Spacing.LARGE, 0)
        }

        // Add icon if available
        val iconPanel = createToolIconPanel().apply {
            preferredSize = Dimension(80, 80)
            minimumSize = Dimension(80, 80)
            border = UIStyleUtils.Borders.emptyBorder(0, 0, 0, UIStyleUtils.Spacing.LARGE)
        }
        headerPanel.add(iconPanel, BorderLayout.WEST)

        // Right side of header with name and author
        val headerRightPanel = JPanel().apply {
            layout = BoxLayout(this, BoxLayout.Y_AXIS)
            isOpaque = false
            alignmentX = Component.LEFT_ALIGNMENT
        }

        // Tool name with larger, bold font
        val nameLabel = JBLabel(tool.name.toString()).apply {
            font = UIStyleUtils.Typography.TITLE_FONT
            alignmentX = Component.LEFT_ALIGNMENT
        }
        headerRightPanel.add(nameLabel)

        // Add spacing between name and author
        headerRightPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))

        // Author info with @ prefix
        val authorText = if (tool.author != null && tool.author.isNotEmpty()) {
            "@${tool.author}"
        } else {
            ""
        }

        val authorLabel = JBLabel(authorText).apply {
            foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
            font = UIStyleUtils.Typography.MEDIUM_FONT
            alignmentX = Component.LEFT_ALIGNMENT
        }
        headerRightPanel.add(authorLabel)

        headerPanel.add(headerRightPanel, BorderLayout.CENTER)

        infoPanel.add(headerPanel)

        // Tool link
        if (tool.link != null) {
            val linkLabel = JBLabel("<html><a href='${tool.link}'>${tool.link}</a></html>").apply {
                alignmentX = Component.LEFT_ALIGNMENT
                cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
            }
            infoPanel.add(linkLabel)
        }

        // Tool description
        if (tool.description != null) {
            val descriptionLabel = JBLabel(tool.description).apply {
                alignmentX = Component.LEFT_ALIGNMENT
                border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.SMALL, 0, 0, 0)
            }
            infoPanel.add(descriptionLabel)
        }

        // Community warning
        if (tool.author != null && tool.author != "zencoder") {
            val warningPanel = JPanel(BorderLayout()).apply {
                border = BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(JBColor.YELLOW, 1),
                    UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.SMALL)
                )
                background = JBColor(Color(255, 250, 220), Color(70, 60, 30))
            }

            val warningLabel = JBLabel(ZencoderBundle["mcp.tools.panel.dialog.warning.community"]).apply {
                icon = AllIcons.General.BalloonInformation
            }
            warningPanel.add(warningLabel, BorderLayout.CENTER)
            infoPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.LARGE))
            infoPanel.add(warningPanel)
        }

        // Error message
        if (tool.status.status == ToolStatus.ERROR && tool.status.detail != null) {
            val errorPanel = JPanel(BorderLayout()).apply {
                border = BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(JBColor.RED, 1),
                    UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.SMALL)
                )
                background = JBColor(Color(255, 235, 235), Color(70, 30, 30))
            }

            val errorLabel = JBLabel(tool.status.detail.toString()).apply {
                icon = AllIcons.General.Error
            }
            errorPanel.add(errorLabel, BorderLayout.CENTER)
            infoPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.LARGE))
            infoPanel.add(errorPanel)
        }

        // Installation method for library tools
        if (tool.author != null && tool.author != "zencoder" && tool.status.status != ToolStatus.INSTALLED) {
            val methodPanel = JPanel().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.LARGE, 0, 0, 0)
                alignmentX = Component.LEFT_ALIGNMENT
            }

            val methodLabel = JBLabel(ZencoderBundle["mcp.tools.panel.dialog.install.method"]).apply {
                font = UIStyleUtils.Typography.MEDIUM_FONT.asBold()
                alignmentX = Component.LEFT_ALIGNMENT
            }
            methodPanel.add(methodLabel)

            val methodDescLabel = JBLabel(ZencoderBundle["mcp.tools.panel.dialog.install.description"]).apply {
                alignmentX = Component.LEFT_ALIGNMENT
                foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
            }
            methodPanel.add(methodDescLabel)

            // Config selection combo box
            if (tool.configs != null && tool.configs.size > 1) {
                configComboBox.removeAllItems()
                tool.configs.forEachIndexed { index, config ->
                    when (config) {
                        is StdioServerConfig -> configComboBox.addItem(config.command)
                        is SseServerConfig -> configComboBox.addItem(config.url)
                        else -> {}
                    }
                }
                configComboBox.selectedIndex = selectedConfigIndex
                configComboBox.addActionListener {
                    selectedConfigIndex = configComboBox.selectedIndex
                    val selectedConfig = tool.configs[selectedConfigIndex]
                    jsonConfig = jsonFormatter.encodeToString(selectedConfig)
                    configTextArea.text = jsonConfig
                }

                methodPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))
                methodPanel.add(configComboBox)
            }

            infoPanel.add(methodPanel)
        }

        // Config panel for non-Zencoder tools
        if (tool.author != "zencoder") {
            val configPanel = JPanel().apply {
                layout = BoxLayout(this, BoxLayout.Y_AXIS)
                border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.LARGE, 0, 0, 0)
                alignmentX = Component.LEFT_ALIGNMENT
            }

            val configLabel = JBLabel(ZencoderBundle["mcp.tools.panel.dialog.config.title"]).apply {
                font = UIStyleUtils.Typography.MEDIUM_FONT.asBold()
                alignmentX = Component.LEFT_ALIGNMENT
            }
            configPanel.add(configLabel)

            val configDescLabel = JBLabel(ZencoderBundle["mcp.tools.panel.dialog.config.description"]).apply {
                alignmentX = Component.LEFT_ALIGNMENT
                foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
            }
            configPanel.add(configDescLabel)

            // Config text area
            configTextArea.font = Font.decode("monospaced")
            configTextArea.text = jsonConfig
            configTextArea.border = BorderFactory.createLineBorder(JBColor.border())
            configTextArea.document.addDocumentListener(object : DocumentListener {
                override fun insertUpdate(e: DocumentEvent) = updateConfig()
                override fun removeUpdate(e: DocumentEvent) = updateConfig()
                override fun changedUpdate(e: DocumentEvent) = updateConfig()

                private fun updateConfig() {
                    jsonConfig = configTextArea.text
                }
            })

            val scrollPane = JBScrollPane(configTextArea)
            configPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))
            configPanel.add(scrollPane)

            infoPanel.add(configPanel)
        }

        panel.add(infoPanel, BorderLayout.CENTER)
        return panel
    }

    override fun createActions(): Array<Action> {
        val actions = mutableListOf<Action>()

        // Don't show action buttons for Zencoder tools
        if (tool.author != "zencoder") {
            // Install/Uninstall/Save button
            val primaryAction = object : DialogWrapperAction(getActionText()) {
                override fun doAction(e: ActionEvent) {
                    handleToolAction()
                }
            }
            actions.add(primaryAction)
        }

        // Cancel button
        actions.add(cancelAction)
        return actions.toTypedArray()
    }

    private fun getActionText(): String {
        return when (tool.status.status) {
            ToolStatus.INSTALLED -> if (jsonConfig == initialConfig) {
                ZencoderBundle["mcp.tools.panel.dialog.button.uninstall"]
            } else {
                ZencoderBundle["mcp.tools.panel.dialog.button.save"]
            }
            ToolStatus.INSTALLING -> ZencoderBundle["mcp.tools.panel.dialog.button.installing"]
            else -> ZencoderBundle["mcp.tools.panel.dialog.button.install"]
        }
    }

    private fun handleToolAction() {
        try {
            when (tool.status.status) {
                ToolStatus.INSTALLED -> {
                    if (jsonConfig == initialConfig) {
                        // Uninstall
                        coroutineScope.launch {
                            toolsRegistryService.uninstallTool(toolId)
                            close(OK_EXIT_CODE)
                        }
                    } else {
                        // Update config
                        val config = jsonFormatter.decodeFromString<ServerConfig>(jsonConfig)
                        coroutineScope.launch {
                            toolsRegistryService.updateToolConfig(toolId, config)
                            close(OK_EXIT_CODE)
                        }
                    }
                }

                ToolStatus.INSTALLING -> {
                    // Do nothing while installing
                }

                else -> {
                    // Install
                    try {
                        val config = jsonFormatter.decodeFromString<ServerConfig>(jsonConfig)
                        coroutineScope.launch {
                            toolsRegistryService.installTool(toolId, null, config)
                            close(OK_EXIT_CODE)
                        }
                    } catch (e: Exception) {
                        Messages.showErrorDialog(
                            ZencoderBundle["mcp.tools.panel.dialog.error.json", e.message.toString()],
                            ZencoderBundle["mcp.tools.panel.dialog.error.title"]
                        )
                    }
                }
            }
        } catch (e: Exception) {
            Messages.showErrorDialog(
                ZencoderBundle["mcp.tools.panel.dialog.error.operation", e.message.toString()],
                ZencoderBundle["mcp.tools.panel.dialog.error.operation.title"]
            )
        }
    }

    override fun getPreferredFocusedComponent(): JComponent? {
        return configTextArea
    }

    private fun createToolIconPanel(): JPanel {
        // Create a panel that fills the entire left column
        val iconPanel = JPanel(GridBagLayout()).apply {
            isOpaque = false
            minimumSize = Dimension(80, 80)
            preferredSize = Dimension(80, 80)
            maximumSize = Dimension(80, 80)
        }

        // Create the icon label with center alignment
        val iconLabel = JLabel().apply {
            horizontalAlignment = SwingConstants.CENTER
            verticalAlignment = SwingConstants.CENTER
            horizontalTextPosition = SwingConstants.CENTER
            verticalTextPosition = SwingConstants.BOTTOM
        }

        // Set the icon
        iconLabel.icon = ToolCardComponent.Companion.getToolIcon(tool)

        // Add the icon label to the panel with GridBagConstraints to center it
        val gbc = GridBagConstraints().apply {
            gridx = 0
            gridy = 0
            fill = GridBagConstraints.BOTH
            weightx = 1.0
            weighty = 1.0
        }
        iconPanel.add(iconLabel, gbc)

        return iconPanel
    }
}
