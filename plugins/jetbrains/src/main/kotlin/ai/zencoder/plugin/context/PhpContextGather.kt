package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.PhpNodeResolver
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import com.intellij.lang.Language
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.project.Project
import com.intellij.psi.*
import com.intellij.psi.util.PsiTreeUtil
import com.jetbrains.php.PhpIndex
import com.jetbrains.php.lang.PhpLanguage
import com.jetbrains.php.lang.psi.elements.Field
import com.jetbrains.php.lang.psi.elements.Method
import com.jetbrains.php.lang.psi.elements.PhpClass
import com.jetbrains.php.lang.psi.elements.PhpReference
import com.jetbrains.php.lang.psi.elements.PhpUse
import com.jetbrains.php.lang.psi.elements.PhpUseList
import com.jetbrains.php.lang.psi.resolve.types.PhpType

class PhpContextGather : ContextGather {

    private val resolver = PhpNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean {
        return language.isKindOf(PhpLanguage.INSTANCE)
    }

    // todo: implement for unit tests and docstrings
    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? = when (psiElement) {
        is Method -> {
            val name = runReadAction { psiElement.name }
            val docString = if (extractDocComment) {
                runReadAction { psiElement.docComment?.text }
            } else {
                null
            }
            ContextSymbolHeader(name = name, kind = NodeType.Method, docstring = docString)
        }

        is PhpClass -> {
            val name = runReadAction { psiElement.name }
            val docString = if (extractDocComment) {
                runReadAction { psiElement.docComment?.text }
            } else {
                null
            }
            ContextSymbolHeader(name = name, kind = NodeType.Class, docstring = docString)
        }

        else -> null
    }

    override fun readImportsSection(psiFile: PsiFile): String =
        PsiTreeUtil.findChildrenOfType(psiFile, PhpUseList::class.java).joinToString("\n") { it.text }

    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        val importSignatures = mutableSetOf<ImportSignature>()
        val psiFile = runReadAction { element.containingFile }
            ?: return emptyList()
        for (importElement in runReadAction { PsiTreeUtil.findChildrenOfType(psiFile, PhpUse::class.java) }) {
            val type = runReadAction { importElement.type }
            runReadAction {
                resolver.toResolvedASTNode(type.resolve(element.project), zencoderFeatureFlags.enableContextExternalReferenceGathering)
                    ?.let {
                        importSignatures.add(
                            ImportSignature(importElement.text, it)
                        )
                    }
            }
        }
        return importSignatures.toList()
    }

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? Method ?: runReadAction { PsiTreeUtil.getParentOfType(element, Method::class.java) }
        val classElement = element as? PhpClass ?: runReadAction { PsiTreeUtil.getParentOfType(element, PhpClass::class.java) }
        val fileTypes = if (classElement == null && methodElement == null) {
            runReadAction {
                PsiTreeUtil.collectElementsOfType(element.containingFile, PhpReference::class.java).map { it.type }
                    .toSet()
            }
        } else {
            emptySet()
        }

        val methodTypes = runReadAction { PsiTreeUtil.collectElementsOfType(methodElement, PhpReference::class.java).map { it.type } }
        val fieldTypes = runReadAction { PsiTreeUtil.collectElementsOfType(classElement, Field::class.java).map { it.declaredType } }
        val resolved = (fileTypes + methodTypes + fieldTypes).asSequence().map {
            runReadAction { it.resolve(element.project) }
        }.filterNotNull()
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.textOffset) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }.toList()
        return resolved
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)

    private fun PhpType.resolve(project: Project): PsiElement? {
        return PhpIndex.getInstance(project).getAnyByFQN(toStringResolved()).firstOrNull()
    }
}
