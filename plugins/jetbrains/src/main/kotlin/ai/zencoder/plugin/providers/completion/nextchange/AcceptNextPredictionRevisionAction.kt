package ai.zencoder.plugin.providers.completion.nextchange

import com.intellij.codeInsight.hint.HintManagerImpl
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorWriteActionHandler

class AcceptNextPredictionRevisionAction :
    EditorAction(Handler()),
    HintManagerImpl.ActionToIgnore {

    companion object {
        const val ID = "zencoder.acceptNextPrediction"
    }

    private class Handler : EditorWriteActionHandler() {

        override fun doExecute(
            editor: Editor,
            caret: Caret?,
            dataContext: DataContext?
        ) {
            CodeSuggestionDiffViewer.acceptPrediction(editor)
        }

        override fun isEnabledForCaret(
            editor: Editor,
            caret: Caret,
            dataContext: DataContext
        ): <PERSON><PERSON><PERSON> {
            val diffViewer = editor.getUserData(ZencoderKeys.EDITOR_PREDICTION_DIFF_VIEWER)
            return diffViewer != null && diffViewer.isVisible()
        }
    }
}
