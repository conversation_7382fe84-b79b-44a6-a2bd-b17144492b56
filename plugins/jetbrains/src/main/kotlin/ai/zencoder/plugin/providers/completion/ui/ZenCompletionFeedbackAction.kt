package ai.zencoder.plugin.providers.completion.ui

import ai.zencoder.generated.client.feedback.models.UserFeedback
import ai.zencoder.generated.client.feedback.models.UserFeedbackRequest
import ai.zencoder.plugin.api.UserFeedbackService
import ai.zencoder.plugin.core.ZencoderAction
import ai.zencoder.plugin.providers.completion.COMPLETION_TEXT_KEY
import ai.zencoder.plugin.providers.completion.nextchange.ZencoderKeys
import ai.zencoder.plugin.providers.completion.nextchange.ZencoderKeys.NEXT_EDIT_REQUEST_ID
import ai.zencoder.plugin.ui.TextAreaWithPlaceholder
import ai.zencoder.plugin.ui.ZenButton
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.CommonDataKeys
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopup
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.util.ui.JBUI.Borders
import net.miginfocom.swing.MigLayout
import javax.swing.*

class ZenCompletionFeedbackAction : ZencoderAction() {

    private var popup: JBPopup? = null

    override fun doAction(project: Project, event: AnActionEvent) {
        val editor = event.getData(CommonDataKeys.EDITOR) ?: return
        showPopupInEditor(editor)
    }

    private fun showPopupInEditor(editor: Editor) {
        // Check if we have a completion or if we have an edit diff viewer open
        val completion = editor.getUserData(COMPLETION_TEXT_KEY)
        val nextEditRequestId = editor.getUserData(NEXT_EDIT_REQUEST_ID)
        val diffViewerOpen = editor.getUserData(ZencoderKeys.EDITOR_PREDICTION_DIFF_VIEWER) != null

        // Get requestId based on the context:
        // - If diffViewerOpen is true, use nextEditRequestId
        // - Otherwise, use completion.requestId
        val requestId = if (diffViewerOpen && nextEditRequestId != null) {
            nextEditRequestId
        } else {
            completion?.requestId ?: return
        }

        val suggestionText = if (diffViewerOpen && nextEditRequestId != null) {
            "Next edit prediction"
        } else {
            completion?.text ?: ""
        }

        val mainPanel = JPanel(MigLayout("fillx,wrap 2"))
        val topLabel =
            JLabel(
                """
            Help us improve the service by providing an alternative code version.
                """.trimIndent()
            )
        mainPanel.add(topLabel, "span 2 ")
        mainPanel.add(JLabel("Suggested:"))
        val suggestedCompletionTextArea = JTextArea(suggestionText, 5, 60).apply {
            isEnabled = true
            isEditable = false
            lineWrap = true
            foreground = disabledTextColor
        }
        mainPanel.add(JScrollPane(suggestedCompletionTextArea), "")

        mainPanel.add(JLabel("Expected:"))
        val expectedCompletionTextArea =
            TextAreaWithPlaceholder(placeHolder = "The code you had expected", rows = 5, columns = 60)
        mainPanel.add(JScrollPane(expectedCompletionTextArea), "")

        mainPanel.add(JLabel("Comments:"))
        val commentTextArea =
            TextAreaWithPlaceholder(placeHolder = "Why the original suggestion is not good enough", rows = 5, columns = 60)
        mainPanel.add(JScrollPane(commentTextArea), "")
        val requestIdPanel = JPanel(MigLayout("align right"))
        requestIdPanel.add(JLabel("Request ID:"))
        requestIdPanel.add(
            JTextField(requestId).apply {
                isEditable = false
                border = Borders.empty()
                background = mainPanel.background
            }
        )
        mainPanel.add(requestIdPanel, "span 2, align right")
        val buttonPanel = JPanel(MigLayout("align right"))
        val button1 = ZenButton.secondary("Cancel") { closePopup() }
        val button2 = ZenButton.primary("OK") {
            submitFeedback(requestId, expectedCompletionTextArea.text, commentTextArea.text)
        }
        buttonPanel.add(button1)
        buttonPanel.add(button2)

        // Add the button panel to the main panel
        mainPanel.add(buttonPanel, "span 2, align right")
        popup = JBPopupFactory.getInstance().createComponentPopupBuilder(mainPanel, null)
            .setTitle("Send Feedback")
            .setCancelOnClickOutside(false)
            .setResizable(false)
            .setRequestFocus(true)
            .setCancelOnWindowDeactivation(false)
            .createPopup()
        val editorComponent = editor.contentComponent
        popup?.showInCenterOf(editorComponent)
    }

    private fun closePopup() {
        popup?.cancel()
        popup = null
    }

    private fun submitFeedback(
        requestId: String,
        expectedText: String,
        comment: String
    ) {
        if (expectedText.isBlank() && comment.isBlank()) {
            return
        }
        service<UserFeedbackService>().submitUserFeedbackDeliberate(
            UserFeedbackRequest(
                requestId = requestId,
                ideName = UserFeedbackRequest.IdeName.jetbrains,
                feedback = mapOf(
                    "feedback_form" to UserFeedback(
                        text = comment,
                        accepted = false,
                        modification = expectedText
                    )
                )
            )
        )
        closePopup()
    }
}
