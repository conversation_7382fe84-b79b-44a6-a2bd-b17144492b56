package ai.zencoder.plugin.providers.completion.nextchange

import com.intellij.openapi.util.Key

object ZencoderKeys {
    val REMAINING_EDITOR_COMPLETION: Key<String> = Key.create("zencoder.editorCompletionLines")
    val EDITOR_PREDICTION_DIFF_VIEWER: Key<CodeSuggestionDiffViewer> = Key.create("zencoder.editorPredictionDiffViewer")
    val NEXT_EDIT_REQUEST_ID: Key<String> = Key.create("zencoder.nextEditRequestId")
}
