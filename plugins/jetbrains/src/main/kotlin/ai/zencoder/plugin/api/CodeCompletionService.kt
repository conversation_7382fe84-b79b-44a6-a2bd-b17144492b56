package ai.zencoder.plugin.api

import ai.zencoder.generated.client.completion.apis.CodeCompletionApi
import ai.zencoder.generated.client.completion.models.CodeCompletionRequest
import ai.zencoder.generated.client.completion.models.CodeCompletionResponse
import ai.zencoder.generated.client.completion.models.EditPredictionRequest
import ai.zencoder.generated.client.completion.models.EditPredictionResponse
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.settings.ZencoderCodeCompletionUrlChangeListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.ZencoderSettingsEvents
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.util.application
import com.intellij.util.io.await
import io.sentry.ISpan

@Service(Level.APP)
class CodeCompletionService {
    private val authService get() = service<AuthService>()
    private var client = createClient()

    init {
        application.messageBus
            .connect()
            .subscribe(
                ZencoderSettingsEvents.CODE_COMPLETION_SETTINGS_CHANGED,
                object : ZencoderCodeCompletionUrlChangeListener {
                    override fun codeCompletionUrlChanged() {
                        client = createClient()
                    }
                }
            )
    }

    suspend fun requestCodeCompletion(request: CodeCompletionRequest, sentryTransaction: ISpan): CodeCompletionResponse {
        return client
            .withAuth(authService.accessToken)
            .inferenceInferencePostAsync(sentryTransaction.toSentryTrace().value, request).await()
    }

    suspend fun requestNextEditPrediction(request: EditPredictionRequest): EditPredictionResponse {
        return client
            .withAuth(authService.accessToken)
            .nextEditPredictionPostAsync(request).await()
    }

    private fun createClient() = CodeCompletionApi(basePath = service<ZencoderSettings>().codeCompletionServiceUrl)
}
