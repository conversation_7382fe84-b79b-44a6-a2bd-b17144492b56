package ai.zencoder.plugin.settings

import com.intellij.util.messages.Topic

object ZencoderSettingsEvents {
    @Topic.AppLevel
    val AGENTS_URL_SETTINGS_CHANGED = Topic.create("Zencoder Agents URL Changed", ZencoderAgentsUrlChangeListener::class.java)

    @Topic.AppLevel
    val CODE_COMPLETION_SETTINGS_CHANGED = Topic.create(
        "Zencoder Code Completion Url Changed",
        ZencoderCodeCompletionUrlChangeListener::class.java
    )

    @Topic.AppLevel
    val FEEDBACK_SERVICE_URL_CHANGED = Topic.create(
        "Zencoder Feedback Service Url Changed",
        ZencoderFeedbackServiceUrlChangeListener::class.java
    )

    @Topic.AppLevel
    val CODE_GEN_SERVICE_URL_CHANGED = Topic.create(
        "Zencoder Code Gen Service Url Changed",
        ZencoderCodeGenServiceUrlChangeListener::class.java
    )

    @Topic.AppLevel
    val CHAT_SERVICE_URL_CHANGED = Topic.create(
        "Zencoder Chat Service Url Changed",
        ZencoderChatServiceUrlChangeListener::class.java
    )
}

interface ZencoderAgentsUrlChangeListener {
    fun agentsServiceUrlChanged()
}

interface ZencoderCodeCompletionUrlChangeListener {
    fun codeCompletionUrlChanged()
}

interface ZencoderFeedbackServiceUrlChangeListener {
    fun feedbackServiceUrlChanged()
}

interface ZencoderCodeGenServiceUrlChangeListener {
    fun codeGenServiceUrlChanged()
}

interface ZencoderChatServiceUrlChangeListener {
    fun chatServiceUrlChanged()
}
