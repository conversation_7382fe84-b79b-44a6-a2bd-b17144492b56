package ai.zencoder.plugin.providers.completion.nextchange

import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.settings.ZencoderSettings
import com.intellij.codeInsight.hint.HintManagerImpl
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Caret
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.actionSystem.EditorAction
import com.intellij.openapi.editor.actionSystem.EditorWriteActionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class TriggerCustomPredictionAction :
    EditorAction(Handler()),
    HintManagerImpl.ActionToIgnore {

    companion object {
        const val ID = "zencoder.triggerCustomPrediction"
    }

    private class Handler : EditorWriteActionHandler() {

        override fun doExecute(
            editor: Editor,
            caret: Caret?,
            dataContext: DataContext?
        ) {
            if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
                return
            }
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    // Use NextEditPredictionHandler to ensure proper request tracking
                    val nextEditHandler = service<NextEditPredictionHandler>()
                    nextEditHandler.onCompletionAbsent(editor)
                } catch (e: Exception) {
                    thisLogger().warn("Failed to trigger next edit prediction", e)
                }
            }
        }

        override fun isEnabledForCaret(
            editor: Editor,
            caret: Caret,
            dataContext: DataContext
        ): Boolean {
            val diffViewer = editor.getUserData(ZencoderKeys.EDITOR_PREDICTION_DIFF_VIEWER)
            val isEnabled = diffViewer == null || !diffViewer.isVisible()
            thisLogger().warn("isEnabledForCaret: diffViewer is null? ${diffViewer == null}, isVisible? ${diffViewer?.isVisible()}")
            return isEnabled
        }
    }
}
