package ai.zencoder.plugin.mcp

import ai.zencoder.generated.client.all.models.ZencoderWorkflowToolkitType
import ai.zencoder.plugin.mcp.sdk.*
import ai.zencoder.plugin.mcp.sdk.client.*
import ai.zencoder.plugin.mcp.sdk.shared.RequestOptions
import ai.zencoder.plugin.mcp.tools.StdioClient
import ai.zencoder.plugin.services.ZencoderPluginProjectDisposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import io.github.oshai.kotlinlogging.KotlinLogging
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.sse.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive
import java.io.File
import java.util.concurrent.ConcurrentHashMap
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

private val logger = KotlinLogging.logger {}

private const val INTERNAL_MCP_SERVER_NAME = "zencoder"

private val WAIT_TIMEOUT = 60.seconds

@Service(PROJECT)
class MCPClientsStateManager(
    private val project: Project,
    private val cs: CoroutineScope
) {
    private val configuredClients = ConcurrentHashMap<ServerId, Client>()
    private val clientTools = ConcurrentHashMap<String, Client>()
    private val serverTools = ConcurrentHashMap<ServerId, List<String>>()
    private val stateManager get() = project.service<MCPServersStateManager>()

    suspend fun initialize(request: InitializeRequest?): InitializeResult {
        stateManager.getAllServerConfigs().forEach { (server, config) ->
            try {
                when (config) {
                    is StdioServerConfig -> {
                        createStdioClient(request, server, config)
                    }

                    is SseServerConfig ->
                        createSseClient(request, server, config)

                    else -> {}
                }
            } catch (e: Throwable) {
                logger.error(e) { "Failed to start server: $server, config: $config" }
            }
        }

        createInternalClient(
            request,
            INTERNAL_MCP_SERVER_NAME,
            InternalServerConfig(
                type = TransportType.INTERNAL,
                name = ZencoderWorkflowToolkitType.ZENCODER_MCP_CLIENT.value
            )
        )

        return InitializeResult(
            capabilities = ServerCapabilities(
                tools = ServerCapabilities.Tools(
                    listChanged = true
                )
            ),
            serverInfo = Implementation(name = "zencoder-server", version = "1.0.0"),
            protocolVersion = LATEST_PROTOCOL_VERSION
        )
    }

    suspend fun listTools(request: ListToolsRequest?): ListToolsResult? {
        // Extract serversList from _meta if present
        val serversList = request?._meta?.let { meta ->
            when {
                meta.containsKey("serverList") -> {
                    try {
                        val list: List<String>? = meta["serverList"]?.jsonArray?.map { it.jsonPrimitive.content }
                        logger.debug { "Filtering tools by servers: $list" }
                        list
                    } catch (e: Exception) {
                        logger.error(e) { "Failed to parse serversList from _meta" }
                        null
                    }
                }

                else -> null
            }
        }?.toSet()

        // Filter clients by serversList if present
        return configuredClients.entries.map { (server, client) ->
            val result = request?.let { client.listTools(it) } ?: client.listTools()
            val tools = result.tools.map { tool ->
                clientTools[tool.name] = client
                tool.name
            }
            serverTools[server] = tools

            when {
                server == INTERNAL_MCP_SERVER_NAME -> ListToolsResult(
                    nextCursor = result.nextCursor,
                    tools = result.tools.filter {
                        serversList?.contains(it.name) ?: true
                    }
                )

                serversList == null -> result
                serversList.contains(server) -> result
                else -> ListToolsResult(tools = emptyList(), nextCursor = null)
            }
        }.fold(ListToolsResult(tools = emptyList(), nextCursor = null)) { acc, toolsResult ->
            ListToolsResult(
                tools = (acc.tools + toolsResult.tools).associateBy { it.name }.values.toList(),
                nextCursor = null
            )
        }
    }

    suspend fun callTool(request: CallToolRequest?): CallToolResult? {
        return request?.let { clientTools[request.name]?.callTool(it) } as CallToolResult?
    }

    /**
     * Gracefully shuts down all running servers.
     * This should be called when server configurations have changed.
     */
    fun shutdownAllServers() {
        logger.info { "Shutting down all servers due to configuration change" }

        // Create a copy of the keys to avoid concurrent modification issues
        val serverIds = configuredClients.keys.toList()

        // Shutdown each server
        serverIds.forEach { serverId ->
            cs.launch {
                try {
                    logger.info { "Shutting down server: $serverId" }
                    configuredClients[serverId]?.close()
                } catch (e: Exception) {
                    logger.error(e) { "Error shutting down server: $serverId" }
                } finally {
                    configuredClients.remove(serverId)
                    // Clean up tool references
                    (serverTools[serverId] ?: listOf()).forEach(clientTools::remove)
                    serverTools.remove(serverId)
                }
            }
        }
    }

    internal suspend fun createStdioClient(
        request: InitializeRequest?,
        server: ServerId,
        config: StdioServerConfig
    ): Client? {
        // Check if the server is alive
        if (checkServerAlive(server)) {
            return configuredClients[server]
        }
        val client = Client(
            clientInfo = Implementation(
                name = "zencoder-client",
                version = "1.0.0"
            ),
            options = ClientOptions(
                capabilities = request?.capabilities ?: ClientCapabilities()
            )
        )
        val clientProcess = StdioClient(
            workDirectory = when (config.cwd) {
                is String -> File(config.cwd.toString())
                else -> File(project.basePath.toString())
            },
            command = (listOf(config.command) + config.args),
            environment = config.env ?: emptyMap()
        )
        val (input, output) = clientProcess.connect()
        val clientTransport = StdioClientTransport(input, output, cs)
        return withTimeoutOrNull(WAIT_TIMEOUT) {
            client.connect(clientTransport)
            configuredClients[server] = client
            Disposer.register(project.service<ZencoderPluginProjectDisposable>()) {
                clientProcess.disconnect()
            }
            client
        }
    }

    internal suspend fun createSseClient(
        request: InitializeRequest?,
        server: ServerId,
        config: SseServerConfig
    ): Client? {
        // Check if the server is alive
        if (checkServerAlive(server)) {
            return configuredClients[server]
        }
        val client = Client(
            clientInfo = Implementation(
                name = "zencoder-client",
                version = "1.0.0"
            ),
            options = ClientOptions(
                capabilities = request?.capabilities ?: ClientCapabilities()
            )
        )
        val clientTransport = HttpClient(CIO) { install(SSE) }.mcpSseTransport(config.url, WAIT_TIMEOUT)
        return withTimeoutOrNull(WAIT_TIMEOUT) {
            client.connect(clientTransport)
            configuredClients[server] = client
            client
        }
    }

    internal suspend fun createInternalClient(
        request: InitializeRequest?,
        server: ServerId,
        config: ServerConfig
    ): Client? {
        // Check if the server is alive
        if (checkServerAlive(server)) {
            return configuredClients[server]
        }
        return try {
            // Create and connect the client
            val client = project.mcpMessageBusClient(cs, project.service<ZencoderPluginProjectDisposable>())
            logger.info { "MessageBus client initialized successfully" }
            configuredClients[server] = client
            client
        } catch (e: Throwable) {
            logger.warn(e) { "Failed to initialize MessageBus client" }
            return null
        }
    }

    private suspend fun checkServerAlive(server: ServerId): Boolean = configuredClients[server].let { client ->
        try {
            client?.ping(RequestOptions(timeout = 500.milliseconds)) ?: return false
            return true
        } catch (_: Throwable) {
            try {
                configuredClients[server]?.close()
            } catch (_: Throwable) {
            } finally {
                configuredClients.remove(server)
                (serverTools[server] ?: listOf()).forEach { clientTools.remove(it) }
                serverTools.remove(server)
            }
            return false
        }
    }
}
