package ai.zencoder.plugin.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.thisLogger

@Service(Service.Level.PROJECT)
class ChatCodeChangesTracker {

    private val changesByChat = mutableMapOf<String, CodeChangesInChat>()

    fun forChat(chatId: String): ForChat = ForChatImpl(chatId)

    interface ForChat {

        fun isFileAlreadyTracked(path: String): Boolean

        fun trackInitialFileContent(
            path: String,
            initialFileContent: String?,
            isNew: Boolean
        )

        fun trackLatestFileContent(path: String, latestContent: String)

        fun getChangesAndCleanup(): List<FileChangeSummary>
    }

    inner class ForChatImpl(val chatId: String) : ForChat {

        override fun isFileAlreadyTracked(path: String): Boolean = changesByChat[chatId]?.isInitialStateTracked(path) ?: false

        override fun trackInitialFileContent(
            path: String,
            initialFileContent: String?,
            isNew: Boolean
        ) {
            changesByChat.putIfAbsent(chatId, CodeChangesInChat())
            changesByChat[chatId]?.trackInitialFileContent(
                path = path,
                initialContent = initialFileContent,
                isNew = isNew
            )
        }

        override fun trackLatestFileContent(path: String, latestContent: String) {
            val codeChangesInChat = changesByChat[chatId]
            codeChangesInChat?.trackLatestFileContent(path = path, latestFileContent = latestContent)
        }

        override fun getChangesAndCleanup(): List<FileChangeSummary> {
            val trackedChanges = changesByChat[chatId]
            changesByChat.remove(chatId)
            return trackedChanges?.allChanges() ?: emptyList()
        }
    }
}

data class FileChangeSummary(
    val path: String,
    val before: String?,
    val after: String,
    val isNew: Boolean
)

class FileChange(
    var initialContent: String? = null,
    var latestFileContent: String? = null,
    var isNew: Boolean? = null
)

class CodeChangesInChat {

    private val fileChangesByPath = mutableMapOf<String, FileChange>()

    fun allChanges(): List<FileChangeSummary> {
        return fileChangesByPath.entries
            .filter { it.value.latestFileContent != null }
            .filter { it.value.isNew != null }
            .map { (path, change) ->
                FileChangeSummary(
                    path = path,
                    before = change.initialContent,
                    after = change.latestFileContent!!,
                    isNew = change.isNew!!
                )
            }.toList()
    }

    fun isEmpty() = fileChangesByPath.isEmpty()

    fun isInitialStateTracked(path: String): Boolean = fileChangesByPath.containsKey(path)

    fun trackInitialFileContent(
        path: String,
        initialContent: String?,
        isNew: Boolean
    ) {
        fileChangesByPath.putIfAbsent(
            path,
            FileChange(
                initialContent = initialContent,
                isNew = isNew
            )
        )
    }

    fun trackLatestFileContent(path: String, latestFileContent: String) {
        val trackedChanges = fileChangesByPath[path]
        if (trackedChanges == null) {
            thisLogger().warn("No initial file content tracked for $path")
            return
        }
        trackedChanges.latestFileContent = latestFileContent
    }
}
