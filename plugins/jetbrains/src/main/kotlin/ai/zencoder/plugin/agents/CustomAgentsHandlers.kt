package ai.zencoder.plugin.agents

import ai.zencoder.plugin.services.TerminalService
import ai.zencoder.plugin.webview.chat.handlers.ChatViewBaseHandler
import ai.zencoder.plugin.webview.model.CallbackMessage
import ai.zencoder.plugin.webview.model.CallbackMessage.*
import ai.zencoder.plugin.webview.model.PostedMessage.SetCustomAgents
import ai.zencoder.plugin.webview.model.toRequestResponse
import com.intellij.openapi.components.service
import kotlinx.coroutines.launch

abstract class CustomAgentsHandlers<T : CallbackMessage> : ChatViewBaseHandler<T>() {
    protected val agentsManager get() = service<CustomAgentsManager>()

    protected fun sendAgents() {
        val agents = agentsManager.getAll()
        sendToWebview(SetCustomAgents(agents))
    }
}

class GetCustomAgentsHandler : CustomAgentsHandlers<GetCustomAgents>() {
    override fun handle(msg: GetCustomAgents) {
        sendAgents()
    }
}

class UpdateCustomAgentHandler : CustomAgentsHandlers<UpdateCustomAgent>() {
    override fun handle(msg: UpdateCustomAgent) {
        agentsManager.save(msg.agent)
        sendAgents()
    }
}

class DeleteCustomAgentHandler : CustomAgentsHandlers<DeleteCustomAgent>() {
    override fun handle(msg: DeleteCustomAgent) {
        agentsManager.remove(msg.id)
        sendAgents()
    }
}

class SaveCustomAgentHandler : CustomAgentsHandlers<SaveCustomAgent>() {
    override fun handle(msg: SaveCustomAgent) {
        val result = agentsManager.save(msg.agent)

        val requestKey = msg.requestKey
        if (requestKey == null) {
            sendAgents()
            return
        }

        sendToWebview(result.toRequestResponse(requestKey))
    }
}

class ShowTerminalHandler : CustomAgentsHandlers<ShowTerminal>() {
    override fun handle(msg: ShowTerminal) {
        val terminalService = project.service<TerminalService>()
        cs.launch {
            terminalService.showTerminal(msg.terminalName)
        }
    }
}
