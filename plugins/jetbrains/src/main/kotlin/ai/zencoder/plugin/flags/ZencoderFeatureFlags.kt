package ai.zencoder.plugin.flags

import com.intellij.openapi.components.service

val zencoderFeatureFlags
    get() = ZencoderFeatureFlags(service<FeatureFlagsService>().rawFlags)

data class ZencoderFeatureFlags(
    val jiraIntegration: <PERSON>olean,
    val diagnosticsTool: <PERSON><PERSON>an,
    val enableCoffeeMode: <PERSON>olean,
    val enableRequirementsTool: <PERSON>olean,
    val enableCodeReferenceTool: Boolean,
    val enableNewChatManager: Boolean,
    val enableNextEditPredictions: Boolean,
    val enableContextExternalReferenceGathering: Boolean
) {
    constructor(map: FeatureFlagsMap) : this(
        jiraIntegration = map["jiraIntegration"]?.asBoolean() ?: true,
        diagnosticsTool = map["diagnosticsTool"]?.asBoolean() ?: true,
        enableCoffeeMode = map["enableCoffeeMode"]?.asBoolean() ?: true,
        enableRequirementsTool = map["enable-requirements-tool"]?.asBoolean() ?: true,
        enableCodeReferenceTool = map["enable-code-reference-tool"]?.asBoolean() ?: false,
        enableNewChatManager = map["enable-new-chat-manager"]?.asBoolean() ?: false,
        enableNextEditPredictions = map["enable-nep"]?.asBoolean() ?: false,
        enableContextExternalReferenceGathering = map["enable-context-external-ref-gathering"]?.asBoolean() ?: false
    )
}
