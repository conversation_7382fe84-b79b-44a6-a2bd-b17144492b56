package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBScrollPane
import java.awt.BorderLayout
import javax.swing.BoxLayout
import javax.swing.JComponent
import javax.swing.JPanel

/**
 * Component for displaying a tab panel with scrollable content.
 * Follows JetBrains UI guidelines for tab panels.
 */
class TabPanelComponent : UpdatableComponent<List<JComponent>> {

    private val panel = JPanel(BorderLayout())
    private val contentPanel = JPanel()
    private val scrollPane: JBScrollPane

    init {
        setupPanel()

        // Create a scroll pane with JetBrains UI styling
        scrollPane = JBScrollPane(contentPanel).apply {
            // Remove border for cleaner appearance
            border = UIStyleUtils.Borders.emptyBorder()
            // Set standard scroll increment for smooth scrolling
            verticalScrollBar.unitIncrement = UIStyleUtils.Spacing.LARGE

            // Set the background color to match the panel for theme compatibility
            viewport.background = JBColor.background()

            // Apply JetBrains UI scrollbar styling
            verticalScrollBar.apply {
                putClientProperty("JScrollBar.showButtons", false)
                putClientProperty("JScrollBar.thumbArc", UIStyleUtils.Spacing.SMALL)
                putClientProperty("JScrollBar.thumbInsets", UIStyleUtils.Spacing.TINY / 2)
                putClientProperty("JScrollBar.width", UIStyleUtils.Spacing.SMALL + 2)
            }

            // Improve horizontal scrollbar appearance if needed
            horizontalScrollBar.apply {
                putClientProperty("JScrollBar.showButtons", false)
                putClientProperty("JScrollBar.thumbArc", UIStyleUtils.Spacing.SMALL)
                putClientProperty("JScrollBar.thumbInsets", UIStyleUtils.Spacing.TINY / 2)
            }
        }

        panel.add(scrollPane, BorderLayout.CENTER)
    }

    private fun setupPanel() {
        // Create the main panel with proper spacing
        panel.apply {
            border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.MEDIUM)
            background = JBColor.background()
        }

        // Create a new panel for this category with proper layout
        contentPanel.apply {
            layout = BoxLayout(this, BoxLayout.Y_AXIS)
            border = UIStyleUtils.Borders.emptyBorder(0, 0, UIStyleUtils.Spacing.SMALL, 0)
            background = JBColor.background()
        }
    }

    override fun createComponent(): JComponent = panel

    override fun updateWithData(data: List<JComponent>) {
        contentPanel.removeAll()

        if (data.isNotEmpty()) {
            // Add a top padding
            contentPanel.add(javax.swing.Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))

            // Add each component with spacing
            for (component in data) {
                contentPanel.add(component)
                contentPanel.add(javax.swing.Box.createVerticalStrut(UIStyleUtils.Spacing.SMALL))
            }

            // Add a filler panel at the bottom to push everything up
            contentPanel.add(javax.swing.Box.createVerticalGlue())
        }

        contentPanel.revalidate()
        contentPanel.repaint()
    }

    /**
     * Clears all components from the panel.
     */
    fun clear() {
        contentPanel.removeAll()
        contentPanel.revalidate()
        contentPanel.repaint()
    }
}
