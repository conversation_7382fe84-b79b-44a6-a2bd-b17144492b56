package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.icons.AllIcons
import com.intellij.ui.components.JBLabel
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.FlowLayout
import java.awt.event.ActionListener
import javax.swing.*

/**
 * Component for displaying notifications and error messages.
 * Follows JetBrains UI guidelines for notifications.
 */
class NotificationComponent :
    UpdatableComponent<NotificationComponent.NotificationData>,
    InteractiveComponent<ActionListener> {

    data class NotificationData(
        val message: String,
        val severity: UIStyleUtils.NotificationSeverity = UIStyleUtils.NotificationSeverity.ERROR
    )

    private val panel = JPanel(BorderLayout())
    private var dismissButton: JButton? = null
    private var dismissHandler: ActionListener? = null

    init {
        panel.isOpaque = false
    }

    override fun createComponent(): JComponent = panel

    override fun updateWithData(data: NotificationData) {
        panel.removeAll()

        // Get the appropriate icon based on severity
        val icon = when (data.severity) {
            UIStyleUtils.NotificationSeverity.ERROR -> AllIcons.General.Error
            UIStyleUtils.NotificationSeverity.WARNING -> AllIcons.General.Warning
            UIStyleUtils.NotificationSeverity.INFO -> AllIcons.General.Information
        }

        // Create the notification panel with standard styling
        val notificationPanel = JPanel(BorderLayout(UIStyleUtils.Spacing.SMALL, 0)).apply {
            border = UIStyleUtils.Borders.notificationBorder(data.severity)
            background = UIStyleUtils.getNotificationBackgroundColor(data.severity)
        }

        // Create the message with icon using standard spacing
        val messagePanel = JPanel(BorderLayout(UIStyleUtils.Spacing.SMALL, 0)).apply {
            isOpaque = false
        }

        // Use JBLabel for icon to ensure proper scaling
        val iconLabel = JBLabel(icon)
        messagePanel.add(iconLabel, BorderLayout.WEST)

        // Use JBLabel with HTML for proper word wrapping
        val messageLabel = JBLabel("<html><div width='400'>${data.message}</div></html>").apply {
            foreground = UIStyleUtils.getNotificationForegroundColor(data.severity)
            font = UIStyleUtils.Typography.REGULAR_FONT
        }
        messagePanel.add(messageLabel, BorderLayout.CENTER)

        // Create a dismiss button styled as a link
        dismissButton = JButton("Dismiss").apply {
            isBorderPainted = false
            isContentAreaFilled = false
            isFocusPainted = false
            foreground = UIStyleUtils.Colors.LINK_COLOR
            cursor = java.awt.Cursor.getPredefinedCursor(java.awt.Cursor.HAND_CURSOR)
            margin = JBUI.emptyInsets()
            font = UIStyleUtils.Typography.REGULAR_FONT

            // Add the dismiss handler if set
            dismissHandler?.let { addActionListener(it) }
        }

        // Button panel with proper alignment and spacing
        val buttonPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 0, 0)).apply {
            isOpaque = false
            border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.SMALL, 0, 0, 0)
            add(dismissButton)
        }

        notificationPanel.add(messagePanel, BorderLayout.CENTER)
        notificationPanel.add(buttonPanel, BorderLayout.SOUTH)

        // Add the panel with standard margin
        val wrapperPanel = JPanel(BorderLayout()).apply {
            isOpaque = false
            border = UIStyleUtils.Borders.emptyBorder(
                UIStyleUtils.Spacing.SMALL,
                UIStyleUtils.Spacing.LARGE,
                0,
                UIStyleUtils.Spacing.LARGE
            )
            add(notificationPanel, BorderLayout.CENTER)
        }

        panel.add(wrapperPanel, BorderLayout.CENTER)
        panel.revalidate()
        panel.repaint()
    }

    override fun setEventHandler(handler: ActionListener) {
        dismissHandler = handler
        dismissButton?.addActionListener(handler)
    }

    fun clear() {
        panel.removeAll()
        panel.revalidate()
        panel.repaint()
    }
}
