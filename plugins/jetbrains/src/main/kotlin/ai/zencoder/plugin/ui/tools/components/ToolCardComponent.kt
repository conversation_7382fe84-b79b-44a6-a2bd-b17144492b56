package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.openapi.util.IconLoader
import com.intellij.ui.JBColor
import com.intellij.ui.components.JBLabel
import java.awt.*
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.awt.event.MouseListener
import javax.swing.*

/**
 * Component for displaying a tool card with icon, name, author, and description.
 * Follows JetBrains UI guidelines for cards.
 */
class ToolCardComponent :
    UpdatableComponent<ToolCardComponent.ToolCardData>,
    InteractiveComponent<ToolCardComponent.ToolCardEventHandler> {

    data class ToolCardData(
        val toolId: String,
        val tool: McpServerInfo,
        val showStatus: Boolean = false
    )

    interface ToolCardEventHandler {
        fun onToolCardClicked(toolId: String, tool: McpServerInfo)
    }

    private val wrapper = JPanel(BorderLayout())
    private val card = JPanel(BorderLayout())
    private var currentData: ToolCardData? = null
    private var eventHandler: ToolCardEventHandler? = null

    companion object {
        private val MCP_ICON = IconLoader.getIcon("/icons/mcp.svg", ToolCardComponent::class.java)

        /**
         * Gets the icon for a tool.
         * @param tool The tool to get the icon for
         * @return The icon for the tool
         */
        fun getToolIcon(tool: McpServerInfo): Icon {
            // Try to load the icon
            if (!tool.icon.isNullOrBlank()) {
                try {
                    // Check if it's a Base64 encoded image with data URI prefix
                    if (tool.icon.startsWith("data:image")) {
                        val base64Data = tool.icon.substringAfter("base64,")
                        val imageData = java.util.Base64.getDecoder().decode(base64Data)
                        val image = ImageIcon(imageData)

                        // Resize the image to fit the icon panel
                        val scaledImage = image.image.getScaledInstance(32, 32, Image.SCALE_SMOOTH)
                        return ImageIcon(scaledImage)
                    }
                    // Check if it's a raw Base64 string (without data URI prefix)
                    else if (isBase64(tool.icon)) {
                        try {
                            val imageData = java.util.Base64.getDecoder().decode(tool.icon)
                            val image = ImageIcon(imageData)

                            // Resize the image to fit the icon panel
                            val scaledImage = image.image.getScaledInstance(32, 32, Image.SCALE_SMOOTH)
                            return ImageIcon(scaledImage)
                        } catch (e: Exception) {
                            return MCP_ICON
                        }
                    }
                } catch (e: Exception) {
                    return MCP_ICON
                }
            }

            return MCP_ICON
        }

        /**
         * Checks if a string is likely a Base64 encoded image.
         */
        private fun isBase64(str: String): Boolean {
            // Base64 strings typically have a length that's a multiple of 4
            if (str.length % 4 != 0) return false

            // Check if the string contains only valid Base64 characters
            val base64Pattern = Regex("^[A-Za-z0-9+/=]+$")
            if (!base64Pattern.matches(str)) return false

            // Try to decode a small portion to see if it's valid Base64
            try {
                val decoder = java.util.Base64.getDecoder()
                // Just decode the first few bytes to check validity
                decoder.decode(str.substring(0, Math.min(str.length, 100)))
                return true
            } catch (e: Exception) {
                return false
            }
        }
    }

    init {
        setupPanel()
    }

    private fun setupPanel() {
        // Create a wrapper panel with consistent spacing
        wrapper.apply {
            isOpaque = false
            alignmentX = Component.LEFT_ALIGNMENT
            maximumSize = Dimension(Integer.MAX_VALUE, UIStyleUtils.Spacing.LARGE * 10)
            border = UIStyleUtils.Borders.emptyBorder(
                UIStyleUtils.Spacing.SMALL,
                UIStyleUtils.Spacing.LARGE
            )
        }

        // Create the card with proper styling and focus states
        card.apply {
            border = UIStyleUtils.Borders.standardCardBorder()
            background = JBColor.background()
            cursor = Cursor.getPredefinedCursor(Cursor.HAND_CURSOR)
        }

        wrapper.add(card, BorderLayout.CENTER)
    }

    override fun createComponent(): JComponent = wrapper

    override fun updateWithData(data: ToolCardData) {
        currentData = data
        card.removeAll()

        // Add hover and focus effects
        val mouseListener = createMouseListener()
        card.mouseListeners.forEach { card.removeMouseListener(it) }
        card.addMouseListener(mouseListener)

        // Top panel with proper spacing between icon and text
        val topPanel = JPanel(BorderLayout(UIStyleUtils.Spacing.MEDIUM, 0)).apply {
            isOpaque = false
        }

        // Create icon panel with proper sizing
        val iconPanel = createToolIconPanel(data.tool).apply {
            preferredSize = UIStyleUtils.Sizes.ICON_LARGE
            minimumSize = UIStyleUtils.Sizes.ICON_LARGE
            maximumSize = UIStyleUtils.Sizes.ICON_LARGE
        }

        // Panel for name and author with proper alignment
        val nameAuthorPanel = JPanel().apply {
            layout = BoxLayout(this, BoxLayout.Y_AXIS)
            isOpaque = false
            alignmentX = Component.LEFT_ALIGNMENT
        }

        // Tool name with proper typography
        val nameLabel = JBLabel(data.tool.name.toString()).apply {
            font = UIStyleUtils.Typography.MEDIUM_FONT.asBold()
            foreground = JBColor.foreground()
            alignmentX = Component.LEFT_ALIGNMENT
        }
        nameAuthorPanel.add(nameLabel)

        // Author info with @ prefix
        val authorText = if (data.tool.author != null && data.tool.author.isNotEmpty()) {
            "@${data.tool.author}"
        } else {
            ""
        }

        if (authorText.isNotEmpty()) {
            val authorLabel = JBLabel(authorText).apply {
                foreground = UIStyleUtils.Colors.SECONDARY_TEXT_COLOR
                font = UIStyleUtils.Typography.SMALL_FONT
                alignmentX = Component.LEFT_ALIGNMENT
            }
            // Standard spacing between related elements
            nameAuthorPanel.add(Box.createVerticalStrut(UIStyleUtils.Spacing.TINY))
            nameAuthorPanel.add(authorLabel)
        }

        // Add status label if needed
        if (data.showStatus && data.tool.status.status == ToolStatus.INSTALLED) {
            val statusPanel = JPanel(FlowLayout(FlowLayout.RIGHT, 0, 0)).apply {
                isOpaque = false
            }

            val statusLabel = JBLabel("Installed").apply {
                foreground = JBColor.namedColor("Plugins.tagForeground", JBColor.GREEN)
                background = JBColor.namedColor(
                    "Plugins.tagBackground",
                    JBColor(Color(232, 246, 232), Color(39, 55, 39))
                )
                border = BorderFactory.createCompoundBorder(
                    BorderFactory.createLineBorder(
                        JBColor.namedColor(
                            "Plugins.tagBorderColor",
                            JBColor(Color(220, 240, 220), Color(50, 70, 50))
                        ),
                        1
                    ),
                    UIStyleUtils.Borders.emptyBorder(2, 6)
                )
                font = UIStyleUtils.Typography.SMALL_FONT
                isOpaque = true
            }
            statusPanel.add(statusLabel)

            topPanel.add(iconPanel, BorderLayout.WEST)
            topPanel.add(nameAuthorPanel, BorderLayout.CENTER)
            topPanel.add(statusPanel, BorderLayout.EAST)
        } else {
            topPanel.add(iconPanel, BorderLayout.WEST)
            topPanel.add(nameAuthorPanel, BorderLayout.CENTER)
        }

        // Description panel with standard spacing
        val descriptionPanel = JPanel(BorderLayout()).apply {
            isOpaque = false
            border = UIStyleUtils.Borders.emptyBorder(UIStyleUtils.Spacing.SMALL, 0, 0, 0)
        }

        // Description with HTML for word wrapping
        val description = data.tool.description ?: ""
        if (description.isNotEmpty()) {
            val descriptionLabel = JBLabel("<html><div width='400'>$description</div></html>").apply {
                foreground = JBColor.foreground()
                font = UIStyleUtils.Typography.REGULAR_FONT
            }
            descriptionPanel.add(descriptionLabel, BorderLayout.CENTER)
        }

        // Main content panel with proper spacing
        val contentPanel = JPanel(BorderLayout(0, 0)).apply {
            isOpaque = false
        }
        contentPanel.add(topPanel, BorderLayout.NORTH)
        contentPanel.add(descriptionPanel, BorderLayout.CENTER)

        card.add(contentPanel, BorderLayout.CENTER)
        card.revalidate()
        card.repaint()
    }

    override fun setEventHandler(handler: ToolCardEventHandler) {
        eventHandler = handler
    }

    private fun createMouseListener(): MouseListener {
        return object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                card.background = UIStyleUtils.Colors.HOVER_BACKGROUND
                card.repaint()
            }

            override fun mouseExited(e: MouseEvent) {
                card.background = JBColor.background()
                card.repaint()
            }

            override fun mouseClicked(e: MouseEvent) {
                currentData?.let { data ->
                    eventHandler?.onToolCardClicked(data.toolId, data.tool)
                }
            }
        }
    }

    private fun createToolIconPanel(tool: McpServerInfo): JPanel {
        // Create a panel that fills the entire left column
        val iconPanel = JPanel(GridBagLayout()).apply {
            isOpaque = false
            minimumSize = UIStyleUtils.Sizes.ICON_LARGE
            preferredSize = UIStyleUtils.Sizes.ICON_LARGE
            maximumSize = UIStyleUtils.Sizes.ICON_LARGE
        }

        // Create the icon label with center alignment
        val iconLabel = JLabel().apply {
            horizontalAlignment = SwingConstants.CENTER
            verticalAlignment = SwingConstants.CENTER
            horizontalTextPosition = SwingConstants.CENTER
            verticalTextPosition = SwingConstants.BOTTOM
            icon = getToolIcon(tool)
        }

        // Add the icon label to the panel with GridBagConstraints to center it
        val gbc = GridBagConstraints().apply {
            gridx = 0
            gridy = 0
            fill = GridBagConstraints.BOTH
            weightx = 1.0
            weighty = 1.0
        }
        iconPanel.add(iconLabel, gbc)

        return iconPanel
    }
}
