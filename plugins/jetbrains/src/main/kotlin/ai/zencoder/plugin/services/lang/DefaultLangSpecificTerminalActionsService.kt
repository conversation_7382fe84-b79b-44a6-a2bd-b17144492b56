package ai.zencoder.plugin.services.lang

import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.sh.run.ShRunConfiguration

@Service(Service.Level.PROJECT)
class DefaultLangSpecificTerminalActionsService : LangSpecificTerminalActionsService {
    override fun patchShConfiguration(
        project: Project,
        terminalName: String,
        command: String,
        configuration: ShRunConfiguration
    ) {
        configuration.scriptText = command
        configuration.isExecuteInTerminal = false
        configuration.isExecuteScriptFile = false
    }
}
