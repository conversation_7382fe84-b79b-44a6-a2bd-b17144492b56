package ai.zencoder.plugin.auth

import ai.zencoder.plugin.log.errorAndThrow
import ai.zencoder.plugin.utils.auth.ZenRefreshAuthenticator
import ai.zencoder.plugin.utils.systemSsl
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import okhttp3.OkHttpClient
import okhttp3.RequestBody.Companion.toRequestBody
import org.eclipse.jetty.http.HttpHeader
import org.eclipse.jetty.http.pathmap.PathSpec
import org.eclipse.jetty.io.Content
import org.eclipse.jetty.server.*
import org.eclipse.jetty.server.handler.DefaultHandler
import org.eclipse.jetty.server.handler.PathMappingsHandler
import org.eclipse.jetty.util.Callback
import org.eclipse.jetty.util.Fields
import java.nio.charset.StandardCharsets
import java.time.Duration
import javax.servlet.http.HttpServletResponse

class AuthorizationCodeFlowListener(
    private val tokenExchangeUrl: String,
    private val additionalParams: Map<String, String> = emptyMap(),
    private val urlPath: String,
    private val codeVerifier: String? = null,
    private val provider: String? = null,
//    TODO remove - temporary hack on SPA
    private val clientRedirectUrl: String? = null,
    private val tokenExchangeResponseParser: (response: String) -> OAuthTokenExchangeResponse
) : AuthInfoReceiver {
    private val server: Server = Server(0)

    private var authInfo: AuthInfo? = null
    private val objectMapper = ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .registerModule(
            KotlinModule.Builder()
                .build()
        )

    init {
        val pathMappingHandler = PathMappingsHandler()
        pathMappingHandler.addMapping(PathSpec.from(urlPath), RequestHandler(this))
        val handlers: Handler.Sequence = Handler.Sequence()
        handlers.addHandler(pathMappingHandler)
        handlers.addHandler(DefaultHandler())
        server.handler = handlers
        server.start()
    }

    fun provideAuthenticationCallbackUrl(): String = "http://127.0.0.1:${server.getAllocatedPort()}$urlPath"

    fun stop() {
        if (server.isRunning) {
            server.stop()
        }
    }

    inner class RequestHandler(private val receiver: AuthInfoReceiver) : Handler.Abstract() {
        override fun handle(
            request: Request,
            response: Response,
            callback: Callback
        ): Boolean {
            val fields: Fields = Request.extractQueryParameters(request, StandardCharsets.UTF_8)
            val authCode = fields.get("code").value

            // Exchange auth code for tokens
            val tokens = exchangeCodeForTokens(authCode)

            response.status = HttpServletResponse.SC_OK
            response.headers.put(HttpHeader.CONTENT_TYPE, "text/html; charset=utf-8")
            response.headers.put(HttpHeader.ACCESS_CONTROL_ALLOW_HEADERS, HttpHeader.CONTENT_TYPE.asString())
            Content.Sink.write(
                response,
                true,
                """
               <!DOCTYPE html>
                <html>
                <head>
                    <title>Authentication Successful</title>
                    <script>
                        setTimeout(function() {
                            window.close();
                            // Fallback for browsers that block window.close()
                            document.getElementById('message').innerHTML = 'You can now close this window.';
                        }, 1000);
                    </script>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            margin: 0;
                            background-color: #f5f5f5;
                        }
                        .message {
                            text-align: center;
                            padding: 20px;
                            background-color: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        }
                    </style>
                </head>
                <body>
                    <div class="message">
                        <h2>Authentication Successful</h2>
                        <p id="message">This window will close automatically...</p>
                    </div>
                </body>
                </html>
                """.trimIndent(),
                callback
            )

            receiver.receive(tokens)
            return true
        }

        private fun exchangeCodeForTokens(authCode: String): AuthInfo {
            val client = OkHttpClient()
                .newBuilder()
                .systemSsl()
                .authenticator(ZenRefreshAuthenticator)
                .readTimeout(Duration.ofSeconds(10))
                .connectTimeout(Duration.ofSeconds(10))
                .build()

            val params = mutableMapOf(
                "code" to authCode,
                "redirectUrl" to clientRedirectUrl,
                "providerType" to provider
            )

            codeVerifier?.let {
                // hacky solution to support both versions of the SDK
                params["codeVerifier"] = it
                params["code_verifier"] = it
            }

            // Add any additional parameters
            params.putAll(additionalParams)

            val requestBuilder = okhttp3.Request.Builder().url(tokenExchangeUrl)
            requestBuilder.header("Content-Type", "application/json")
            if (service<AuthService>().authInfoOrNull() != null) {
                requestBuilder.header("Authorization", "Bearer ${service<AuthService>().accessToken}")
            }
            requestBuilder.post(objectMapper.writeValueAsString(params).toRequestBody())
            val request = requestBuilder.build()

            val response = client.newCall(request).execute()
            if (response.code == 200) {
                val responseBody = response.body?.string()
                if (responseBody == null) {
                    throw IllegalStateException("Empty response body from token exchange endpoint")
                }
                val parsedResponse = tokenExchangeResponseParser.invoke(responseBody)
                return AuthInfo(
                    parsedResponse.accessToken(),
                    parsedResponse.refreshToken()
                )
            } else {
                thisLogger().warn(
                    "Failed to exchange auth code for tokens. Status: ${response.code}, Body: ${response.body?.string()}"
                )
                throw RuntimeException("Failed to exchange authorization code for tokens")
            }
        }
    }

    fun getAuthInfo(): AuthInfo? = authInfo
    fun checkIfAuthDataArrived(): Boolean = authInfo != null

    private fun Server.getAllocatedPort(): Int {
        for (connector in this.connectors) {
            if (connector is ServerConnector) {
                return connector.localPort
            }
        }
        thisLogger().errorAndThrow("Could not find allocated port for server")
    }

    override fun receive(authInfo: AuthInfo) {
        stop()
        this.authInfo = authInfo
    }
}
