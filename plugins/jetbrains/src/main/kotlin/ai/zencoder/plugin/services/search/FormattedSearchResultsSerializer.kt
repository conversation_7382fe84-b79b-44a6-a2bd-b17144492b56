package ai.zencoder.plugin.services.search

import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * Custom serializer for FormattedSearchResults that generates a formatted text representation.
 * This serializer directly serializes the class as a string, without any object wrapper.
 */
object FormattedSearchResultsSerializer : KSerializer<FormattedSearchResults> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("FormattedSearchResults", PrimitiveKind.STRING)

    // Constants for indentation management
    const val INDENT_SIZE = 2
    const val INDENT_CHAR = ' '

    override fun serialize(encoder: Encoder, value: FormattedSearchResults) {
        // Generate the formatted text representation
        val formattedText = buildFormattedText(
            value.keyword,
            value.declarations,
            value.directUsages,
            value.dynamicUsages
        )

        // Encode directly as a string
        encoder.encodeString(formattedText)
    }

    override fun deserialize(decoder: Decoder): FormattedSearchResults {
        // Get the string representation
        val formattedText = decoder.decodeString()

        // Parse the formatted text to extract the keyword
        val lines = formattedText.lines()
        val keyword = if (lines.size >= 2 && lines[0] == "Definition") {
            // Extract keyword from the second line (removing leading spaces)
            lines[1].trim()
        } else {
            // If we can't find a keyword, use the first line or an empty string
            lines.firstOrNull()?.trim() ?: ""
        }

        // Return a FormattedSearchResults with the extracted keyword
        // This is a minimal implementation since we're only using FormattedSearchResults as a return type
        return FormattedSearchResults(
            keyword = keyword,
            declarations = emptyList(),
            directUsages = null,
            dynamicUsages = null
        )
    }

    /**
     * Creates an indentation string of specified level.
     *
     * @param level The indentation level
     * @return A string with the appropriate number of spaces for the given level
     */
    private fun indent(level: Int): String {
        return INDENT_CHAR.toString().repeat(level * INDENT_SIZE)
    }

    /**
     * Appends a line with the specified indentation level.
     *
     * @param sb The StringBuilder to append to
     * @param level The indentation level
     * @param text The text to append after indentation
     */
    private fun appendIndentedLine(
        sb: StringBuilder,
        level: Int,
        text: String
    ) {
        sb.appendLine("${indent(level)}$text")
    }

    /**
     * Builds a formatted text representation of the search results.
     */
    private fun buildFormattedText(
        keyword: String,
        declarations: List<BaseSearchResult>,
        directUsagesGroup: UsageTypeGroup?,
        dynamicUsagesGroup: DynamicUsagesGroup?
    ): String {
        val sb = StringBuilder()

        // Add declarations section
        if (declarations.isNotEmpty()) {
            sb.appendLine("Definition")
            appendIndentedLine(sb, 1, keyword)

            // Group declarations by directory and file for better organization
            val declarationsByDirectory = declarations.groupBy { SearchFormattingUtils.getDirectory(it.relativePath) }

            declarationsByDirectory.forEach { (directory, dirDeclarations) ->
                appendIndentedLine(sb, 2, "$directory (${dirDeclarations.size} ${pluralize("declaration", dirDeclarations.size)} found)")

                // Group by file
                val declarationsByFile = dirDeclarations.groupBy { SearchFormattingUtils.getFileName(it.relativePath) }

                declarationsByFile.forEach { (fileName, fileDeclarations) ->
                    appendIndentedLine(
                        sb,
                        3,
                        "$fileName (${fileDeclarations.size} ${pluralize("declaration", fileDeclarations.size)} found)"
                    )

                    // Show each declaration
                    fileDeclarations.forEach { declaration ->
                        appendIndentedLine(sb, 4, "${declaration.lineNumber} ${declaration.lineText.trim()}")
                    }
                }
            }

            sb.appendLine()
        }

        // Add direct usages section
        if (directUsagesGroup != null) {
            sb.appendLine(
                "Usages in Project Files  (${directUsagesGroup.totalCount} ${pluralize("usage", directUsagesGroup.totalCount)} found)"
            )
            formatUsageTypes(sb, directUsagesGroup.usagesByType)
            sb.appendLine()
        }

        // Add dynamic usages section
        if (dynamicUsagesGroup != null) {
            sb.appendLine("Dynamic usages  (${dynamicUsagesGroup.totalCount} ${pluralize("usage", dynamicUsagesGroup.totalCount)} found)")
            formatUsageTypes(sb, dynamicUsagesGroup.usagesByType)
        }

        return sb.toString()
    }

    /**
     * Formats usage types and their hierarchical structure.
     */
    private fun formatUsageTypes(sb: StringBuilder, usagesByType: Map<String, DirectoryGroup>) {
        // Add usage types
        usagesByType.forEach { (usageType, directoryGroup) ->
            appendIndentedLine(
                sb,
                1,
                "Usage in $usageType  (${directoryGroup.totalCount} ${pluralize("usage", directoryGroup.totalCount)} found)"
            )

            // Add directories
            directoryGroup.usagesByDirectory.forEach { (directory, fileGroup) ->
                appendIndentedLine(sb, 2, "$directory  (${fileGroup.totalCount} ${pluralize("usage", fileGroup.totalCount)} found)")

                // Add files
                fileGroup.usagesByFile.forEach { (fileName, elementGroup) ->
                    appendIndentedLine(
                        sb,
                        3,
                        "$fileName  (${elementGroup.totalCount} ${pluralize("usage", elementGroup.totalCount)} found)"
                    )

                    // Add elements
                    elementGroup.usagesByElement.forEach { (elementName, results) ->
                        appendIndentedLine(sb, 4, "$elementName  (${results.size} ${pluralize("usage", results.size)} found)")

                        // Add individual usages
                        results.forEach { result ->
                            appendIndentedLine(sb, 5, "${result.lineNumber} ${result.lineText.trim()}")
                        }
                    }
                }
            }
        }
    }

    /**
     * Returns a pluralized form of the word based on the count.
     */
    private fun pluralize(word: String, count: Int): String {
        return if (count == 1) word else "${word}s"
    }
}
