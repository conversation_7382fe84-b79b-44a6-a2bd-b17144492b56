package ai.zencoder.plugin.config

import ai.zencoder.plugin.auth.AuthInfo
import ai.zencoder.plugin.auth.AuthOperationsTrackerService
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.auth.askAuthInWebview
import ai.zencoder.plugin.observers.auth.AuthObserver
import ai.zencoder.plugin.services.ZencoderPluginDisposable
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.maskToken
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.project.getOpenedProjects
import com.intellij.openapi.util.Disposer
import com.intellij.util.concurrency.AppExecutorUtil
import java.io.FileInputStream
import java.io.FileWriter
import java.nio.file.*
import java.time.LocalDate
import java.util.*
import java.util.concurrent.Future
import kotlin.sequences.forEach

interface TokenStorage {
    fun loadAuthInfo(): AuthInfo?
    fun storeAuthInfo(authInfo: AuthInfo)
    fun cleanAuthInfo()
}

class FileTokenStorage : TokenStorage {

    companion object {
        private val instance = FileTokenStorage()
        fun getInstance(): TokenStorage = instance
    }

    private val authLogger = service<AuthOperationsTrackerService>()

    private val configDirectoryPath: Path = Paths.get(System.getProperty("user.home"), ".zencoder")
    private val configPath: Path = configDirectoryPath.resolve("config")
    private val accessTokenKeyPostfix = "access.token"
    private val refreshTokenKeyPostfix = "refresh.token"

    private var properties: Properties? = null
    private var isWatcherInitialized = false
    private var watchService: WatchService? = null
    private var watcherFuture: Future<*>? = null

    @Synchronized
    override fun loadAuthInfo(): AuthInfo? {
        initializeFileWatcher()
        val authServiceKeyPrefix = keyPrefix()
        authLogger.info("Loading access token from auth storage for $authServiceKeyPrefix")
        val accessToken = get("$authServiceKeyPrefix.$accessTokenKeyPostfix")
        val refreshToken = get("$authServiceKeyPrefix.$refreshTokenKeyPostfix")
        if (!accessToken.isNullOrBlank() && !refreshToken.isNullOrBlank()) {
            return AuthInfo(
                accessToken = accessToken,
                refreshToken = refreshToken
            )
        }
        return null
    }

    @Synchronized
    override fun storeAuthInfo(authInfo: AuthInfo) {
        initializeFileWatcher()
        val authServiceKeyPrefix = keyPrefix()
        authLogger.info(
            "Storing new access token into auth storage for ${
                authServiceKeyPrefix
            }, refresh token: ${
                maskToken(authInfo.refreshToken)
            }, access token: ${
                maskToken(authInfo.accessToken)
            }"
        )
        put("$authServiceKeyPrefix.$accessTokenKeyPostfix", authInfo.accessToken)
        put("$authServiceKeyPrefix.$refreshTokenKeyPostfix", authInfo.refreshToken)
    }

    @Synchronized
    override fun cleanAuthInfo() {
        initializeFileWatcher()
        val authServiceKeyPrefix = keyPrefix()
        authLogger.info("Cleaning up token auth storage for $authServiceKeyPrefix")
        put("$authServiceKeyPrefix.$accessTokenKeyPostfix", "")
        put("$authServiceKeyPrefix.$refreshTokenKeyPostfix", "")
    }

    private fun initializeFileWatcher() {
        if (isWatcherInitialized) {
            return
        }
        isWatcherInitialized = true
        if (properties == null) {
            properties = loadProperties()
        }

        try {
            val watcher = FileSystems.getDefault().newWatchService()
            watchService = watcher
            configDirectoryPath.register(
                watcher,
                StandardWatchEventKinds.ENTRY_CREATE,
                StandardWatchEventKinds.ENTRY_MODIFY,
                StandardWatchEventKinds.ENTRY_DELETE
            )
            watcherFuture = AppExecutorUtil.getAppExecutorService().submit {
                try {
                    while (!Thread.currentThread().isInterrupted) {
                        val key = try {
                            watcher.take()
                        } catch (_: InterruptedException) {
                            Thread.currentThread().interrupt()
                            break
                        }
                        for (event in key.pollEvents()) {
                            val kind = event.kind()
                            val fileName = event.context() as? Path ?: continue
                            if (fileName.toString() == "config") {
                                when (kind) {
                                    StandardWatchEventKinds.ENTRY_CREATE,
                                    StandardWatchEventKinds.ENTRY_MODIFY -> {
                                        authLogger.info("Config file changed, reloading properties")
                                        ApplicationManager.getApplication().invokeLater {
                                            synchronized(this@FileTokenStorage) {
                                                properties = loadProperties()
                                                loadAuthInfo()?.let { service<AuthObserver>().signIn(it) }
                                            }
                                        }
                                    }
                                    StandardWatchEventKinds.ENTRY_DELETE -> {
                                        authLogger.info("Config file deleted. Reset authentication")
                                        ApplicationManager.getApplication().invokeLater {
                                            synchronized(this@FileTokenStorage) {
                                                properties = null
                                                service<AuthService>().resetAuthentication()
                                                getOpenedProjects().forEach(::askAuthInWebview)
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        if (!key.reset()) {
                            break
                        }
                    }
                } catch (e: Exception) {
                    authLogger.error("Error in file watcher", e)
                }
            }

            val disposable = service<ZencoderPluginDisposable>()
            Disposer.register(
                disposable,
                Disposable {
                    watcherFuture?.cancel(true)
                    try {
                        watchService?.close()
                    } catch (e: Exception) {
                        authLogger.warn("Error closing watch service", e)
                    } finally {
                        watchService = null
                    }
                }
            )
        } catch (e: Exception) {
            authLogger.error("Failed to initialize file watcher", e)
        }
    }

    private fun loadProperties(): Properties {
        val file = configPath.toFile()
        val properties = Properties()
        configDirectoryPath.toFile().mkdir()
        file.createNewFile()
        FileInputStream(file).use {
            properties.load(it)
        }
        return properties
    }

    private fun get(key: String): String? {
        if (properties == null) {
            properties = loadProperties()
        }
        return properties?.getProperty(key)
    }

    private fun put(key: String, value: String) {
        val file = configPath.toFile()
        if (properties == null) {
            properties = loadProperties()
        }
        FileWriter(file).use {
            properties?.put(key, value)
            properties?.store(it, "updated " + LocalDate.now())
        }
    }

    private fun keyPrefix(): String = service<ZencoderSettings>().signInUrl.substringAfter("https://")
}
