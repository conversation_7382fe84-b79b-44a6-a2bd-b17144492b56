package ai.zencoder.plugin.agents

import ai.zencoder.plugin.chat.util.commandMessageOf
import ai.zencoder.plugin.core.ZencoderAction
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.utils.editor
import ai.zencoder.plugin.utils.language
import ai.zencoder.plugin.webview.chat.ChatDecorator
import com.intellij.openapi.actionSystem.ActionUpdateThread
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiManager
import com.intellij.ui.IconManager
import com.intellij.util.application

class RunCustomAgentAction(private val agent: Agent) : ZencoderAction() {
    private val customAgentsManager get() = service<CustomAgentsManager>()

    init {
        templatePresentation.setText(actionName(), false)
        templatePresentation.icon = IconManager.getInstance().getIcon("/zencoder/progress/original.svg", javaClass.classLoader)
    }

    override fun doAction(project: Project, event: AnActionEvent) {
        val editor = event.editor ?: return
        val document = editor.document
        if (!document.isWritable) return

        track(
            "Custom agent called",
            "agent_id" to agent.id,
            "agent_use_repair" to agent.repair,
            "agent_use_rag" to agent.rag,
            "agent_instruction_length" to agent.command.length
        ) // todo agent_usage_type

        val currentVirtualFile = FileDocumentManager.getInstance().getFile(document) as VirtualFile
        val currentPsiFile = application.runReadAction<PsiFile> {
            PsiManager.getInstance(project).findFile(currentVirtualFile)
        }

        val selectedText = editor.selectionModel.selectedText
        val commandName = agent.commandName ?: error("Missing command name for custom agent: ${agent.name}")
        val commandMessage = commandMessageOf(
            editor,
            commandName,
            null,
            selectedText,
            editor.language,
            isCustomAgent = true,
            customAgent = agent
        )
        project.service<ChatDecorator>().addToNewChatAndShow(commandMessage)
    }

    override fun update(event: AnActionEvent) {
        event.presentation.isEnabled = customAgentsManager.findBy(agent.id) != null
        event.presentation.text = actionName()
    }

    private fun actionName() = "Run Custom Agent: ${agent.name}${if (agent.isSharedWithOrganization == true) " (Org)" else ""}"

    override fun getActionUpdateThread(): ActionUpdateThread {
        return ActionUpdateThread.BGT
    }
}
