package ai.zencoder.plugin.providers.completion.nextchange

import com.intellij.history.core.ChangeProcessor
import com.intellij.history.core.changes.Change
import com.intellij.history.core.changes.ChangeSet
import java.util.regex.Pattern

class ZencoderCodeChangeProcessor(
    private val projectId: String?,
    patternString: String?,
    private val consumer: (ChangeSet) -> Unit
) : ChangeProcessor {
    private val pattern = patternString?.let { Pattern.compile(it) }
    private val processedChangesSets = mutableSetOf<Long>()

    override fun process(
        changeSet: ChangeSet,
        change: Change,
        changePath: String
    ) {
        if (!processedChangesSets.contains(changeSet.id) && change.matches(projectId, changePath, pattern)) {
            processedChangesSets.add(changeSet.id)
            consumer(changeSet)
        }
    }
}

fun Change.matches(
    projectId: String?,
    path: String,
    pattern: Pattern?
): Boolean {
    if (!affectsPath(path) && !affectsProject(projectId)) return false
    if (pattern != null && !affectsMatching(pattern)) return false
    return true
}
