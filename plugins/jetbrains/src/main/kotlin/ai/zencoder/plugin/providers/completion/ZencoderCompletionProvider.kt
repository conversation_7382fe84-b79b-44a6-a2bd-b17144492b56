package ai.zencoder.plugin.providers.completion

import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.log.SentryConfiguration
import ai.zencoder.plugin.providers.completion.nextchange.NextEditPredictionHandler
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.ui.unittests.ZenIcons
import com.intellij.codeInsight.inline.completion.*
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionGrayTextElement
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSingleSuggestion
import com.intellij.codeInsight.lookup.LookupManager
import com.intellij.lang.Language
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.fileTypes.LanguageFileType
import com.intellij.openapi.project.IndexNotReadyException
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Key
import io.sentry.ITransaction
import io.sentry.Sentry
import io.sentry.SpanStatus
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.emptyFlow
import java.util.concurrent.atomic.AtomicReference
import javax.swing.JComponent
import javax.swing.JLabel
import kotlin.coroutines.cancellation.CancellationException
import kotlin.coroutines.coroutineContext
import kotlin.time.Duration.Companion.milliseconds

val COMPLETION_TEXT_KEY = Key.create<CompletionText>("COMPLETION_TEXT")
val LAST_NES_TRIGGER_TIME_KEY = Key.create<Long>("LAST_NES_TRIGGER_TIME")
const val JOB_REPLACEMENT_TIMEOUT = 5000L
const val NEXT_EDIT_PREDICTION_DEBOUNCE_MS = 300L

val CODE_COMPLETION_LANGUAGES_DENY_LIST: Set<String> = setOf(
    "text", // text files
    "properties", // property files
    "cucumber", // cucumber framework
    "gherkin", // gherkin framework
    "smartyconfig", // configuration files
    "dotenv" // files
)

// exclude by file type explicitly
val CODE_COMPLETION_EXCLUDED_FILE_TYPES: Set<String> = setOf(
    "conf",
    "env"
)

@Suppress("UnstableTypeUsedInSignature")
class ZencoderCompletionProvider : InlineCompletionProvider {
    override val id: InlineCompletionProviderID
        get() = InlineCompletionProviderID("ZencoderCompletionProvider")

    private val customTooltipPresentation by lazy {
        object : InlineCompletionProviderPresentation {
            override fun getTooltip(project: Project?): JComponent {
                return JLabel("Zencoder", ZenIcons.DEFAULT, JLabel.LEFT)
            }
        }
    }
    override val providerPresentation: InlineCompletionProviderPresentation
        get() = customTooltipPresentation

    private val jobCall = AtomicReference<Job?>(null)

    override val insertHandler: InlineCompletionInsertHandler
        get() = ZencoderInlineCompletionInsertHandler.INSTANCE

    private suspend fun getSuggestionDebounced(
        request: InlineCompletionRequest,
        transaction: ITransaction
    ): InlineCompletionSingleSuggestion {
        if (LookupManager.getActiveLookup(request.editor) != null) {
            return InlineCompletionSingleSuggestion.build(elements = emptyFlow())
        }
        return resolveSuggestion(request, transaction)
    }

    private suspend fun requestSuggestion(request: InlineCompletionRequest, requestTransaction: ITransaction) {
        if (LookupManager.getActiveLookup(request.editor) != null) {
            requestTransaction.finish(SpanStatus.FAILED_PRECONDITION)
            return
        }
        service<CodeSuggestionService>().requestSuggestion(request, requestTransaction)
    }

    private suspend fun resolveSuggestion(request: InlineCompletionRequest, transaction: ITransaction): InlineCompletionSingleSuggestion {
        val result = service<CodeSuggestionService>().resolveSuggestion(request, transaction)
        transaction.finish(SpanStatus.OK)
        if (result == null || result.result.isNullOrBlank()) {
            // No completion available, try to trigger next edit prediction
            tryTriggerNextEditPrediction(request.editor)
            return InlineCompletionSingleSuggestion.build(elements = emptyFlow())
        }

        val fileType = request.file.fileType.defaultExtension
        request.editor.putUserData(
            COMPLETION_TEXT_KEY,
            CompletionText(
                requestId = result.requestId,
                text = result.result,
                fileType = fileType,
                createdAt = System.currentTimeMillis()
            )
        )
        return InlineCompletionSingleSuggestion.build {
            emit(InlineCompletionGrayTextElement(result.result))
        }
    }

    private fun tryTriggerNextEditPrediction(editor: Editor) {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        // Use a debounced approach to avoid triggering too frequently
        val lastTriggerTime = editor.getUserData(LAST_NES_TRIGGER_TIME_KEY) ?: 0L
        val currentTime = System.currentTimeMillis()

        if (currentTime - lastTriggerTime > NEXT_EDIT_PREDICTION_DEBOUNCE_MS) {
            editor.putUserData(LAST_NES_TRIGGER_TIME_KEY, currentTime)

            try {
                val nextEditHandler = service<NextEditPredictionHandler>()
                nextEditHandler.onCompletionAbsent(editor)
            } catch (e: Exception) {
                thisLogger().warn("Failed to trigger NEP: ${e.message}")
            }
        }
    }

    override suspend fun getSuggestion(request: InlineCompletionRequest): InlineCompletionSingleSuggestion {
        val resolveTransaction = Sentry.startTransaction("jetbrains.code-completion.v3", SentryConfiguration.DEFAULT_OPERATION)
        val requestTransaction = Sentry.startTransaction("jetbrains.code-completion.backend.v3", SentryConfiguration.DEFAULT_OPERATION)
        return try {
            requestSuggestion(request, requestTransaction)
            replaceJob()
            val debounceDelay = resolveTransaction.startChild(SentryConfiguration.DEFAULT_OPERATION, "code-completion.delay")
            delay(service<ZencoderSettings>().codeCompletionDebounceDelay.milliseconds)
            debounceDelay.finish()
            return getSuggestionDebounced(request, resolveTransaction)
        } catch (_: CancellationException) {
            resolveTransaction.finish(SpanStatus.CANCELLED)
            // Workaround to catch JobCancellationException thrown due to job cancel due to debounce.
            InlineCompletionSingleSuggestion.build(elements = emptyFlow())
        } catch (_: IndexNotReadyException) {
            // index not ready
            resolveTransaction.finish(SpanStatus.ABORTED)
            InlineCompletionSingleSuggestion.build(elements = emptyFlow())
        }
    }

    // implementation was taken from there
    // https://github.com/JetBrains/intellij-community/blob/master/platform/platform-impl/src/com/intellij/codeInsight/inline/completion/DebouncedInlineCompletionProvider.kt#L24
    private suspend fun replaceJob() {
        val newJob = coroutineContext.job
        if (jobCall.compareAndSet(newJob, newJob)) {
            return
        }

        newJob.invokeOnCompletion {
            jobCall.compareAndSet(newJob, null)
        }

        withTimeoutOrNull(JOB_REPLACEMENT_TIMEOUT) {
            while (true) {
                val currentJob = jobCall.get()
                if (jobCall.compareAndSet(currentJob, newJob)) {
                    currentJob?.cancelAndJoin()
                    return@withTimeoutOrNull
                }
            }
        } ?: run {
            thisLogger().warn("Timeout occurred while trying to replace code completion job")
        }
    }

    override fun isEnabled(event: InlineCompletionEvent): Boolean {
        if (!service<ZencoderSettings>().enableInlineCodeCompletion) {
            return false
        }
        val request = event.toRequest() ?: return false

        val filetype = request.file.fileType as? LanguageFileType ?: return false
        if (filetype.isBinary) {
            return false
        }
        if (CODE_COMPLETION_LANGUAGES_DENY_LIST.contains(filetype.language.id.lowercase())) {
            return false
        }
        val fileExtension = request.file.virtualFile.extension.orEmpty().lowercase()
        if (CODE_COMPLETION_EXCLUDED_FILE_TYPES.contains(fileExtension)) {
            return false
        }
        return filetype.language != Language.ANY
    }
}
