package ai.zencoder.plugin.mcp

import ai.zencoder.plugin.api.WorkflowService
import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.settings.ZencoderSettings
import com.intellij.openapi.components.*
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.xmlb.annotations.OptionTag
import com.intellij.util.xmlb.annotations.Transient
import io.sentry.Sentry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.DeserializationStrategy
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.*
import java.net.URL

/**
 * Project-level service for managing MCP servers state.
 * This service delegates most of its functionality to the application-level MCPServersStateAppManager.
 * It maintains project-specific state and handles configuration changes for the project.
 */
@Service(PROJECT)
@State(
    name = "ai.zencoder.plugin.mcp",
    storages = [Storage(StoragePathMacros.WORKSPACE_FILE)],
    reloadable = true
)
class MCPServersStateManager(private val project: Project) : SimplePersistentStateComponent<MCPServersState>(MCPServersState()) {

    override fun loadState(state: MCPServersState) {
        super.loadState(state)
        state.zencoderBackendTools = emptyMap()
        state.libraryTools = emptyMap()
    }

    /**
     * Fetches and caches Zencoder backend tools.
     * This method helps avoid duplicate API calls and follows the DRY principle.
     *
     * @param project The project to use for fetching tools
     * @return Map of Zencoder backend tools
     */
    suspend fun getZencoderBackendTools(project: Project): Map<ServerId, McpServerInfo> {
        // Return cached tools if available
        if (state.zencoderBackendTools.isNotEmpty()) {
            return state.zencoderBackendTools
        }

        try {
            // Fetch Zencoder backend tools
            val zencoderBackendTools = withContext(Dispatchers.IO) {
                project.service<WorkflowService>().getAvailableToolkits()
            }

            // Convert to McpServerInfo and cache
            val toolsMap = zencoderBackendTools.availableToolkits.mapValues { (serverId, serverInfo) ->
                state.internalToolsState.computeIfAbsent(serverId) { true }
                val isEnabled = state.internalToolsState[serverId] ?: false
                McpServerInfo(
                    id = serverId,
                    name = serverInfo.name,
                    description = serverInfo.description,
                    author = serverInfo.type.value,
                    status = McpServerStatus(
                        status = if (isEnabled) ToolStatus.INSTALLED else ToolStatus.LIBRARY
                    ),
                    tools = serverInfo.tools,
                    icon = serverInfo.icon
                )
            }

            // Cache the result
            state.zencoderBackendTools = toolsMap
            return toolsMap
        } catch (e: Exception) {
            thisLogger().warn("Failed to fetch Zencoder backend tools", e)
            Sentry.captureException(e)
            return emptyMap()
        }
    }

    /**
     * Fetches and caches library tools from cloud storage.
     * This method delegates to the application-level service.
     *
     * @return Map of library tools
     */
    suspend fun getLibraryTools(project: Project): Map<ServerId, McpServerInfo> {
        // Return cached tools if available
        if (state.libraryTools.isNotEmpty()) {
            return state.libraryTools
        }

        try {
            // Fetch library tools from cloud storage
            val cloudStorageEndpoint = service<ZencoderSettings>().cloudStorageUrl
            val response = withContext(Dispatchers.IO) {
                URL("$cloudStorageEndpoint/mcp-library.json").readText()
            }

            // Parse the response
            val libraryTools = Json.decodeFromString<Map<String, McpServerInfo>>(response).mapValues { (serverId, serverInfo) ->
                // Set ID field to match the key and update status
                serverInfo.copy(
                    id = serverId,
                    status = McpServerStatus(
                        status = if (state.mcpServers.containsKey(serverId)) ToolStatus.INSTALLED else ToolStatus.LIBRARY,
                        config = state.mcpServers[serverId]
                    )
                )
            }

            // Cache the result
            state.libraryTools = libraryTools
            return libraryTools
        } catch (e: Exception) {
            thisLogger().warn("Failed to fetch library tools", e)
            Sentry.captureException(e)
            return emptyMap()
        }
    }

    /**
     * Gets the internal tools state for a given server ID.
     *
     * @param serverId The ID of the server
     * @return True if the tool is enabled, false otherwise
     */
    fun getInternalToolState(serverId: ServerId): Boolean {
        return state.internalToolsState[serverId] ?: false
    }

    /**
     * Gets all server configurations.
     *
     * @return Map of server IDs to server configurations
     */
    fun getAllServerConfigs(): Map<ServerId, ServerConfig> {
        return state.mcpServers
    }

    /**
     * Gets the server configuration for a given server ID.
     *
     * @param serverId The ID of the server
     * @return The server configuration, or null if not found
     */
    fun getServerConfig(serverId: ServerId): ServerConfig? {
        return state.mcpServers[serverId]
    }

    /**
     * Sets the server configuration for a given server ID.
     * If a configuration with the same ID already exists, it will be overwritten.
     *
     * @param serverId The ID of the server
     * @param config The server configuration
     * @param projectName Optional project name for logging purposes
     * @return True if a new configuration was added, false if an existing one was updated
     */
    fun setServerConfig(
        serverId: ServerId,
        config: ServerConfig,
        projectName: String? = null
    ): Boolean {
        val isNewConfig = !state.mcpServers.containsKey(serverId)
        val logMessage = if (isNewConfig) {
            "Adding new server configuration for '$serverId'${projectName?.let { " from project '$it'" } ?: ""}"
        } else {
            "Updating existing server configuration for '$serverId'${projectName?.let { " from project '$it'" } ?: ""}"
        }
        thisLogger().info(logMessage)

        state.mcpServers[serverId] = config
        return isNewConfig
    }

    /**
     * Sets the internal tools state for a given server ID.
     *
     * @param serverId The ID of the server
     * @param enabled True to enable the tool, false to disable
     * @param projectName Optional project name for logging purposes
     * @return True if the state was changed, false if it remained the same
     */
    fun setInternalToolState(
        serverId: ServerId,
        enabled: Boolean,
        projectName: String? = null
    ): Boolean {
        val currentState = state.internalToolsState[serverId]

        if (currentState == enabled) {
            // State is already set to the desired value
            return false
        }

        thisLogger().info(
            "Setting internal tool state for '$serverId' to ${if (enabled) "enabled" else "disabled"}${
                projectName?.let {
                    " from project '$it'"
                } ?: ""
            }"
        )
        state.internalToolsState[serverId] = enabled
        return true
    }

    /**
     * Checks if a tool ID belongs to a Zencoder backend tool.
     * This method delegates to the application-level service.
     *
     * @param toolId The ID of the tool to check
     * @return True if the tool is a Zencoder backend tool, false otherwise
     */
    fun isZencoderBackendTool(toolId: String): Boolean {
        return state.zencoderBackendTools.containsKey(toolId)
    }

    /**
     * Checks if a tool ID belongs to a library tool.
     * This method delegates to the application-level service.
     *
     * @param toolId The ID of the tool to check
     * @return True if the tool is a library tool, false otherwise
     */
    fun isLibraryTool(toolId: String): Boolean {
        return state.libraryTools.containsKey(toolId)
    }

    /**
     * Removes the server configuration for a given server ID.
     *
     * @param serverId The ID of the server
     */
    fun removeServerConfig(serverId: ServerId) {
        state.mcpServers.remove(serverId)
    }

    /**
     * Clears all cached tools.
     * This method delegates to the application-level service.
     */
    fun clearToolsCache() {
        state.zencoderBackendTools = emptyMap()
        state.libraryTools = emptyMap()
    }
}

/**
 * Project-level state for MCP servers.
 * This state is now primarily used for migration tracking.
 */
class MCPServersState : BaseState() {
    @get:OptionTag(converter = ServerConfigConverter::class)
    var mcpServers by map<ServerId, ServerConfig>()

    // Track which internal tools are enabled
    @get:OptionTag(converter = InternalToolsStateConverter::class)
    var internalToolsState by map<ServerId, Boolean>()

    // These fields are not persisted, used for caching tools
    @Transient
    var zencoderBackendTools: Map<ServerId, McpServerInfo> = emptyMap()

    @Transient
    var libraryTools: Map<ServerId, McpServerInfo> = emptyMap()
}

typealias ServerId = String

@Serializable(with = ServerConfigSerializer::class)
sealed interface ServerConfig {
    var type: TransportType
}

@Serializable
data class StdioServerConfig(
    override var type: TransportType = TransportType.STDIO,
    var command: String,
    var cwd: String? = null,
    var args: List<String> = emptyList(),
    var env: Map<String, String>? = emptyMap()
) : ServerConfig

@Serializable
data class SseServerConfig(
    override var type: TransportType = TransportType.SSE,
    var url: String
) : ServerConfig

@Serializable
data class InternalServerConfig(
    override var type: TransportType = TransportType.INTERNAL,
    var name: String
) : ServerConfig

@Serializable
enum class TransportType {
    /**
     * STDIO transport used for communicating with external processes via stdin/stdout
     */
    STDIO,

    /**
     * SSE transport used for real-time communication over EventSource
     */
    SSE,

    /**
     * Internal transport used for communicating between MCP clients within the same process
     */
    INTERNAL
}

object ServerConfigSerializer : JsonContentPolymorphicSerializer<ServerConfig>(ServerConfig::class) {
    override fun selectDeserializer(element: JsonElement): DeserializationStrategy<ServerConfig> {
        val type = element.jsonObject.getOrElse("type") { null }?.jsonPrimitive?.content ?: TransportType.STDIO.name

        return when (type) {
            TransportType.STDIO.name -> StdioServerConfig.serializer()
            TransportType.SSE.name -> SseServerConfig.serializer()
            TransportType.INTERNAL.name -> InternalServerConfig.serializer()
            else -> throw IllegalStateException("Unexpected server config type: $type")
        }
    }
}
