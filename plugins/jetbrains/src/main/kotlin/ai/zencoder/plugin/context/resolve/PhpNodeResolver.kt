package ai.zencoder.plugin.context.resolve

import ai.zencoder.plugin.context.SimpleASTNode
import com.intellij.openapi.application.runReadAction
import com.jetbrains.php.lang.psi.PhpFile
import com.jetbrains.php.lang.psi.elements.Field
import com.jetbrains.php.lang.psi.elements.Include
import com.jetbrains.php.lang.psi.elements.Method
import com.jetbrains.php.lang.psi.elements.PhpClass
import kotlin.reflect.KClass

class PhpNodeResolver : ASTNodeResolver<PhpClass, Method, Field, PhpFile> {
    override fun classElement(): KClass<PhpClass> = PhpClass::class

    override fun functionElement(): KClass<Method> = Method::class

    override fun fieldElement(): KClass<Field> = Field::class

    override fun fileElement(): KClass<PhpFile> = PhpFile::class

    override fun importsElement(): KClass<Include> = Include::class

    override fun extractClassSignature(element: PhpClass): SimpleASTNode? {
        if (element.name.isEmpty()) {
            return null
        }
        val classLine = runReadAction { "class ${element.name}" }

        return SimpleASTNode.forClassHeader(psiElement = element, className = element.name, signature = classLine)
    }

    override fun extractMethodSignature(element: Method): SimpleASTNode? {
        if (element.name.isEmpty()) {
            return null
        }
        val visibility = element.access.name.lowercase()
        val name = element.name
        val parameters = element.parameters.joinToString(", ") { param ->
            param.text
        }

        val returnType = element.typeDeclaration?.type?.toString() ?: "void"
        return SimpleASTNode.forMethod(
            psiElement = element,
            methodName = element.name,
            signature = "$visibility function $name($parameters): $returnType"
        )
    }

    override fun extractFieldSignature(element: Field): SimpleASTNode? {
        if (element.name.isEmpty()) {
            return null
        }
        val visibility = element.modifier.access.name.lowercase()
        return SimpleASTNode.forProperty(psiElement = element, propertyName = element.name, signature = "$visibility ${element.text}")
    }
}
