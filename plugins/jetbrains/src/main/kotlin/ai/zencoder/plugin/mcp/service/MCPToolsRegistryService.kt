package ai.zencoder.plugin.mcp.service

import ai.zencoder.plugin.mcp.MCPLibraryManager
import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.McpServerStatus
import ai.zencoder.plugin.mcp.ServerConfig
import ai.zencoder.plugin.mcp.StdioServerConfig
import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.mcp.model.ToolsListPayload
import ai.zencoder.plugin.mcp.model.ToolsRegistryMessage
import ai.zencoder.plugin.mcp.model.ToolsRegistryRequest
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

private val logger = KotlinLogging.logger {}

/**
 * Service for managing the MCP Tools Registry state and operations.
 */
@Service(PROJECT)
class MCPToolsRegistryService(
    private val project: Project,
    private val cs: CoroutineScope
) {
    private val coroutineScope = CoroutineScope(cs.coroutineContext + SupervisorJob() + Dispatchers.IO)
    private val _toolsUpdates = MutableSharedFlow<ToolsRegistryMessage>()
    val toolsUpdates: SharedFlow<ToolsRegistryMessage> = _toolsUpdates.asSharedFlow()

    // Using a Map directly instead of a List for better performance and reduced code complexity
    private val toolsMap: ConcurrentHashMap<String, McpServerInfo> = ConcurrentHashMap()

    /**
     * Loads the tools from the library and installed tools.
     */
    suspend fun loadTools() {
        logger.info { "Loading MCP tools" }
        val libraryManager = project.service<MCPLibraryManager>()
        val toolsState = libraryManager.getAgentToolsState()

        // Clear existing tools and populate with new data
        toolsMap.clear()
        toolsState.forEach { (id, info) ->
            // Ensure the ID field is set correctly
            toolsMap[id] = info.copy(id = id)
        }

        // Notify subscribers about the updated tools map
        _toolsUpdates.emit(
            ToolsRegistryMessage.ToolsListUpdate(
                payload = ToolsListPayload(toolsMap)
            )
        )

        logger.info { "Loaded ${toolsMap.size} MCP tools" }
    }

    /**
     * Installs a tool.
     */
    fun installTool(
        toolId: String,
        toolName: String?,
        config: ServerConfig
    ) {
        coroutineScope.launch {
            try {
                // Get the tool name
                val toolName = toolName ?: toolId

                // Add tool to the map
                if (!toolsMap.containsKey(toolId)) {
                    // New user server
                    toolsMap[toolId] = McpServerInfo(
                        name = toolName,
                        status = McpServerStatus(
                            status = ToolStatus.LIBRARY,
                            config = config
                        )
                    )
                }
                // Update UI to show installing status
                updateToolStatus(toolId, ToolStatus.INSTALLING)

                // Install the tool
                val libraryManager = project.service<MCPLibraryManager>()
                libraryManager.installAgentTool(toolId, toolName, config)

                // Update UI to show installed status
                updateToolStatus(toolId, ToolStatus.INSTALLED)
            } catch (e: Throwable) {
                logger.error(e) { "Error installing tool $toolId" }
                val userFriendlyErrorMessage = createUserFriendlyErrorMessage(e, config)
                updateToolStatus(toolId, ToolStatus.ERROR, userFriendlyErrorMessage)
            }
        }
    }

    /**
     * Uninstalls a tool.
     */
    fun uninstallTool(toolId: String) {
        coroutineScope.launch {
            try {
                // Uninstall the tool
                val libraryManager = project.service<MCPLibraryManager>()
                libraryManager.uninstallAgentTool(toolId)

                // Update UI to show installed status
                updateToolStatus(toolId, ToolStatus.LIBRARY)
            } catch (e: Throwable) {
                logger.error(e) { "Error uninstalling tool $toolId" }
                updateToolStatus(toolId, ToolStatus.ERROR, e.message)
            }
        }
    }

    /**
     * Updates the configuration of a tool.
     */
    fun updateToolConfig(toolId: String, config: ServerConfig) {
        coroutineScope.launch {
            try {
                // First uninstall the tool
                val libraryManager = project.service<MCPLibraryManager>()
                libraryManager.uninstallAgentTool(toolId)

                // Then reinstall with new config
                val toolName = toolsMap[toolId]?.name ?: toolId
                libraryManager.installAgentTool(toolId, toolName, config)

                // Update UI to show installed status
                updateToolStatus(toolId, ToolStatus.INSTALLED)
            } catch (e: Throwable) {
                logger.error(e) { "Error updating tool config for $toolId" }
                val userFriendlyErrorMessage = createUserFriendlyErrorMessage(e, config)
                updateToolStatus(toolId, ToolStatus.ERROR, userFriendlyErrorMessage)
            }
        }
    }

    /**
     * Updates the status of a tool and notifies subscribers.
     */
    suspend fun updateToolStatus(
        toolId: String,
        status: ToolStatus,
        errorMessage: String? = null
    ) {
        // Update the tool status in the map
        toolsMap[toolId]?.let { toolItem ->
            // Update the tool with new status
            toolsMap[toolId] = toolItem.copy(
                status = McpServerStatus(
                    status = status,
                    detail = errorMessage,
                    config = toolItem.config
                )
            )
        }

        // Notify subscribers about the status change
        _toolsUpdates.emit(
            ToolsRegistryMessage.ToolsListUpdate(
                payload = ToolsListPayload(toolsMap)
            )
        )
    }

    /**
     * Handles requests from the webview.
     */
    fun handleWebviewRequest(request: ToolsRegistryRequest) {
        when (request) {
            is ToolsRegistryRequest.RefreshTools -> {
                coroutineScope.launch {
                    loadTools()
                }
            }

            is ToolsRegistryRequest.InstallTool -> {
                installTool(request.toolId, request.toolName, request.config)
            }

            is ToolsRegistryRequest.UninstallTool -> {
                uninstallTool(request.toolId)
            }

            is ToolsRegistryRequest.UpdateToolConfig -> {
                updateToolConfig(request.toolId, request.config)
            }

            is ToolsRegistryRequest.NavigateToToolsRegistry -> {
                // This is handled by the action class, but we include it here for completeness
                coroutineScope.launch {
                    loadTools() // Ensure tools are loaded before navigation
                }
            }

            is ToolsRegistryRequest.SearchTools -> {
                // Handle search tools request if needed
                logger.info { "Search tools request received: ${request.query}, category: ${request.category}" }
                // Implementation would go here
            }
        }
    }

    /**
     * Gets the tools state as a map for the webview.
     */
    fun getToolsStateForWebview(): Map<String, McpServerInfo> {
        // Since we're now using McpServerInfo directly, we can just return the map
        return toolsMap
    }

    /**
     * Creates a user-friendly error message based on the error and configuration.
     */
    private fun createUserFriendlyErrorMessage(e: Throwable, config: ServerConfig): String {
        val errorMessage = (e.message ?: "Unknown error") + "."
        return when (config) {
            is StdioServerConfig -> {
                when (config.command) {
                    "node", "npx" ->
                        "$errorMessage Please ensure Node.js is installed and configured properly."
                    "uv", "uvx" ->
                        "$errorMessage Please ensure uv is installed and configured properly."
                    "docker" ->
                        "$errorMessage Please ensure Docker is installed and running."
                    else -> errorMessage
                }
            }
            else -> errorMessage
        }
    }
}
