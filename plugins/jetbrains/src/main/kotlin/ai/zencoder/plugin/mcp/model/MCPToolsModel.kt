package ai.zencoder.plugin.mcp.model

import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.ServerConfig
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Model classes for MCP Tools Registry UI
 */

/**
 * Represents a category of tools in the UI
 */
enum class ToolCategory {
    ALL,
    ZENCODER,
    CUSTOM,
    LIBRARY
}

/**
 * Represents the status of a tool
 */
enum class ToolStatus {
    INSTALLED,
    LIBRARY,
    ERROR,
    INSTALLING
}

/**
 * Backward compatibility extension function
 * This maintains compatibility with existing code that expects toToolItem to return a ToolItem
 */
fun McpServerInfo.toToolItem(id: String): McpServerInfo {
    return this.copy(id = id)
}

/**
 * Represents a message to be sent to the webview
 */
@Serializable
sealed interface ToolsRegistryMessage {
    /**
     * Message for tool list updates
     */
    @Serializable
    @SerialName("tools-list-update")
    data class ToolsListUpdate(val payload: ToolsListPayload) : ToolsRegistryMessage

    /**
     * Message for tool installation status updates
     */
    @Serializable
    @SerialName("tool-status-update")
    data class ToolStatusUpdate(val payload: ToolInstallationStatusPayload) : ToolsRegistryMessage

    /**
     * Message for tool configuration updates
     */
    @Serializable
    @SerialName("tool-config-update")
    data class ToolConfigUpdate(val payload: ToolConfigurationPayload) : ToolsRegistryMessage
}

/**
 * Payload for tool list updates
 */
@Serializable
data class ToolsListPayload(val tools: Map<String, McpServerInfo>) {
    constructor(tools: List<McpServerInfo>) : this(tools.associateBy { it.id })

    // For backward compatibility with existing code
    val toolsList: List<McpServerInfo>
        get() = tools.values.toList()
}

/**
 * Payload for tool installation status updates
 */
@Serializable
data class ToolInstallationStatusPayload(
    val toolId: String,
    val status: ToolStatus,
    val error: String? = null
)

/**
 * Payload for tool configuration updates
 */
@Serializable
data class ToolConfigurationPayload(
    val toolId: String,
    val config: ServerConfig
)

/**
 * Represents a message from the webview
 */
@Serializable
sealed interface ToolsRegistryRequest {
    /**
     * Request to refresh the tools list
     */
    @Serializable
    @SerialName("refresh-tools")
    data object RefreshTools : ToolsRegistryRequest

    /**
     * Request to install a tool
     */
    @Serializable
    @SerialName("install-tool")
    data class InstallTool(
        val toolId: String,
        val toolName: String,
        val config: ServerConfig
    ) : ToolsRegistryRequest

    /**
     * Request to uninstall a tool
     */
    @Serializable
    @SerialName("uninstall-tool")
    data class UninstallTool(val toolId: String) : ToolsRegistryRequest

    /**
     * Request to update a tool's configuration
     */
    @Serializable
    @SerialName("update-tool-config")
    data class UpdateToolConfig(
        val toolId: String,
        val config: ServerConfig
    ) : ToolsRegistryRequest

    /**
     * Request to navigate to the tools registry
     */
    @Serializable
    @SerialName("navigate-to-tools-registry")
    data object NavigateToToolsRegistry : ToolsRegistryRequest

    /**
     * Request to search for tools
     */
    @Serializable
    @SerialName("search-tools")
    data class SearchTools(
        val query: String,
        val category: ToolCategory
    ) : ToolsRegistryRequest
}

@Serializable
data class AgentToolsStatePayload(
    val tools: Map<String, McpServerInfo>,
    val isLoading: Boolean,
    val error: ErrorInfo? = null
)

/**
 * Represents error information to be displayed in the UI
 */
@Serializable
data class ErrorInfo(
    val message: String,
    val details: String? = null,
    val severity: ErrorSeverity = ErrorSeverity.ERROR
)

/**
 * Represents the severity of an error
 */
enum class ErrorSeverity {
    INFO,
    WARNING,
    ERROR
}
