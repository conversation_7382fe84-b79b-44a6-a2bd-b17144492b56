package ai.zencoder.plugin.ui.tools.components

import ai.zencoder.plugin.ui.tools.utils.UIStyleUtils
import com.intellij.ui.JBColor
import com.intellij.ui.SearchTextField
import com.intellij.ui.components.JBLabel
import java.awt.BorderLayout
import java.awt.Dimension
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.event.DocumentEvent
import javax.swing.event.DocumentListener

/**
 * Component for displaying a search panel with label and search field.
 * Follows JetBrains UI guidelines for search components.
 */
class SearchPanelComponent(private val labelText: String) : InteractiveComponent<SearchPanelComponent.SearchListener> {

    interface SearchListener {
        fun onSearchTextChanged(searchText: String)
    }

    private val panel = JPanel(BorderLayout())
    private val searchField = SearchTextField()
    private var searchListener: SearchListener? = null

    init {
        setupPanel()
    }

    private fun setupPanel() {
        panel.apply {
            // Use standard spacing
            border = UIStyleUtils.Borders.emptyBorder(
                UIStyleUtils.Spacing.SMALL,
                UIStyleUtils.Spacing.LARGE,
                UIStyleUtils.Spacing.SMALL,
                UIStyleUtils.Spacing.LARGE
            )
            isOpaque = false

            // Add a label for the search field with proper styling
            val searchLabel = JBLabel(labelText).apply {
                labelFor = searchField
                border = UIStyleUtils.Borders.emptyBorder(0, 0, 0, UIStyleUtils.Spacing.SMALL)
                foreground = JBColor.foreground()
                font = UIStyleUtils.Typography.REGULAR_FONT
            }

            // Improve search field appearance following JetBrains UI guidelines
            searchField.apply {
                preferredSize = Dimension(UIStyleUtils.Sizes.SEARCH_FIELD_WIDTH, preferredSize.height)
                textEditor.putClientProperty("JTextField.Search.noBorderRing", false)
                textEditor.putClientProperty("StatusVisibleFunction", java.util.function.Function<Any, Boolean> { true })
                textEditor.putClientProperty("JTextField.Search.Gap", UIStyleUtils.Spacing.TINY)
                textEditor.font = UIStyleUtils.Typography.REGULAR_FONT

                // Add document listener to notify about text changes
                textEditor.document.addDocumentListener(object : DocumentListener {
                    override fun insertUpdate(e: DocumentEvent) = notifyTextChanged()
                    override fun removeUpdate(e: DocumentEvent) = notifyTextChanged()
                    override fun changedUpdate(e: DocumentEvent) = notifyTextChanged()
                })
            }

            add(searchLabel, BorderLayout.WEST)
            add(searchField, BorderLayout.CENTER)
        }
    }

    private fun notifyTextChanged() {
        searchListener?.onSearchTextChanged(searchField.text)
    }

    override fun createComponent(): JComponent = panel

    override fun setEventHandler(handler: SearchListener) {
        searchListener = handler
    }

    /**
     * Gets the current search text.
     * @return The current text in the search field
     */
    fun getSearchText(): String = searchField.text

    /**
     * Sets the search text programmatically.
     * @param text The text to set in the search field
     */
    fun setSearchText(text: String) {
        searchField.text = text
    }
}
