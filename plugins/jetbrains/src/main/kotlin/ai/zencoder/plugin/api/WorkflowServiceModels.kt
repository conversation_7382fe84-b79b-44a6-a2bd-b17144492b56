package ai.zencoder.plugin.api

import ai.zencoder.generated.client.all.models.ZencoderWorkflowAIHistoryMessageContentInner
import ai.zencoder.generated.client.all.models.ZencoderWorkflowAgentContextMessageHistoryInner
import ai.zencoder.generated.client.all.models.ZencoderWorkflowHistoryContextFile
import ai.zencoder.generated.client.all.models.ZencoderWorkflowHistoryToolCall

// Those classes are needed because there is no possibility
// to correctly generate polymorphic classes with openapi generator.

class AIHistoryMessage(
    override val content: List<ZencoderWorkflowAIHistoryMessageContentInner>,
    override val id: String? = null,
    override val name: String? = null,
    override val toolCalls: List<ZencoderWorkflowHistoryToolCall>? = null
) : ZencoderWorkflowAgentContextMessageHistoryInner {
    override val toolCallId: String
        get() = ""
    override val type: ZencoderWorkflowAgentContextMessageHistoryInner.Type
        get() = ZencoderWorkflowAgentContextMessageHistoryInner.Type.ASSISTANT
}

class SystemHistoryMessage(
    override val content: List<ZencoderWorkflowAIHistoryMessageContentInner>,
    override val id: String? = null,
    override val name: String? = null
) : ZencoderWorkflowAgentContextMessageHistoryInner {
    override val toolCallId: String
        get() = ""
    override val toolCalls: List<ZencoderWorkflowHistoryToolCall>?
        get() = null
    override val type: ZencoderWorkflowAgentContextMessageHistoryInner.Type
        get() = ZencoderWorkflowAgentContextMessageHistoryInner.Type.SYSTEM
}

class ToolHistoryMessage(
    override val content: List<ZencoderWorkflowAIHistoryMessageContentInner>,
    override val toolCallId: String,
    override val id: String? = null,
    override val name: String? = null
) : ZencoderWorkflowAgentContextMessageHistoryInner {
    override val type: ZencoderWorkflowAgentContextMessageHistoryInner.Type
        get() = ZencoderWorkflowAgentContextMessageHistoryInner.Type.TOOL
    override val toolCalls: List<ZencoderWorkflowHistoryToolCall>?
        get() = null
}

class UserHistoryMessage(
    override val content: List<ZencoderWorkflowAIHistoryMessageContentInner>,
    override val id: String? = null,
    override val name: String? = null
) : ZencoderWorkflowAgentContextMessageHistoryInner {
    override val toolCallId: String
        get() = ""
    override val type: ZencoderWorkflowAgentContextMessageHistoryInner.Type
        get() = ZencoderWorkflowAgentContextMessageHistoryInner.Type.USER
    override val toolCalls: List<ZencoderWorkflowHistoryToolCall>?
        get() = null
}

class TextContentPart(override val text: String) : ZencoderWorkflowAIHistoryMessageContentInner {
    override val attachment: ZencoderWorkflowHistoryContextFile
        get() = ZencoderWorkflowHistoryContextFile(path = "", fsPath = "")
    override val codebaseEnabled: Boolean
        get() = false
    override val type: ZencoderWorkflowAIHistoryMessageContentInner.Type
        get() = ZencoderWorkflowAIHistoryMessageContentInner.Type.TEXT
}

class AttachmentContentPart(override val attachment: ZencoderWorkflowHistoryContextFile) : ZencoderWorkflowAIHistoryMessageContentInner {
    override val text: String
        get() = ""
    override val codebaseEnabled: Boolean
        get() = false
    override val type: ZencoderWorkflowAIHistoryMessageContentInner.Type
        get() = ZencoderWorkflowAIHistoryMessageContentInner.Type.ATTACHMENT
}

class OptionsContentPart(override val codebaseEnabled: Boolean) : ZencoderWorkflowAIHistoryMessageContentInner {
    override val attachment: ZencoderWorkflowHistoryContextFile
        get() = ZencoderWorkflowHistoryContextFile(path = "", fsPath = "")
    override val text: String
        get() = ""
    override val type: ZencoderWorkflowAIHistoryMessageContentInner.Type
        get() = ZencoderWorkflowAIHistoryMessageContentInner.Type.OPTIONS
}
