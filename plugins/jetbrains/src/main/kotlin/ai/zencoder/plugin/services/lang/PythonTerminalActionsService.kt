package ai.zencoder.plugin.services.lang

import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.roots.ProjectRootManager
import com.intellij.openapi.util.SystemInfo
import com.intellij.openapi.util.io.FileUtil
import com.intellij.sh.run.ShRunConfiguration
import com.jetbrains.python.sdk.PythonSdkType
import com.jetbrains.python.sdk.PythonSdkUtil

class PythonTerminalActionsService : LangSpecificTerminalActionsService {

    override fun patchShConfiguration(
        project: Project,
        terminalName: String,
        command: String,
        configuration: ShRunConfiguration
    ) {
        val projectSdk = ProjectRootManager.getInstance(project).projectSdk
        val envVars = mutableMapOf<String, String>()
        if (projectSdk != null && PythonSdkUtil.isVirtualEnv(projectSdk)) {
            PythonSdkType.patchEnvironmentVariablesForVirtualenv(envVars, projectSdk)
        }

        if (envVars.isEmpty()) {
            configuration.scriptText = command
            configuration.isExecuteScriptFile = false
        } else {
            // JB ignores env vars in script text, so we need to create a script file
            configuration.envData = configuration.envData.with(envVars)
            try {
                val scriptFile = FileUtil.createTempFile(terminalName, if (SystemInfo.isWindows) ".bat" else ".sh")
                scriptFile.writeText(command)
                configuration.scriptPath = scriptFile.path
                configuration.isExecuteScriptFile = true
            } catch (e: Exception) {
                thisLogger().error("Failed to create temporary script file for command execution", e)
                configuration.scriptText = command
                configuration.isExecuteScriptFile = false
            }
        }
    }
}
