package ai.zencoder.plugin.observers.graphql

import ai.zencoder.agent.operations.*
import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.analytics.ToolCallTracker
import ai.zencoder.plugin.chat.util.getChatAnalyticsProps
import ai.zencoder.plugin.chat.util.lastMessageAssistantProperties
import ai.zencoder.plugin.graphql.generated.models.Context
import ai.zencoder.plugin.obfuscation.DoNotObfuscate
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.services.GraphQLExecutor
import ai.zencoder.plugin.socket.ably.AblyConnectionManager
import ai.zencoder.plugin.utils.resolve
import ai.zencoder.plugin.utils.uuid
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.chat.ChatRequestService
import ai.zencoder.plugin.webview.chat.OperationId
import ai.zencoder.plugin.webview.chat.handlers.ChatShellCommandHandler
import ai.zencoder.plugin.webview.chat.model.CustomInstructionStateManager
import ai.zencoder.plugin.webview.model.*
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.google.gson.JsonObject
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import graphql.ExecutionInput
import io.ably.lib.realtime.*
import io.ably.lib.types.ErrorInfo
import io.ably.lib.types.Message
import io.ably.lib.types.MessageExtras
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.concurrent.TimeUnit

private const val AGENT_GQL_COMMANDS_EVENT_NAME = "gql"
private const val AGENT_OPERATIONS_EVENT_NAME = "agent_operation_event"

private typealias ProcessingMessageId = String

@Suppress("LoggingSimilarMessage")
@Service(Service.Level.PROJECT)
class AgentCommunicationsService(
    val project: Project,
    val cs: CoroutineScope
) : Disposable {

    companion object {
        /*
         * The maximum time an agent session should be kept alive before being automatically closed.
         */
        private const val AGENT_SESSION_LIFECYCLE_IN_MINUTES = 15L
        private const val CLEAN_CHUNKED_MESSAGES_CACHE_IN_MINUTES = 10L
        private const val MAX_MESSAGE_SIZE = 65536
        private const val MAX_AUX_DATA_SIZE = 1024
        private const val CHUNK_SIZE = MAX_MESSAGE_SIZE - MAX_AUX_DATA_SIZE

        private const val CHUNK_EVENT_ID_HEADER_NAME = "x-operation-event-id"
        private const val IS_COMPRESSED_HEADER_NAME = "x-operation-event-compressed"
        private const val TOTAL_CHUNKS_HEADER_NAME = "x-operation-event-total-chunks"
        private const val CHUNK_INDEX_HEADER_NAME = "x-operation-event-chunk-index"
    }

    private val chatManager get() = project.service<ChatManager>()
    private val requestService get() = project.service<ChatRequestService>()
    private val customInstructionState get() = service<CustomInstructionStateManager>()
    private val chunkedMessagesCollector = ChunkedMessagesCollector()
    private val chatMessageProcessor = ChatMessageProcessor()
    private val toolCallTracker get() = service<ToolCallTracker>()

    private val socketConnectionManager get() = service<AblyConnectionManager>()
    private val objectMapper = ObjectMapper()

    private var agentGQLCommandsListener: Channel.MessageListener? = null
    private var agentOperationsListener: Channel.MessageListener? = null

    private var lastReconnectAttemptDate: Instant? = null
    val ongoingOperationsCache: Cache<OperationId, OperationContext> = CacheBuilder.newBuilder()
        // In some cases we can miss the ending agent operation call (due to network issues for example)
        .expireAfterWrite(AGENT_SESSION_LIFECYCLE_IN_MINUTES, TimeUnit.MINUTES)
        .removalListener<OperationId, OperationContext> { event ->
            event.key?.let {
                project.serviceOrNull<ChatManager>()?.endOperation(it)
            }
            unsubscribeIfCacheIsEmpty()
        }
        .build()

    @Synchronized
    fun registerOperation(
        operationId: OperationId,
        agentName: String?,
        ideState: Context?
    ) {
        if (ongoingOperationsCache.size() == 0L) {
            subscribeToOperationSessionChannel()
        }
        ongoingOperationsCache.put(
            operationId,
            OperationContext(
                agentName = agentName,
                processingMessageId = null,
                initialOperationIdeState = ideState
            )
        )
    }

    @Synchronized
    fun reconnect() {
        val avoidReconnectingTooOftenToPreventLoopScenarios =
            lastReconnectAttemptDate?.isAfter(Instant.now().minus(15, ChronoUnit.MINUTES)) ?: false
        if (avoidReconnectingTooOftenToPreventLoopScenarios) {
            Sentry.captureException(Exception("Reconnect initiated too often"))
            return
        }
        lastReconnectAttemptDate = Instant.now()

        thisLogger().info("Reconnecting...")
        val resubscribeAfterRefreshRequired = ongoingOperationsCache.asMap().keys.isNotEmpty()
        socketConnectionManager.refreshWithNoChannelsDetach()
        thisLogger().info("Refreshing Ably connection, resubscribe required: $resubscribeAfterRefreshRequired")
        if (resubscribeAfterRefreshRequired) {
            subscribeToOperationSessionChannel()
        }
    }

    @Synchronized
    fun unregisterOperation(operationId: OperationId) {
        ongoingOperationsCache.invalidate(operationId)
    }

    @Synchronized
    private fun unsubscribeIfCacheIsEmpty() {
        if (ongoingOperationsCache.size() == 0L) {
            val sessionChannel = socketConnectionManager.getOperationSessionChannel(project)
            thisLogger().info("Unsubscribing: ${sessionChannel.name}")
            agentGQLCommandsListener?.let { sessionChannel.unsubscribe(it) }
            agentOperationsListener?.let { sessionChannel.unsubscribe(it) }
            agentGQLCommandsListener = null
            agentOperationsListener = null
        }
    }

    private fun subscribeToOperationSessionChannel() {
        val sessionChannel = socketConnectionManager.getOperationSessionChannel(project)
        thisLogger().info("Subscribing to channel: ${sessionChannel.name}")
        subscribe(sessionChannel)
    }

    private fun subscribe(channel: Channel) {
        subscribeToAgentGQLCommandEvents(channel)
        subscribeToAgentOperationEvents(channel)
        channel.once(ChannelState.detached) {
            channel.unsubscribe(AGENT_OPERATIONS_EVENT_NAME, agentOperationsListener)
            channel.unsubscribe(AGENT_GQL_COMMANDS_EVENT_NAME, agentGQLCommandsListener)
            ongoingOperationsCache.invalidateAll()
        }
        channel.once(ChannelState.failed) {
            thisLogger().info("failed state reached for channel: ${channel.name}")
            Sentry.captureException(Exception("Failed state reached for channel: ${channel.name}"))
            reconnect()
        }
    }

    private fun subscribeToAgentGQLCommandEvents(channel: Channel) {
        agentGQLCommandsListener = object : Channel.MessageListener {
            override fun onMessage(message: Message) {
                if (message.data == null) {
                    thisLogger().warn("Received message with null data on channel: ${channel.name}")
                    return
                }
                thisLogger().info("Received message on channel: ${channel.name}")
                val payloadObject = try {
                    ObjectMapper().readValue(
                        (message.data as JsonObject).get("payload").asString,
                        object : TypeReference<Map<String, Any>>() {}
                    )
                } catch (e: Exception) {
                    thisLogger().error("Error parsing message payload: ${e.message}", e)
                    return
                }
                val messageId = try {
                    (message.data as JsonObject).get("id").asString
                } catch (e: Exception) {
                    thisLogger().error("Error extracting message id: ${e.message}", e)
                    return
                }
                val query = payloadObject["query"] as String

                @Suppress("UNCHECKED_CAST")
                val variables = payloadObject["variables"] as? Map<String, Any?>
                val executionInput = ExecutionInput.newExecutionInput()
                    .query(query)
                    .apply {
                        variables?.let { variables(it) }
                    }
                    .build()

                @Suppress("USELESS_ELVIS")
                val executionResult = project.service<GraphQLExecutor>().execute(executionInput) ?: return
                val result = Result(
                    data = executionResult.getData(),
                    errors = executionResult.errors.takeIf { it.isNotEmpty() }
                )
                if (!result.errors.isNullOrEmpty()) {
                    thisLogger().warn("Errors in agent execution result: ${result.errors.joinToString { "$it" }}")
                }
                splitResultByChunksAndSend(result, messageId, channel)
            }
        }
        channel.subscribe(AGENT_GQL_COMMANDS_EVENT_NAME, agentGQLCommandsListener)
    }

    private fun subscribeToAgentOperationEvents(channel: Channel) {
        agentOperationsListener = object : Channel.MessageListener {
            override fun onMessage(message: Message) {
                if (message.hasChunkedEventId()) {
                    chunkedMessagesCollector.collectAndProcessIfLastChunk(message, channel)
                    return
                }
                chatMessageProcessor.processMessage(message, channel)
            }
        }
        channel.subscribe(AGENT_OPERATIONS_EVENT_NAME, agentOperationsListener)
    }

    inner class ChatMessageProcessor {

        fun processMessage(message: Message, channel: Channel) {
            val eventPart = message.data
            if (eventPart !is ByteArray) {
                val exception =
                    Exception("Invalid message type received: ${message.data::class.simpleName} for channel ${channel.name}")
                Sentry.captureException(exception)
                thisLogger().debug(exception)
                return
            }
            thisLogger().info("Received message on channel: ${channel.name}")
            val agentOperationEvent = AgentOperationEvent.ADAPTER.decode(eventPart)
            val operationId = agentOperationEvent.operation_id

            if (agentOperationEvent.event == null) {
                val exception =
                    Exception("No operation event found in message: $agentOperationEvent for operation ID: $operationId")
                Sentry.captureException(exception)
                thisLogger().debug(exception)
                return
            }

            val chatId = chatManager.findChatIdForOperationId(operationId)
            if (chatId == null) {
                val exception =
                    Exception("No chat found for operation ID: $operationId end message ")
                Sentry.captureException(exception)
                thisLogger().debug(exception)
                return
            }
            val chat = chatManager.getChatById(chatId, false)

            val operationContext = ongoingOperationsCache.getIfPresent(operationId)
            if (operationContext == null) {
                val exception =
                    Exception("No operation context available for operation ID: $operationId end message ")
                Sentry.captureException(exception)
                thisLogger().debug(exception)
                return
            }

            val modifyingMessageId = if (operationContext.processingMessageId != null) {
                operationContext.processingMessageId!!
            } else {
                val newMessageId = uuid()
                val emptyAssistantMessage = ChatMessage(
                    id = newMessageId,
                    role = ChatMessageRole.ASSISTANT,
                    content = emptyList(),
                    createdAt = System.currentTimeMillis(),
                    isInternal = false,
                    context = ChatMessageContext(
                        author = operationContext.agentName,
                        operationId = operationId
                    )
                )
                chatManager.addMessageToChat(
                    chat,
                    emptyAssistantMessage,
                    isPermanent = true,
                    isTypingDisabled = true
                )
                operationContext.processingMessageId = newMessageId
                newMessageId
            }

            var isLoading = false
            var content: MessagePart? = null
            var postProcessingData: PostProcessingData? = null

            when (agentOperationEvent.event.key) {
                AgentOperationEvent.EVENT_TEXT_PART -> {
                    val textPart = agentOperationEvent.event.value as TextPart
                    content = MessagePart.Text(textPart.text)
                }

                AgentOperationEvent.EVENT_LOADING_MARKER_PART -> {
                    val loadingMarkerPart = agentOperationEvent.event.value as LoadingMarkerPart
                    isLoading = loadingMarkerPart.is_loading
                    content = loadingMarkerPart.toLoadingMarkerMessagePart()
                }

                AgentOperationEvent.EVENT_CODE_DIFFS_PART -> {
                    val codeDiffsPart = agentOperationEvent.event.value as CodeDiffsPart
                    content = MessagePart.CodePatch(codeDiffsPart.diffs.map { it.toDiffModel() })
                }

                AgentOperationEvent.EVENT_GROUPED_CHECKLIST_PART -> {
                    val groupedChecklistPart = agentOperationEvent.event.value as GroupedChecklistPart
                    content = groupedChecklistPart.toMessagePart()
                }

                AgentOperationEvent.EVENT_UPDATE_CONTEXT_PART -> {
                    val updateContextPart = agentOperationEvent.event.value as UpdateContextPart
                    processUpdateContextEvent(updateContextPart, chat, modifyingMessageId)
                    return
                }

                AgentOperationEvent.EVENT_LOADING_PART -> {
                    val loadingPart = agentOperationEvent.event.value as LoadingPart
                    content = MessagePart.LoadingPart(loadingPart.text)
                    isLoading = loadingPart.is_loading
                }

                AgentOperationEvent.EVENT_TOOL_CALL_PART -> {
                    val toolCallPart = agentOperationEvent.event.value as ToolCallPart
                    content = toolCallPart.toDomain(project)
                    toolCallTracker.trackToolCall(
                        chat = chat,
                        operationId = operationId,
                        agentName = operationContext.agentName,
                        toolCallId = toolCallPart.tool_call_id,
                        statusText = toolCallPart.status_text,
                        status = toolCallPart.status
                    )
                }

                AgentOperationEvent.EVENT_CODE_HUNK_PART -> {
                    val codeHunkPart = agentOperationEvent.event.value as CodeHunkPart
                    content = MessagePart.CodeHunk(
                        lineBasedDiff = codeHunkPart.line_based_diff,
                        refToolCallId = codeHunkPart.ref_tool_call_id
                    )
                }

                AgentOperationEvent.EVENT_SHELL_COMMAND_PART -> {
                    val shellCommandPart = agentOperationEvent.event.value as ShellCommandPart
                    content = MessagePart.ShellCommand(
                        id = uuid(),
                        command = shellCommandPart.command,
                        status = MessagePart.ShellCommand.CommandStatus.CONFIRMATION,
                        toolCallId = shellCommandPart.tool_call_id,
                        commandsRequiringConfirmation = shellCommandPart.commands_requiring_confirmation.map {
                            MessagePart.ShellCommand.CommandInfo(
                                name = it.name,
                                arguments = it.arguments
                            )
                        }
                    )
                    postProcessingData =
                        PostProcessingData.ShellCommandData(content, shellCommandPart.requires_confirmation)
                }

                AgentOperationEvent.EVENT_WARNING_ALERT -> {
                    val warningAlert = agentOperationEvent.event.value as WarningAlert
                    thisLogger().info("warning alert message received with ${warningAlert.title}\n${warningAlert.body}")
                    chatManager.sendWarningMessageToChat(warningAlert.title, warningAlert.body, chat, operationId)
                    return
                }

                AgentOperationEvent.EVENT_END_OPERATION -> {
                    thisLogger().info("endOfOperation message received")
                    val endOperationEvent = agentOperationEvent.event.value as EndOperation
                    if (endOperationEvent.error_message != null) {
                        val errorMessage = endOperationEvent.error_message
                        Sentry.captureException(Exception("Received error from agent for operation $operationId: $errorMessage"))
                        thisLogger().warn("Received error from agent for operation $operationId: $errorMessage")
                        if (errorMessage.lowercase().contains("invalid model settings")) {
                            chatManager.sendInvalidModelSettingsErrorToChat(
                                ZencoderBundle["chat.request.error.invalidModelsSettings"],
                                chat,
                                operationId
                            )
                        } else {
                            chatManager.sendErrorMessageToChat(errorMessage, chat, operationId)
                        }
                    } else {
                        track(
                            "Chat response generation completed",
                            "operation_id" to operationId,
                            *chat.lastMessageAssistantProperties(),
                            *chat.getChatAnalyticsProps()
                        )
                        try {
                            if (chat.isNameGenerated == false && chat.selfDestruct != true && chat.isRenamedByUser == false &&
                                chat.messages.size >= 2
                            ) {
                                val customInstructions = listOfNotNull(customInstructionState.customInstruction)
                                val title = requestService.generateChatName(operationId, chat, customInstructions)
                                chatManager.renameChatSession(chat.id, title, isNameGenerated = true)
                            } else if (chat.isNameGenerated == false) {
                                chatManager.renameChatSession(chat.id, chat.title, isNameGenerated = true)
                            }
                        } catch (e: Exception) {
                            Sentry.captureException(
                                Exception("Could not rename chat properly for operation $operationId: ${e.message}")
                            )
                            thisLogger().warn("Could not rename chat properly for $operationId: ${e.message}")
                        }
                    }
                    project.service<AgentCommunicationsService>().unregisterOperation(operationId)
                    try { // Chat post-processing
                        val finalizedChat = chatManager.getChatById(chatId, false)
                        chatManager.postProcessAgentChat(finalizedChat, modifyingMessageId)
                    } catch (e: Exception) {
                        thisLogger().warn("Could not post-process operation $operationId: ${e.message}")
                    }
                    return
                }
            }

            if (content == null) {
                thisLogger().debug("No content found for event: ${agentOperationEvent.event.key} for operation ID: $operationId")
                return
            }

            chatManager.appendMessagePartContent(
                chatId = chat.id,
                modifyingMessageId = modifyingMessageId,
                content = content,
                isPermanent = !isLoading
            )

            if (postProcessingData != null) {
                postProcessMessagePart(chat, postProcessingData, modifyingMessageId)
            }
        }
    }

    inner class ChunkedMessagesCollector {

        private val chuckedMessagesCache: Cache<String, Array<Message?>> = CacheBuilder.newBuilder()
            // To prevent memory leaks in case if backend sent wrong number of chunked messages or some network instability
            .expireAfterWrite(CLEAN_CHUNKED_MESSAGES_CACHE_IN_MINUTES, TimeUnit.MINUTES)
            .build()

        fun collectAndProcessIfLastChunk(message: Message, channel: Channel) {
            if (!message.hasChunkedEventId()) {
                "Expected to have chunked message ID in chunked messages".logAsExceptionQuietly()
                return
            }
            val chunkedMessageId = message.getChunkedEventId() as String
            val totalChunkNumber = message.getTotalChunkNumber()
            if (totalChunkNumber == null) {
                "Expected to have total chunk number in chunk message: $chunkedMessageId".logAsExceptionQuietly()
                return
            }
            val chunkIndex = message.getChunkIndex()
            if (chunkIndex == null) {
                "Expected to have chunk number in chunk message: $chunkedMessageId".logAsExceptionQuietly()
                return
            }
            if (message.data !is ByteArray) {
                "Expected ByteArray data in chunked message, but got ${message.data?.javaClass?.simpleName}".logAsExceptionQuietly()
                return
            }
            var existingArrayOfMessages = chuckedMessagesCache.getIfPresent(chunkedMessageId)
            if (existingArrayOfMessages == null) {
                thisLogger().info("Creating new chunk array for message ID: $chunkedMessageId with total chunks: $totalChunkNumber")
                existingArrayOfMessages = arrayOfNulls(totalChunkNumber)
                existingArrayOfMessages[chunkIndex] = message
                chuckedMessagesCache.put(chunkedMessageId, existingArrayOfMessages)
            } else {
                thisLogger().info("Adding chunk $chunkIndex of $totalChunkNumber for message ID: $chunkedMessageId")
                existingArrayOfMessages[chunkIndex] = message
            }

            val collectedCount = existingArrayOfMessages.count { it != null }
            thisLogger().info("Collected $collectedCount of $totalChunkNumber chunks for message ID: $chunkedMessageId")

            if (existingArrayOfMessages.all { it != null }) {
                thisLogger().info("All chunks collected for message ID: $chunkedMessageId. Total chunks: ${existingArrayOfMessages.size}")

                try {
                    val combinedData = combineChunkedMessages(existingArrayOfMessages)
                    thisLogger().info("Combined data size: ${combinedData.size} bytes")

                    val firstMessage = existingArrayOfMessages[0]!!
                    val combinedMessage = Message(
                        firstMessage.name,
                        combinedData
                    )

                    thisLogger().info("Processing combined message")
                    chuckedMessagesCache.invalidate(chunkedMessageId)
                    chatMessageProcessor.processMessage(combinedMessage, channel)
                } catch (e: Exception) {
                    "Error processing combined chunks for message ID: $chunkedMessageId".logAsExceptionQuietly()
                }
            }
        }

        private fun combineChunkedMessages(messages: Array<Message?>): ByteArray {
            val totalSize = messages.sumOf { message ->
                (message?.data as? ByteArray)?.size ?: 0
            }
            val result = ByteArray(totalSize)
            var position = 0
            messages.forEach { message ->
                val data = message?.data as? ByteArray
                if (data != null) {
                    System.arraycopy(data, 0, result, position, data.size)
                    position += data.size
                }
            }
            return result
        }

        private fun String.logAsExceptionQuietly() {
            val e = Exception(this)
            thisLogger().warn(e)
            Sentry.captureException(e)
        }
    }

    private fun postProcessMessagePart(
        chat: ChatModel,
        postProcessingData: PostProcessingData,
        modifyingMessageId: ProcessingMessageId
    ) {
        when (postProcessingData) {
            is PostProcessingData.ShellCommandData -> {
                if (!postProcessingData.requiresConfirmation) {
                    cs.launch {
                        ChatShellCommandHandler(
                            project = project,
                            chatId = chat.id,
                            messageId = modifyingMessageId,
                            messagePartId = postProcessingData.content.id
                        ).launch()
                    }
                }
            }
        }
    }

    sealed class PostProcessingData {
        data class ShellCommandData(
            val content: MessagePart.ShellCommand,
            val requiresConfirmation: Boolean
        ) : PostProcessingData()
    }

    private fun processUpdateContextEvent(
        updateContextPart: UpdateContextPart,
        chat: ChatModel,
        modifyingMessageId: ProcessingMessageId
    ) {
        val newContext = ChatMessageContext(
            usedFilePaths = updateContextPart.used_files?.files?.map { usedFile ->
                ChatAttachedFile(usedFile.path, project.resolve(usedFile.path).toString(), emptyList())
            },
            rerankedFiles = updateContextPart.reranked_files?.files?.map {
                ChatAttachedFile(it.path, project.resolve(it.path).toString(), emptyList())
            }
        )
        chatManager.updateMessageContext(chat, modifyingMessageId, newContext)
    }

    fun sendAbortOperationSignal(operationId: OperationId) {
        val channel = socketConnectionManager.getOperationSessionChannel(project)
        val chunkMessage = Message(
            "gql-abort",
            "",
            MessageExtras(
                JsonObject().apply {
                    add(
                        "headers",
                        JsonObject().apply {
                            addProperty("x-operation-id", operationId)
                        }
                    )
                }
            )
        )
        channel.publish(
            chunkMessage,
            object : CompletionListener {
                override fun onSuccess() {
                    thisLogger().info("Successfully sent abort operation $operationId to channel ${channel.name}")
                }

                override fun onError(reason: ErrorInfo?) {
                    thisLogger().error("Error sending abort for operation $operationId, reason: $reason")
                }
            }
        )
    }

    private fun splitResultByChunksAndSend(
        result: Result,
        messageId: String?,
        channel: Channel
    ) {
        val resultByteArray = objectMapper.writeValueAsString(result).toByteArray()
        val chunked = resultByteArray.asIterable().chunked(CHUNK_SIZE)
        chunked.forEachIndexed { index, chunk ->
            val chunkMessage = Message(
                "gql-result",
                chunk.toByteArray(),
                MessageExtras(
                    JsonObject().apply {
                        add(
                            "headers",
                            JsonObject().apply {
                                addProperty("x-gql-result-request-id", messageId)
                                addProperty("x-gql-result-is-error", false)
                                addProperty("x-gql-result-is-compressed", false)
                                if (chunked.size > 1) {
                                    addProperty("x-gql-result-chunk-index", index)
                                    addProperty("x-gql-result-chunks-total", chunked.size)
                                }
                            }
                        )
                    }
                )
            )
            channel.publish(
                chunkMessage,
                object : CompletionListener { // required to see the error if something happens
                    override fun onSuccess() {
                        thisLogger().info("Successfully published message to channel ${channel.name}")
                    }

                    override fun onError(reason: ErrorInfo?) {
                        thisLogger().error("Error publishing to channel $reason")
                    }
                }
            )
        }
    }

    private fun Message.getChunkedEventId(): String? {
        val extrasJson = this.extras?.asJsonObject() ?: return null
        val headers = extrasJson.getAsJsonObject("headers") ?: return null
        return headers[CHUNK_EVENT_ID_HEADER_NAME]?.asString
    }

    private fun Message.isCompressed(): Boolean {
        val extrasJson = this.extras.asJsonObject()
        val headers = extrasJson.getAsJsonObject("headers") ?: return false
        return headers[IS_COMPRESSED_HEADER_NAME]?.asBoolean ?: false
    }

    private fun Message.getTotalChunkNumber() = this.getNumericHeader(TOTAL_CHUNKS_HEADER_NAME)

    private fun Message.getChunkIndex() = this.getNumericHeader(CHUNK_INDEX_HEADER_NAME)

    private fun Message.getNumericHeader(headerName: String): Int? {
        val extrasJson = this.extras.asJsonObject()
        val headers = extrasJson.getAsJsonObject("headers")
        if (headers == null) {
            val exception = Exception("Expected to have header in message extras")
            Sentry.captureException(exception)
            thisLogger().error(exception)
            return null
        }
        return headers[headerName].asInt
    }

    private fun Message.hasChunkedEventId() = this.getChunkedEventId() != null

    @DoNotObfuscate
    data class Result(
        val errors: List<Any>? = null,
        val data: Any? = null
    )

    override fun dispose() {
        socketConnectionManager.getOperationSessionChannel(project).unsubscribe(agentGQLCommandsListener)
        socketConnectionManager.getOperationSessionChannel(project).unsubscribe(agentOperationsListener)
    }
}

private fun LoadingMarkerPart.toLoadingMarkerMessagePart(): MessagePart.LoadingMarker? {
    val steps = this.steps.map { LoadingStep(it.title, it.status.toLoadingStepStatus()) }
    return steps.takeIf { it.isNotEmpty() }?.let { MessagePart.LoadingMarker(it) }
}

private fun LoadingMarkerStepStatus.toLoadingStepStatus(): LoadingStepStatus = when (this) {
    LoadingMarkerStepStatus.IDLE -> LoadingStepStatus.IDLE
    LoadingMarkerStepStatus.ACTIVE -> LoadingStepStatus.ACTIVE
    LoadingMarkerStepStatus.FINISHED -> LoadingStepStatus.FINISHED
}

private fun Diff.toDiffModel(): MessagePart.CodePatch.ClientDiff {
    return MessagePart.CodePatch.ClientDiff(
        this.path,
        this.patch,
        this.is_new,
        this.is_applied,
        isUnread = !this.is_applied
    )
}

private fun ToolCallPart.toDomain(project: Project): MessagePart.ToolCall {
    return MessagePart.ToolCall(
        toolCallId = tool_call_id,
        title = title,
        status = status.toDomain(),
        statusText = status_text,
        context = context?.toDomain(project),
        isHidden = is_hidden,
        localResult = null
    )
}

private fun ToolCallStatus.toDomain(): MessagePart.ToolCall.ToolCallStatus = when (this) {
    ToolCallStatus.IN_PROGRESS -> MessagePart.ToolCall.ToolCallStatus.IN_PROGRESS
    ToolCallStatus.SUCCESS -> MessagePart.ToolCall.ToolCallStatus.SUCCESS
    ToolCallStatus.WARNING -> MessagePart.ToolCall.ToolCallStatus.WARNING
    ToolCallStatus.ERROR -> MessagePart.ToolCall.ToolCallStatus.ERROR
}

private fun ToolCallContext.toDomain(project: Project): MessagePart.ToolCall.ToolCallContext {
    return MessagePart.ToolCall.ToolCallContext(
        files = used_files?.files?.map {
            ChatAttachedFile(it.path, project.resolve(it.path).toString())
        } ?: emptyList()
    )
}

private fun GroupedChecklistPart.toMessagePart(): MessagePart.GroupedChecklist {
    return MessagePart.GroupedChecklist(
        id = uuid(),
        items = this.items.map { item ->
            MessagePart.GroupedChecklist.GroupedChecklistItem(item.group, item.text, item.is_selected)
        },
        newItemButtonText = this.new_item_button_text,
        newItemGroupName = this.new_item_group_name,
        submitButtonText = this.submit_button_text,
        newItemPlaceholder = this.new_item_placeholder,
        title = this.title,
        toolCallId = this.tool_call_id,
        disabled = this.disabled
    )
}

data class OperationContext(
    val agentName: String?,
    var processingMessageId: ProcessingMessageId?,
    var initialOperationIdeState: Context?
)
