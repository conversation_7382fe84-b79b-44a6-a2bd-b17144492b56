package ai.zencoder.plugin.ui.tools.components

import javax.swing.JComponent

/**
 * Base interface for all UI components.
 * Follows the Component pattern for consistent UI building.
 */
interface UIComponent {
    /**
     * Creates and returns the Swing component.
     * @return The created JComponent
     */
    fun createComponent(): JComponent
}

/**
 * Interface for components that can be updated with new data.
 * @param T The type of data used to update the component
 */
interface UpdatableComponent<T> : UIComponent {
    /**
     * Updates the component with new data.
     * @param data The data to update the component with
     */
    fun updateWithData(data: T)
}

/**
 * Interface for components that can handle events.
 * @param T The type of event handler
 */
interface InteractiveComponent<T> : UIComponent {
    /**
     * Sets the event handler for this component.
     * @param handler The event handler
     */
    fun setEventHandler(handler: T)
}
