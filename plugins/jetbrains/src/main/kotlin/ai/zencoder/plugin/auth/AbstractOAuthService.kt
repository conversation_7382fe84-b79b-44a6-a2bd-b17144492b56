@file:Suppress("DuplicatedCode")

package ai.zencoder.plugin.auth

import ai.zencoder.generated.client.all.infrastructure.isClientError
import ai.zencoder.plugin.config.PropertyLoader
import ai.zencoder.plugin.config.TokenStorage
import ai.zencoder.plugin.observability.AnalyticsService
import ai.zencoder.plugin.process.InterruptibleBackgroundTask
import ai.zencoder.plugin.utils.maskToken
import ai.zencoder.plugin.utils.showInformation
import ai.zencoder.plugin.utils.systemSsl
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.intellij.ide.browsers.BrowserLauncher
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.ui.Messages
import kotlinx.serialization.json.Json
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.awaitility.core.DurationFactory
import java.time.Duration
import java.util.concurrent.TimeUnit

abstract class AbstractOAuthService : AuthService {

    private val tokenStorage = getTokenStorage()
    private val propertyLoader = PropertyLoader.getInstance()
    protected val authOperationsTracker = service<AuthOperationsTrackerService>()
    protected abstract val providerTitle: String
    protected abstract val authorizationUrl: String
    protected abstract val signUpUrl: String?
    protected abstract val redirectUrlPath: String
    protected abstract val tokenExchangeUrl: String
    protected abstract val tokenRefreshUrl: String

//    todo remove - temporary hack on SPA
    protected abstract val clientRedirectUrl: String

    protected val usePKCE: Boolean = true // Can be overridden in implementing classes

    protected val json = Json {
        ignoreUnknownKeys = true // so unknown JSON fields don't break parsing
        encodeDefaults = true
    }

    private val refreshHttpTimeout = Duration.ofSeconds(5)

    override val accessToken: String
        get() = authInfoOrNull()?.accessToken ?: throw NoAuthInfoException("accessToken not found")

    protected val objectMapper = ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .registerModule(KotlinModule.Builder().build())

    protected abstract fun refreshRequestBuilderModifier(builder: Request.Builder)

    override fun authInfoOrNull(): AuthInfo? = tokenStorage.loadAuthInfo()

    @Synchronized
    override fun signIn() {
        authOperationsTracker.info("${Thread.currentThread().name} Initiating authentication for $providerTitle")
        initiateAuthentication(signUp = false)
    }

    override fun signUp() {
        if (signUpUrl == null) {
            throw UnsupportedOperationException("Sign up is not supported for $providerTitle")
        }
        authOperationsTracker.info("${Thread.currentThread().name} Initiating signup for $providerTitle")
        initiateAuthentication(signUp = true)
    }

    @Synchronized
    override fun refreshAuthentication(expiredAccessToken: String): AuthInfo {
        authOperationsTracker.info(
            "${Thread.currentThread().name} Refreshing authentication token for $providerTitle, expired access token ${maskToken(
                expiredAccessToken
            )}"
        )
        val refreshedValue = refreshToken(expiredAccessToken)
        return storeAuthInfo(refreshedValue)
    }

    private fun storeAuthInfo(authInfo: AuthInfo): AuthInfo {
        tokenStorage.storeAuthInfo(authInfo)
        authOperationsTracker.info(
            "${Thread.currentThread().name} Successfully stored the Auth Info for $providerTitle, accessToken=${maskToken(
                authInfo.accessToken
            )}, refreshToken=${maskToken(authInfo.refreshToken)}"
        )
        return authInfo
    }

    override fun isAuthenticated(): Boolean {
        return tokenStorage.loadAuthInfo() != null
    }

    @Synchronized
    override fun resetAuthentication() {
        authOperationsTracker.info("${Thread.currentThread().name} Resetting authentication for $providerTitle")
        tokenStorage.cleanAuthInfo()
        postLogoutActions()
    }

    private fun initiateAuthentication(signUp: Boolean) {
        var isCancelled = false
        var codeVerifier: String? = null

        val authenticationTask =
            object : InterruptibleBackgroundTask("Authenticating $providerTitle ...") {

                val authenticationListener = AuthorizationCodeFlowListener(
                    tokenExchangeUrl = tokenExchangeUrl,
                    urlPath = redirectUrlPath,
                    codeVerifier = if (usePKCE) generateCodeVerifier() else null,
                    provider = providerTitle.lowercase(),
                    clientRedirectUrl = clientRedirectUrl,
                    tokenExchangeResponseParser = ::parseTokenExchangeResponse
                )

                override fun process() = Runnable {
                    val redirectUri = authenticationListener.provideAuthenticationCallbackUrl()
                    val codeChallenge = if (usePKCE) generateCodeChallenge() else null
                    val authenticationUrl = if (signUp) {
                        generateSignUpUrl(redirectUri = redirectUri, codeChallenge = codeChallenge)
                    } else {
                        generateLoginUrl(redirectUri = redirectUri, codeChallenge = codeChallenge)
                    }
                    BrowserLauncher.instance.open(authenticationUrl)
                }

                private fun generateCodeVerifier(): String {
                    codeVerifier = PKCEUtil.generateCodeVerifier()
                    return codeVerifier
                }

                private fun generateCodeChallenge(): String {
                    return PKCEUtil.generateCodeChallenge(codeVerifier!!)
                }

                override fun timeoutDuration(): Duration = DurationFactory.of(
                    propertyLoader.getTimeoutForLoginInSeconds().toLong(),
                    TimeUnit.SECONDS
                )

                override fun interruptionCondition(): Boolean = isCancelled

                override fun successCondition(): Boolean = authenticationListener.checkIfAuthDataArrived()

                override fun onTimeout() {
                    authOperationsTracker.warn("Authentication timeout exceeded")
                    if (!isCancelled) {
                        invokeLater {
                            Messages.showErrorDialog(
                                "Timeout exceeded, you have only 5 minutes to authorize on the $providerTitle page",
                                "Authentication Error"
                            )
                        }
                    }
                    invokeLater { cancel() }
                }

                override fun onInterrupted() = invokeLater { cancel() }

                override fun complete() {
                    val result = authenticationListener.getAuthInfo() as AuthInfo
                    authOperationsTracker.info(" $providerTitle authentication complete")
                    showInformation("$providerTitle authenticated successfully")
                    storeAuthInfo(result)
                    postLoginActions(result)
                }

                private fun cancel() {
                    authOperationsTracker.warn("Authentication cancelled")
                    isCancelled = true
                    authenticationListener.stop()
                }
            }

        authenticationTask.queue()
    }

    protected open fun postLoginActions(result: AuthInfo) {
    }

    protected open fun postLogoutActions() {
    }

    private fun refreshToken(expiredAccessToken: String): AuthInfo {
        authOperationsTracker.info(
            "${Thread.currentThread().name} Refreshing access token with expired access token ${maskToken(
                expiredAccessToken
            )} for $providerTitle" +
                " with refresh token ${maskToken(tokenStorage.loadAuthInfo()?.refreshToken)}"
        )

        val client = OkHttpClient().newBuilder()
            .systemSsl()
            .readTimeout(refreshHttpTimeout)
            .connectTimeout(refreshHttpTimeout)
            .build()

        val payload = createRefreshTokenRequest(
            refreshToken = tokenStorage.loadAuthInfo()?.refreshToken!!,
            providerType = providerTitle.lowercase()
        )

        val requestBuilder = Request.Builder()
        requestBuilder.url(tokenRefreshUrl)
        requestBuilder.header("Content-Type", "application/json")
        requestBuilder.header("zencoder-anonymous-id", service<AnalyticsService>().anonymousId)
        refreshRequestBuilderModifier(requestBuilder)
        requestBuilder.post(serializeRefreshTokenRequest(payload).toRequestBody())
        val request = requestBuilder.build()

        client.newCall(request).execute().use { response ->

            if (response.code == 200) {
                val responseBody = response.body?.string()!!
                val parsedResponse = parseRefreshTokenResponse(responseBody)
                authOperationsTracker.info(
                    "${Thread.currentThread().name} Successfully refreshed token for $providerTitle, new access token ${maskToken(
                        parsedResponse.accessToken
                    )}"
                )
                return (
                    AuthInfo(
                        accessToken = parsedResponse.accessToken,
                        refreshToken = parsedResponse.refreshToken
                    )
                    )
            } else {
                authOperationsTracker.warn(
                    "${Thread.currentThread().name} Could not refresh token, status code: ${response.code}, response: ${response.body?.string()}"
                )
                val storedAuthInfo = tokenStorage.loadAuthInfo()
                if (storedAuthInfo != null && storedAuthInfo.accessToken != expiredAccessToken) {
                    authOperationsTracker.info("${Thread.currentThread().name}: Already refreshed by another ide")
                    return storedAuthInfo
                } else if (response.isClientError) {
                    throw NoAuthInfoException("Could not refresh token")
                } else {
                    throw TemporaryNoAuthInfoException()
                }
            }
        }
    }

    protected abstract fun getTokenStorage(): TokenStorage

    protected abstract fun generateLoginUrl(redirectUri: String, codeChallenge: String? = null): String

    protected abstract fun generateSignUpUrl(redirectUri: String, codeChallenge: String? = null): String

    protected abstract fun createRefreshTokenRequest(
        refreshToken: String,
        grantType: String? = "refresh_token",
        providerType: String
    ): OAuthRefreshTokenRequest

    protected abstract fun parseRefreshTokenResponse(responseBody: String): OAuthRefreshTokenResponse

    protected abstract fun serializeRefreshTokenRequest(request: OAuthRefreshTokenRequest): String

    protected abstract fun parseTokenExchangeResponse(responseBody: String): OAuthTokenExchangeResponse
}

private fun invokeLater(job: Runnable) {
    ApplicationManager.getApplication().invokeLater(job)
}

interface OAuthRefreshTokenRequest {
    val refreshToken: String
    val grantType: String
}

interface OAuthRefreshTokenResponse {
    val accessToken: String
    val refreshToken: String
}

interface OAuthTokenExchangeResponse {
    fun accessToken(): String
    fun refreshToken(): String
}
