package ai.zencoder.plugin.providers.completion.nextchange

import ai.zencoder.generated.client.completion.models.EditPredictionResponse
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.providers.completion.nextchange.ZencoderKeys.NEXT_EDIT_REQUEST_ID
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.uuid
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

@Service(Service.Level.PROJECT)
class NextEditPredictionHandler {
    private val editPredictionService = service<NextEditPredictionService>()
    private val isNesChainActive = AtomicBoolean(false)
    private val currentRequestId = AtomicReference<String?>(null)

    // Map to store document text at the time of request
    private val documentTextAtRequest = ConcurrentHashMap<String, String>()

    fun onCompletionAccepted(editor: Editor) {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        thisLogger().info("Completion accepted, triggering NES")
        isNesChainActive.set(true)
        showNextEditPrediction(editor)
    }

    fun onCompletionRejected(editor: Editor) {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        thisLogger().info("Completion rejected, triggering NES")
        isNesChainActive.set(true)
        showNextEditPrediction(editor)
    }

    fun onCompletionAbsent(editor: Editor) {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        thisLogger().info("No completion available, triggering NES")
        isNesChainActive.set(true)
        showNextEditPrediction(editor)
    }

    fun onNesAccepted(editor: Editor) {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        thisLogger().info("NES accepted, continuing NES chain")
        isNesChainActive.set(true)
        showNextEditPrediction(editor)
    }

    fun onNesRejected() {
        if (!zencoderFeatureFlags.enableNextEditPredictions || !service<ZencoderSettings>().enableNextEditPrediction) {
            return
        }
        thisLogger().info("NES rejected, stopping NES chain")
        isNesChainActive.set(false)
    }

    private fun showNextEditPrediction(editor: Editor) {
        if (!isNesChainActive.get()) {
            thisLogger().info("NES is already inactive")
            return
        }

        val requestId = uuid()
        currentRequestId.set(requestId)
        thisLogger().info("Starting new NES request with ID: $requestId")

        val currentText = editor.document.text
        documentTextAtRequest[requestId] = currentText

        editPredictionService.requestNextEditPrediction(editor, requestId) { response ->
            val textAtRequest = documentTextAtRequest[requestId]
            val currentTextAtResponse = editor.document.text

            if (requestId == currentRequestId.get() && textAtRequest == currentTextAtResponse) {
                thisLogger().info("Handling NES response for request ID: $requestId")
                handleNextEditPredictionResponse(editor, requestId, response)
            } else {
                thisLogger().info("Document changed or request outdated, not showing NES for request ID: $requestId")
            }

            documentTextAtRequest.remove(requestId)
        }
    }

    private fun handleNextEditPredictionResponse(
        editor: Editor,
        requestId: String,
        response: EditPredictionResponse?
    ) {
        if (response != null) {
            editor.putUserData(NEXT_EDIT_REQUEST_ID, requestId)
            CodeSuggestionDiffViewer.displayInlineDiff(
                editor,
                response.originalStr,
                response.modifiedStr
            )
        }
    }
}
