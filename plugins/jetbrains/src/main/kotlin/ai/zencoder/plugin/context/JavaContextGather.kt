package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.JavaNodeResolver
import com.intellij.lang.Language
import com.intellij.lang.java.JavaLanguage
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.*
import com.intellij.psi.util.*
import org.jetbrains.plugins.groovy.intentions.style.inference.resolve

@Suppress("DuplicatedCode")
class JavaContextGather : ContextGather {

    private val resolver = JavaNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean {
        return language.isKindOf(JavaLanguage.INSTANCE)
    }

    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? = when (psiElement) {
        is PsiMethod -> {
            val name = runReadAction { psiElement.name }
            val docString = if (extractDocComment) {
                runReadAction { psiElement.docComment?.text }
            } else {
                null
            }
            ContextSymbolHeader(name = name, kind = NodeType.Method, docstring = docString)
        }

        is PsiClass -> {
            val name = runReadAction { psiElement.name }
            val docString = if (extractDocComment) {
                runReadAction { psiElement.docComment?.text }
            } else {
                null
            }
            ContextSymbolHeader(name = name ?: "", kind = NodeType.Class, docstring = docString)
        }
        else -> null
    }

    override fun readImportsSection(psiFile: PsiFile): String? = PsiTreeUtil.findChildOfType(psiFile, PsiImportList::class.java)?.text

    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        val importSignatures = mutableSetOf<ImportSignature>()
        val psiFile = runReadAction { element.containingFile }
            ?: return emptyList()
        for (importElement in runReadAction {
            PsiTreeUtil.findChildOfType(psiFile, PsiImportList::class.java)?.importStatements.orEmpty()
        }) {
            val reference = runReadAction { importElement.importReference }
            runReadAction {
                resolver.toResolvedASTNode(reference?.resolve())?.let {
                    importSignatures.add(
                        ImportSignature(importElement.text, it)
                    )
                }
            }
        }
        return importSignatures.toList()
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? PsiMethod ?: runReadAction { PsiTreeUtil.getParentOfType(element, PsiMethod::class.java) }
        val classElement = element as? PsiClass ?: runReadAction { PsiTreeUtil.getParentOfType(element, PsiClass::class.java) }
        val methodTypes = runReadAction { PsiTreeUtil.collectElementsOfType(methodElement, PsiTypeElement::class.java).map { it.type } }
        val fieldTypes = runReadAction { PsiTreeUtil.collectElementsOfType(classElement, PsiField::class.java).map { it.type } }

        val resolved = (methodTypes + fieldTypes).asSequence().map {
            runReadAction { it.resolve() }
        }.filterNotNull()
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.name) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }.toList()
        return resolved
    }
}
