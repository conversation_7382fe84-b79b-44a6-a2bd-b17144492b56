package ai.zencoder.plugin.ui.tools.services

import ai.zencoder.plugin.mcp.MCPLibraryManager
import ai.zencoder.plugin.mcp.McpServerInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Service for loading tools from the MCP library.
 * Follows the Single Responsibility Principle by focusing only on loading tools.
 */
interface ToolsLoadService {
    /**
     * Loads tools from the MCP library.
     *
     * @param onSuccess Callback for successful loading
     * @param onError Callback for loading errors
     */
    fun loadTools(onSuccess: (Map<String, McpServerInfo>) -> Unit, onError: (String) -> Unit)
}

/**
 * Default implementation of ToolsLoadService.
 */
class DefaultToolsLoadService(
    private val libraryManager: MCPLibraryManager,
    private val coroutineScope: CoroutineScope
) : ToolsLoadService {

    override fun loadTools(onSuccess: (Map<String, McpServerInfo>) -> Unit, onError: (String) -> Unit) {
        coroutineScope.launch {
            try {
                val toolsState = libraryManager.getAgentToolsState()
                onSuccess(toolsState)
            } catch (e: Exception) {
                onError(e.message.toString())
            }
        }
    }
}
