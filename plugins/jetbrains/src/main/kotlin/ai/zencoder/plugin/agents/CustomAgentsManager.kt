package ai.zencoder.plugin.agents

import ai.zencoder.plugin.auth.AuthInfo
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.auth.userId
import ai.zencoder.plugin.mcp.InternalToolsRegistry
import ai.zencoder.plugin.mcp.service.MCP_MESSAGE_BUS_SERVER_LISTENER
import ai.zencoder.plugin.mcp.service.McpMessageBusServerListener
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.observers.auth.AUTH_LISTENER
import ai.zencoder.plugin.observers.auth.AuthListener
import ai.zencoder.plugin.services.ZencoderPluginDisposable
import ai.zencoder.plugin.utils.showWarning
import ai.zencoder.plugin.utils.with
import ai.zencoder.plugin.utils.without
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.*
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.getOpenedProjects
import com.intellij.util.application
import com.intellij.util.xmlb.annotations.OptionTag
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.SerializationException
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

private val REFRESH_INTERVAL = 5.minutes
private val RETRY_INTERVAL = 30.seconds

@Service(Level.APP)
@State(name = "ai.zencoder.plugin.agents.cache", storages = [Storage("zencoder-agents-cache.xml")])
class CustomAgentsManager(private val cs: CoroutineScope) :
    SimplePersistentStateComponent<CustomAgentsCache>(CustomAgentsCache()),
    Disposable {

    private var cache
        get() = state.agents
        set(value) {
            state.agents = value
        }
    private val actionRegistrar get() = service<CustomAgentsActionRegistrar>()
    private val agentsService by lazy { service<AgentsService>() }
    private val refreshJob: Job
    private val mutex by lazy { Mutex() }

    init {
        val curUserId = service<AuthService>().userId
        if (state.userId != curUserId) {
            state.userId = curUserId
            cache = emptyMap()
        }
        cache.values.forEach(actionRegistrar::registerSafe)

        refreshJob = getRefreshJob()
        application.messageBus.connect(service<ZencoderPluginDisposable>())
            .subscribe(AUTH_LISTENER, AgentsAuthListener())
        application.messageBus.connect(service<ZencoderPluginDisposable>())
            .subscribe(
                MCP_MESSAGE_BUS_SERVER_LISTENER,
                McpMessageBusServerListener {
                    cs.launch {
                        refreshJob.cancelAndJoin()
                        refreshJob.start()
                    }
                }
            )
    }

    fun load() {
        thisLogger().info("Custom agents manager has been loaded")
        // no additional work, just to trigger constructor
    }

    fun findBy(id: AgentId): Agent? {
        return cache[id]
    }

    fun getAll(): List<Agent> {
        return cache.values.toList()
    }

    fun save(newAgent: Agent): Result<Agent> = runBlocking {
        try {
            val id = newAgent.id.takeUnless { it.isBlank() }
            val request = requestOf(newAgent)

            val response = try {
                id?.let { agentsService.update(it, request) }
                    ?: agentsService.create(request)
            } catch (e: Exception) {
                val msg = "Failed to save custom agent ${newAgent.name}"
                thisLogger().warn(msg, e)
                showWarning(msg)
                return@runBlocking Result.failure(e)
            }

            val agent = response.toDomain()
            mutex.withLock { cache = cache.with(agent.id, agent) }
            actionRegistrar.registerSafe(agent)

            if (id == null) {
                track(
                    "Custom agent created",
                    "agent_id" to agent.id,
                    "agent_code_lens" to agent.codeLens,
                    "agent_instruction_length" to agent.command.length
                )
            } else {
                track(
                    "Custom agent edited",
                    "agent_id" to agent.id,
                    "agent_code_lens" to agent.codeLens,
                    "agent_instruction_length" to agent.command.length
                )
            }

            Result.success(agent)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    fun remove(id: AgentId): Result<Unit> = runBlocking {
        try {
            val agent = findBy(id)
            try {
                agentsService.remove(id)
            } catch (e: Exception) {
                val msg = "Failed to remove custom agent ${agent?.name}"
                thisLogger().warn(msg, e)
                showWarning(msg)
                return@runBlocking Result.failure(e)
            }

            mutex.withLock { cache = cache.without(id) }
            actionRegistrar.unregister(id)

            agent?.let {
                track(
                    "Custom agent deleted",
                    "agent_id" to it.id,
                    "agent_code_lens" to it.codeLens,
                    "agent_instruction_length" to it.command.length
                )
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun getRefreshJob() = cs.launch {
        while (isActive) {
            try {
                refreshAgents()
                delay(REFRESH_INTERVAL)
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                thisLogger().warn("Error refreshing agents: ${e.message}")
                delay(RETRY_INTERVAL)
            }
        }
    }

    private suspend fun refreshAgents() {
        thisLogger().info("Refreshing custom agents")

        val agents = fetchCustomAgents() ?: return
        thisLogger().debug("Fetched custom agents: ${agents.keys}")

        val (added, removed) = mutex.withLock {
            val addedAgents = agents.filter { (key, _) -> !cache.containsKey(key) }
            val removedAgentIds = cache.keys.filter { !agents.containsKey(it) }
            cache = agents
            Pair(addedAgents, removedAgentIds)
        }

        added.values.forEach(actionRegistrar::registerSafe)
        removed.forEach(actionRegistrar::unregister)
    }

    private suspend fun fetchCustomAgents(): Map<AgentId, Agent>? {
        val agents = try {
            agentsService.getAll().content
        } catch (e: Exception) {
            thisLogger().warn("Failed to fetch custom agents: ${e.message}")
            return null
        }

        val agentsMap = agents.mapNotNull {
            try {
                it.toDomain()
            } catch (e: SerializationException) {
                thisLogger().warn("Failed to deserialize agent ${it.id}: ${e.message}")
                null
            }
        }.associateBy { it.id }

        val project = getOpenedProjects().firstOrNull() ?: return agentsMap
        return agentsMap.let { originalAgents ->
            // Some old agents have incorrect internal mcp server id as names, correct them
            val internalMcpServersNameToIdMap = project.service<InternalToolsRegistry>()
                .getRegisteredTools()
                .entries
                .associate { (_, server) -> server.name to server.id }

            originalAgents.mapValues { (_, agent) ->
                agent.copy(
                    tools = agent.tools?.map { tool ->
                        tool.copy(id = internalMcpServersNameToIdMap[tool.id] ?: tool.id)
                    }
                )
            }
        }
    }

    override fun dispose() {
        refreshJob.cancel()
    }

    private inner class AgentsAuthListener : AuthListener {
        override fun signIn(authInfo: AuthInfo) {
            cs.launch {
                try {
                    val userId = authInfo.userData.id
                    val agentsData = fetchCustomAgents() ?: emptyMap()

                    mutex.withLock {
                        state.userId = userId
                        cache = agentsData
                    }

                    agentsData.values.forEach(actionRegistrar::registerSafe)
                } catch (e: Exception) {
                    thisLogger().warn("Error during sign in: ${e.message}")
                }
            }
        }

        override fun signOut() {
            cs.launch {
                try {
                    val currentAgents = mutex.withLock {
                        val currentKeys = cache.keys.toList()
                        state.userId = null
                        cache = emptyMap()
                        currentKeys
                    }

                    currentAgents.forEach(actionRegistrar::unregister)
                } catch (e: Exception) {
                    thisLogger().warn("Error during sign out: ${e.message}")
                }
            }
        }
    }
}

class CustomAgentsCache : BaseState() {
    @Volatile
    var userId: String? = null

    @Volatile
    @OptionTag(converter = AgentsMapConverter::class)
    var agents = emptyMap<AgentId, Agent>()
}
