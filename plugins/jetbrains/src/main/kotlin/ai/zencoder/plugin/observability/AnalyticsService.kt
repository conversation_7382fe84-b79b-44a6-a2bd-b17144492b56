package ai.zencoder.plugin.observability

import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.observers.auth.AUTH_LISTENER
import ai.zencoder.plugin.observers.auth.AuthListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.ZencoderApp
import ai.zencoder.plugin.utils.toJsonElement
import com.intellij.ide.ui.LafManager
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationNamesInfo
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.util.SystemInfo
import com.intellij.util.application
import com.rudderstack.sdk.java.analytics.RudderAnalytics
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.Locale

typealias AnalyticsProps = Array<Pair<String, *>>

fun track(event: String, vararg properties: Pair<String, Any?>) = track(event, properties.toMap())

fun track(event: String, properties: Map<String, Any?>) = service<AnalyticsService>().track(event, properties)

// todo: move to properties
private const val WRITE_KEY = "2nqPuUbn4AUvOiy18alIYzip4Wo"
private const val DATA_PLANE_URL = "https://forgooditjxygd.dataplane.rudderstack.com"
private const val FOR_GOOD_AI_EMAIL_SUFFIX = "@forgood.ai"

@Suppress("UnstableApiUsage")
@Service(Level.APP)
class AnalyticsService : Disposable {
    private val json = Json {
        ignoreUnknownKeys = true
        explicitNulls = false
        prettyPrint = true
    }

    private val client = RudderAnalytics.builder(WRITE_KEY)
        .setDataPlaneUrl(DATA_PLANE_URL)
        .build()

    private val authService get() = service<AuthService>()
    private val analyticsState get() = service<AnalyticsStateManager>()
    private val settings get() = service<ZencoderSettings>()

    private val userId get() = authService.userDataOrNull()?.id
    val anonymousId get() = analyticsState.anonymousId

    init {
        initialIdentify()
        if (authService.isAuthenticated()) {
            identifyUser()
        }

        application.messageBus.connect().subscribe(
            AUTH_LISTENER,
            AuthListener { identifyUser() }
        )
    }

    val isEnabled
        get() = ZencoderApp.isProduction || settings.isAnalyticsEnabled
    val isDisabled get() = !isEnabled

    val webviewOptions
        get() = AnalyticsWebviewOptions(
            writeKey = WRITE_KEY,
            dataPlaneURL = DATA_PLANE_URL,
            userId = userId,
            anonymousId = anonymousId,
            isEnabled = isEnabled
        )

    val user
        get() = User(
            userId = userId,
            anonymousId = anonymousId,
            properties = mapOf(
                "zen_platform" to "jetbrains",
                "zen_version" to ZencoderApp.plugin.version,
                "zen_os" to SystemInfo.OS_NAME,
                "zen_language" to Locale.getDefault().language,
                "zen_theme" to LafManager.getInstance().currentUIThemeLookAndFeel.name,
                "ide_name" to ApplicationNamesInfo.getInstance().fullProductNameWithEdition,
                "ide_version" to ApplicationInfo.getInstance().fullVersion
            )
        )

    fun track(event: String, properties: Map<String, Any?>) {
        if (isDisabled) return

        val jsonPayload = properties.takeIf { it.isNotEmpty() }
            ?.let { "\n" + json.encodeToString(it.toJsonElement()) }
            ?: ""
        thisLogger().info("Track event: $event$jsonPayload")

        client.track(event) {
            // always ensure the same device ID
            anonymousId(anonymousId)

            if (userId != null) {
                userId(userId)
            }
            properties(properties.filterValues { it != null })
        }
    }

    private fun initialIdentify() {
        if (isDisabled) return

        client.identify {
            anonymousId(anonymousId)
            traits(user.properties)
        }
    }

    private fun identifyUser() {
        if (isDisabled) return

        if (userId != null) {
            client.identify {
                userId(userId)
                anonymousId(anonymousId)
            }
        }

        val userData = authService.userDataOrNull()
        val userEmail = userData?.email
        val zenIsInternalUser = userEmail?.endsWith(FOR_GOOD_AI_EMAIL_SUFFIX) ?: false

        client.identify {
            anonymousId(anonymousId)
            traits(
                mapOf(
                    "zen_is_internal_user" to zenIsInternalUser,
                    "zencoder_plan" to userData?.plan?.lowercase(),
                    "zencoder_trial" to userData?.isOnTrial
                ).filterValues { it != null }
            )
        }
    }

    override fun dispose() {
        client.flush()
        client.shutdown()
    }
}

@Serializable
data class AnalyticsWebviewOptions(
    val writeKey: String,
    val dataPlaneURL: String,
    val anonymousId: String,
    val userId: String?,
    val isEnabled: Boolean // true for production, false for development
)

data class User(
    val userId: String?,
    val anonymousId: String,
    val properties: Map<String, Any?>
)
