package ai.zencoder.plugin.auth

import com.intellij.openapi.components.service
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route

abstract class AbstractRefreshAuthenticator : Authenticator {

    private val authOperationsTracker = service<AuthOperationsTrackerService>()

    override fun authenticate(route: Route?, response: Response): Request? {
        if (response.responseCount >= 3) {
            authOperationsTracker.warn("${Thread.currentThread().name}: Too many authentication attempts. Giving up.")
            handleAuthRefreshError()
            return null
        }

        val originalRequest = response.request
        val originalToken = originalRequest.header("Authorization")?.substringAfter("Bearer ")?.trim()

        authOperationsTracker.info(
            "${Thread.currentThread().name}:  Refreshing for: ${originalRequest.url} Added to the refresh process queue"
        )

        synchronized(this) {
            authOperationsTracker.info(
                "${Thread.currentThread().name}: Refreshing for: ${originalRequest.url} , no ongoing refreshes, starting " +
                    "Original token is null: ${originalToken == null}"
            )

            try {
                val authService = getAuthService()
                if (originalToken != authService.accessToken) {
                    authOperationsTracker.info("${Thread.currentThread().name}: Authentication was refreshed in another thread")

                    return originalRequest.newBuilder()
                        .header("Authorization", "Bearer ${authService.accessToken}")
                        .build()
                } else {
                    authOperationsTracker.info("${Thread.currentThread().name}: Expired token, will try to refresh")
                    val newAccessToken = authService.refreshAuthentication(authService.accessToken).accessToken

                    // Retry the request with the new token
                    return originalRequest.newBuilder()
                        .header("Authorization", "Bearer $newAccessToken")
                        .build()
                }
            } catch (e: NoAuthInfoException) {
                authOperationsTracker.warn("${Thread.currentThread().name}:  Refresh operation failed:", e)
                handleAuthRefreshError()
                return null
            } catch (e: Exception) {
                authOperationsTracker.warn("${Thread.currentThread().name}:  Refresh operation unavailable:", e)
                return null
            }
        }
    }

    protected abstract fun getAuthService(): AuthService

    protected abstract fun handleAuthRefreshError()
}

val Response.responseCount: Int
    get() = generateSequence(this) { it.priorResponse }.count { !it.isRedirect }
