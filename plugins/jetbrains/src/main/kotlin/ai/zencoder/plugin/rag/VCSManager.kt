package ai.zencoder.plugin.rag

import ai.zencoder.generated.client.repoUpload.infrastructure.ClientException
import ai.zencoder.plugin.api.RagUploadApiService
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.auth.NoAuthInfoException
import ai.zencoder.plugin.rag.vcs.FileSystemVCSService
import ai.zencoder.plugin.rag.vcs.GitVCSService
import ai.zencoder.plugin.rag.vcs.VCSService
import ai.zencoder.plugin.utils.GIT_DIR
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import io.sentry.kotlin.SentryContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.nio.file.Path
import java.nio.file.Paths
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.io.path.exists
import kotlin.math.pow

@Service(Level.PROJECT)
class VCSManager(
    private val project: Project,
    private val cs: CoroutineScope
) {
    private val logger = thisLogger()

    lateinit var projectPath: Path
    lateinit var vcsService: VCSService
    private val ragUploader get() = project.service<RepoRagUploader>()
    private val repoUploadService = service<RagUploadApiService>()
    private val ragSettingsManager get() = project.service<RagSettingsManager>()
    private val isActive = AtomicBoolean(true)

    init {
        val basePath = project.basePath
        if (basePath == null) {
            thisLogger().warn("Can't get base path of current project")
        } else {
            projectPath = Paths.get(basePath)

            val gitPath = projectPath.resolve(GIT_DIR)
            val isGit = runReadAction { gitPath.exists() }

            vcsService = if (isGit) {
                GitVCSService(project)
            } else {
                FileSystemVCSService(project)
            }
        }
    }

    fun startWatching() {
        cs.launch(Dispatchers.IO + SentryContext()) {
            var inited = false
            var iteration = 1
            while (!inited) {
                val delayMs = 10_000L * (2.toDouble().pow(iteration++)).toLong()
                if (!service<AuthService>().isAuthenticated()) {
                    logger.debug("User is not authenticated, skipping settings fetch, ${delayMs / 1000}s waiting for authentication...")
                    delay(delayMs)
                } else {
                    try {
                        val settingsResponse = repoUploadService.settings()
                        ragSettingsManager.loadState(settingsResponse.asState())
                        inited = true
                    } catch (e: NoAuthInfoException) {
                        thisLogger().warn(e.message, e)
                    } catch (e: ClientException) {
                        thisLogger().warn(e.message, e)
                    }
                }
            }
        }
        cs.launch(Dispatchers.IO + SentryContext()) {
            // start watching files and initial scan
            vcsService.watch {
                logger.info("Preforming initial scan")
                ragUploader.upload()
            }

            while (isActive.get()) {
                // check every REPO_INDEXING_INTERVAL for new changes, gathered by watcher
                delay(ragSettingsManager.state.repoIndexingIntervalMs)
                // All check are done inside `upload` function
                logger.info("Preforming periodic scan, interval=${ragSettingsManager.state.repoIndexingIntervalMs}ms")
                ragUploader.upload()
            }
        }
    }
}
