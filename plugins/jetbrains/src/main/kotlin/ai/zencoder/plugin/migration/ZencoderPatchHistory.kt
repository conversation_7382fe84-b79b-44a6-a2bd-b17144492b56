package ai.zencoder.plugin.migration

import ai.zencoder.plugin.migration.patches.*
import ai.zencoder.plugin.migration.patches.ChatStorageMigrationPatch
import ai.zencoder.plugin.utils.isMarkedAsRunOnce
import com.intellij.openapi.project.Project

val APPLICATION_PATCHES = listOf(
    AddCustomAgentCommandName,
    AddReviewCustomAgent,
    MigrateCustomAgentsToBackend
)
val APPLICATION_PATCHES_IDS = APPLICATION_PATCHES.map { it.id }
val applicationAppliedPatchesIds get() = APPLICATION_PATCHES_IDS.filter { isMarkedAsRunOnce(it) }

val PROJECT_PATCHES = listOf(
    MigrateCustomInstruction,
    GenerateChatMessageId,
    AddChatMessageRawContent,
    ChatStorageMigrationPatch
)
val PROJECT_PATCHES_IDS = PROJECT_PATCHES.map { it.id }
val Project.appliedPatchesIds get() = PROJECT_PATCHES_IDS.filter { this.isMarkedAsRunOnce(it) }
