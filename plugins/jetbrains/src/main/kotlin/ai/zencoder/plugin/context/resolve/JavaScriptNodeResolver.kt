package ai.zencoder.plugin.context.resolve

import ai.zencoder.plugin.context.SimpleASTNode
import com.intellij.lang.ecmascript6.psi.ES6ImportDeclaration
import com.intellij.lang.javascript.presentable.JSFormatUtil
import com.intellij.lang.javascript.psi.JSField
import com.intellij.lang.javascript.psi.JSFile
import com.intellij.lang.javascript.psi.JSFunction
import com.intellij.lang.javascript.psi.JSReferenceExpression
import com.intellij.lang.javascript.psi.ecmal4.JSClass
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.PsiElement
import com.intellij.psi.util.PsiFormatUtilBase
import kotlin.reflect.KClass

class JavaScriptNodeResolver : ASTNodeResolver<JSClass, JSFunction, JSField, JSFile> {
    override fun classElement(): KClass<JSClass> = JSClass::class

    override fun functionElement(): KClass<JSFunction> = JSFunction::class

    override fun fieldElement(): KClass<JSField> = JSField::class
    override fun fileElement(): KClass<JSFile> = JSFile::class

    // todo: javascript doesn't have single import section
    override fun importsElement(): KClass<out PsiElement> = ES6ImportDeclaration::class

    override fun extractClassSignature(element: JSClass): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val classLine = runReadAction { "${element.isClassOrInterface.name.lowercase()} ${element.name}" }
        return SimpleASTNode.forClassHeader(
            psiElement = element,
            className = element.name,
            signature = classLine
        )
    }

    override fun extractMethodSignature(element: JSFunction): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val options =
            PsiFormatUtilBase.SHOW_NAME or PsiFormatUtilBase.SHOW_PARAMETERS or PsiFormatUtilBase.SHOW_TYPE or PsiFormatUtilBase.TYPE_AFTER
        val formattedMethod = JSFormatUtil.formatMethod(element, options, options)
        return SimpleASTNode.forMethod(element, element.name, formattedMethod)
    }

    override fun extractFieldSignature(element: JSField): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val options = PsiFormatUtilBase.SHOW_NAME or PsiFormatUtilBase.SHOW_TYPE or PsiFormatUtilBase.TYPE_AFTER
        val formattedField = JSFormatUtil.formatField(element, options)
        return SimpleASTNode.forProperty(element, element.name, formattedField)
    }

    fun resolve(element: PsiElement): List<PsiElement> {
        return when (element) {
            is ES6ImportDeclaration -> element.importSpecifiers.mapNotNull { it.resolve() }
            is JSReferenceExpression -> listOfNotNull(element.resolve())
            else -> emptyList()
        }
    }
}
