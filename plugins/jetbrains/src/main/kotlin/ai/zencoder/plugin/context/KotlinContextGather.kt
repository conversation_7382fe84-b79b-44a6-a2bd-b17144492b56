@file:Suppress("DuplicatedCode")

package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.KotlinNodeResolver
import com.intellij.lang.Language
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.*
import com.intellij.psi.util.PsiTreeUtil
import org.jetbrains.kotlin.idea.KotlinLanguage
import org.jetbrains.kotlin.idea.references.mainReference
import org.jetbrains.kotlin.psi.*
import org.jetbrains.kotlin.psi.psiUtil.referenceExpression

class KotlinContextGather : ContextGather {

    private val resolver = KotlinNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean = language.id == KotlinLanguage.INSTANCE.id

    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? {
        when (psiElement) {
            is KtFunction -> {
                val name = ApplicationManager.getApplication().runReadAction<String> { psiElement.name }
                val docString = if (extractDocComment) {
                    runReadAction { psiElement.docComment?.text }
                } else {
                    null
                }
                return ContextSymbolHeader(
                    name = name,
                    kind = NodeType.Method,
                    docstring = docString
                )
            }

            is KtClass -> {
                val name = ApplicationManager.getApplication().runReadAction<String> { psiElement.name }
                val docString = if (extractDocComment) {
                    runReadAction { psiElement.docComment?.text }
                } else {
                    null
                }
                return ContextSymbolHeader(
                    name = name,
                    kind = NodeType.Class,
                    docstring = docString
                )
            }

            else -> return null
        }
    }

    override fun readImportsSection(psiFile: PsiFile): String? = PsiTreeUtil.findChildOfType(psiFile, KtImportList::class.java)?.text
    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        val importSignatures = mutableSetOf<ImportSignature>()
        val psiFile = runReadAction { element.containingFile }
            ?: return emptyList()
        val importStatements = runReadAction { PsiTreeUtil.findChildrenOfType(psiFile, KtImportList::class.java) }
        for (importStatement in importStatements) {
            for (importElement in runReadAction { importStatement.imports }) {
                val expression = runReadAction { importElement.importedReference as? KtDotQualifiedExpression } ?: continue
                val reference = runReadAction {
                    expression
                        .selectorExpression
                        ?.referenceExpression()
                        ?.mainReference
                }
                runReadAction {
                    resolver.toResolvedASTNode(reference?.resolve())?.let {
                        importSignatures.add(
                            ImportSignature(importElement.text, it)
                        )
                    }
                }
            }
        }
        return importSignatures.toList()
    }

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? KtFunction ?: runReadAction { PsiTreeUtil.getParentOfType(element, KtFunction::class.java) }
        val classElement = element as? KtClass ?: runReadAction { PsiTreeUtil.getParentOfType(element, KtClass::class.java) }
        val methodTypes = runReadAction { PsiTreeUtil.collectElementsOfType(methodElement, KtUserType::class.java) }

        // class types

        val fieldTypes = if (classElement != null) {
            runReadAction {
                classElement.allConstructors.flatMap {
                    PsiTreeUtil.collectElementsOfType(it, KtUserType::class.java)
                } +
                    classElement.getProperties().flatMap {
                        PsiTreeUtil.collectElementsOfType(it, KtUserType::class.java)
                    }
            }
        } else {
            emptyList()
        }
        val resolved = (methodTypes + fieldTypes).asSequence()
            .mapNotNull { runReadAction { it.referenceExpression?.mainReference?.resolve() } }
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.node?.startOffset) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }
            .toList()
        return resolved
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)
}
