package ai.zencoder.plugin.context

import ai.zencoder.generated.client.all.models.*
import ai.zencoder.plugin.agents.CustomAgentsManager
import ai.zencoder.plugin.graphql.generated.models.Context
import ai.zencoder.plugin.graphql.generated.models.ProjectInfo
import ai.zencoder.plugin.graphql.generated.models.Tab
import ai.zencoder.plugin.rag.RagStateManager
import ai.zencoder.plugin.services.TerminalService
import ai.zencoder.plugin.services.asChatHistoryMessage
import ai.zencoder.plugin.services.toSourceFile
import ai.zencoder.plugin.settings.ChatLLMChoice
import ai.zencoder.plugin.settings.CredentialKey
import ai.zencoder.plugin.settings.CredentialStore
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.model.ChatMessageRole
import ai.zencoder.plugin.webview.model.workflowAgentContextMessageHistoryInner
import com.intellij.openapi.application.ApplicationInfo
import com.intellij.openapi.application.ApplicationNamesInfo
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.SystemInfo
import io.sentry.Sentry
import java.io.File

@Service(PROJECT)
class AgentChatContextService(val project: Project) {

    private val chatManager get() = project.service<ChatManager>()
    private val contextGatherService get() = service<ContextGatherService>()
    private val ragStateManager get() = project.service<RagStateManager>()
    private val customAgentsManager get() = service<CustomAgentsManager>()

    fun collectContextGraphQL(operationId: String): Context? {
        val chatId = chatManager.findChatIdForOperationId(operationId)
        if (chatId == null) {
            Sentry.captureException(Exception("Chat is null for operation id $operationId"))
            thisLogger().error("Chat is null for operation id $operationId")
            return null
        }
        val chat = chatManager.getChatById(chatId)
        val editor = FileEditorManager.getInstance(project).selectedTextEditor
        val openedTabsAndFile = if (editor != null) {
            contextGatherService.gatherOpenedTabsAndOpenedFile(project, editor)
        } else {
            null
        }
        val currentFile = openedTabsAndFile?.openedFile?.toSourceFile()
        return Context(
            chatHistory = chat.messages.asSequence().mapNotNull { it.asChatHistoryMessage() }.toList(),
            currentFile = currentFile,
            repoId = ragStateManager.state.repositoryId,
            chatId = chat.id,
            projectInfo = projectInfoGraphQL(),
            tabs = openedTabsAndFile?.openedTabs
                ?.map { tab ->
                    Tab(tab.index, tab.focused, tab.content.toSourceFile())
                }?.toList() ?: emptyList()
        )
    }

    fun collectAgentContext(operationId: String): ZencoderWorkflowAgentContext {
        val chatId = chatManager.findChatIdForOperationId(operationId)
        if (chatId == null) {
            Sentry.captureException(Exception("Chat is null for operation id $operationId"))
            thisLogger().error("Chat is null for operation id $operationId")
            return ZencoderWorkflowAgentContext(listOf())
        }
        val chat = chatManager.getChatById(chatId)
        return ZencoderWorkflowAgentContext(
            messageHistory = chat.messages.asSequence().flatMap { message ->
                message.workflowAgentContextMessageHistoryInner(customAgentsManager.getAll())
            }.toList(),
            repoId = ragStateManager.state.repositoryId,
            chatId = chatId,
            environmentInfo = ZencoderWorkflowEnvironmentInfo(
                roots = project.basePath?.let {
                    listOf(
                        ZencoderWorkflowEnvironmentRoot(
                            fsPath = it,
                            repoIndexingPrefix = null
                        )
                    )
                },
                shellName = project.service<TerminalService>().currentShell(),
                osName = SystemInfo.OS_NAME,
                osVersion = SystemInfo.OS_VERSION,
                lineSeparator = System.lineSeparator()
            )
        )
    }

    fun collectIdeContext(operationId: String): ZencoderWorkflowIdeContext {
        val (openedTabs, currentFile) = collectOpenTabsAndCurrentFile(operationId)
        return ZencoderWorkflowIdeContext(
            ideName = ApplicationNamesInfo.getInstance().fullProductName,
            ideVersion = ApplicationInfo.getInstance().fullVersion,
            tabs = openedTabs,
            currentFile = currentFile
        )
    }

    private fun collectOpenTabsAndCurrentFile(
        operationId: String
    ): Pair<List<ZencoderWorkflowHistoryTab>, ZencoderWorkflowHistoryContextFile?> {
        val chatId = chatManager.findChatIdForOperationId(operationId)
        val chat = chatId?.let { chatManager.getChatById(it) }
        val lastUserMessage = chat?.messages?.findLast { it.role == ChatMessageRole.USER }
        val codebaseEnabled = lastUserMessage?.context?.codebaseEnabled != false
        val editor = FileEditorManager.getInstance(project).selectedTextEditor
        val openedTabsAndFile = if (editor != null) {
            contextGatherService.gatherOpenedTabsAndOpenedFile(project, editor)
        } else {
            null
        }
        val currentFile = if (lastUserMessage?.context?.currentFile != null) {
            lastUserMessage.context?.currentFile?.let {
                ZencoderWorkflowHistoryContextFile(
                    path = it.path,
                    fsPath = it.fsPath ?: (project.basePath?.let { basePath -> File(basePath, it.path).path } ?: it.path),
                    language = it.language,
                    content = it.content
                )
            }
        } else {
            null
        }
        return Pair(
            if (codebaseEnabled) {
                openedTabsAndFile?.openedTabs?.map { tab ->
                    ZencoderWorkflowHistoryTab(
                        focused = tab.focused,
                        content = ZencoderWorkflowHistoryContextFile(
                            path = tab.content.relativePath,
                            fsPath = tab.content.absolutePath,
                            language = tab.content.language
                        )
                    )
                } ?: emptyList()
            } else {
                emptyList()
            },
            currentFile
        )
    }

    fun collectLLMSettings(settings: ZencoderSettings): ZencoderWorkflowLLMSettings? {
        return if (settings.isOwnApiKeyEnabled) {
            ZencoderWorkflowLLMSettings(
                modelsUse = if (settings.chatLlmChoice == ChatLLMChoice.DEFAULT) {
                    null
                } else {
                    listOfNotNull(
                        when (settings.chatLlmChoice) {
                            ChatLLMChoice.GPT_4O -> ZencoderWorkflowModelUse(
                                model = ZencoderWorkflowModelType.OPEN_AI,
                                entityTag = ChatLLMChoice.GPT_4O.value
                            )
                            ChatLLMChoice.CLAUDE_3_5 -> ZencoderWorkflowModelUse(
                                model = ZencoderWorkflowModelType.ANTHROPIC,
                                entityTag = ChatLLMChoice.CLAUDE_3_5.value
                            )
                            ChatLLMChoice.CLAUDE_3_7 -> ZencoderWorkflowModelUse(
                                model = ZencoderWorkflowModelType.ANTHROPIC,
                                entityTag = ChatLLMChoice.CLAUDE_3_7.value
                            )
                            else -> null
                        }
                    )
                },
                modelsApiKey = listOfNotNull(
                    CredentialStore.retrieve(CredentialKey.OPENAI_API_KEY)?.let { apiKey ->
                        ZencoderWorkflowModelApiKey(
                            model = ZencoderWorkflowModelType.OPEN_AI,
                            apiKey = apiKey
                        )
                    },
                    CredentialStore.retrieve(CredentialKey.ANTHROPIC_API_KEY)?.let { apiKey ->
                        ZencoderWorkflowModelApiKey(
                            model = ZencoderWorkflowModelType.ANTHROPIC,
                            apiKey = apiKey
                        )
                    }
                )
            )
        } else {
            null
        }
    }

    private fun projectInfoGraphQL() = ProjectInfo(
        rootDir = project.basePath?.let { File(it) }?.name,
        shellName = project.service<TerminalService>().currentShell(),
        ideName = ApplicationNamesInfo.getInstance().fullProductName,
        ideVersion = ApplicationInfo.getInstance().fullVersion,
        osName = SystemInfo.OS_NAME,
        osVersion = SystemInfo.OS_VERSION
    )
}
