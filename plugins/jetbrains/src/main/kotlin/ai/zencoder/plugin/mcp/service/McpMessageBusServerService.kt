package ai.zencoder.plugin.mcp.service

import ai.zencoder.plugin.mcp.sdk.EmptyRequestResult
import ai.zencoder.plugin.mcp.sdk.Method
import ai.zencoder.plugin.mcp.sdk.PingRequest
import ai.zencoder.plugin.mcp.sdk.ServerCapabilities
import ai.zencoder.plugin.mcp.sdk.server.Server
import ai.zencoder.plugin.mcp.sdk.server.ServerOptions
import ai.zencoder.plugin.mcp.sdk.server.mcpMessageBusServer
import com.intellij.openapi.Disposable
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.util.messages.Topic
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch

val MCP_MESSAGE_BUS_SERVER_LISTENER = Topic.create("zencoder.mcp.internal", McpMessageBusServerListener::class.java)

fun interface McpMessageBusServerListener {
    fun started(server: Server)
}

/**
 * Service that manages the MCP MessageBus server.
 * This service is started when the plugin is loaded and provides
 * a central point for registering MCP handlers.
 */
@Service(Service.Level.PROJECT)
class McpMessageBusServerService(
    private val project: Project,
    private val cs: CoroutineScope
) : Disposable {
    private val logger = thisLogger()
    private val coroutineScope = CoroutineScope(cs.coroutineContext + Dispatchers.Default + SupervisorJob())
    private val publisher = project.messageBus.syncPublisher(MCP_MESSAGE_BUS_SERVER_LISTENER)

    private lateinit var server: Server

    lateinit var job: Job

    /**
     * Starts the MCP MessageBus server
     */
    fun startServer() {
        if (::job.isInitialized && !job.isCancelled) {
            logger.info("MCP MessageBus server already started")
            return
        }
        logger.info("Starting MCP MessageBus server...")

        job = coroutineScope.launch {
            try {
                // Create server with capabilities
                server = project.mcpMessageBusServer(
                    coroutineScope = coroutineScope,
                    disposable = this@McpMessageBusServerService,
                    options = ServerOptions(
                        capabilities = ServerCapabilities(
                            prompts = ServerCapabilities.Prompts(listChanged = true),
                            resources = ServerCapabilities.Resources(subscribe = true, listChanged = true),
                            tools = ServerCapabilities.Tools(listChanged = true)
                        )
                    )
                )

                // Register basic handlers
                registerBasicHandlers()

                // Register annotated handlers
                registerAnnotatedHandlers()

                publisher.started(server)
                logger.info("MCP MessageBus server started successfully")
            } catch (e: Exception) {
                logger.warn("Failed to start MCP MessageBus server", e)
            }
        }
    }

    /**
     * Registers basic handlers for the server
     */
    private suspend fun registerBasicHandlers() {
        // Register ping handler
        server.setRequestHandler<PingRequest>(Method.Defined.Ping) { _, _ ->
            logger.debug("Received ping request")
            EmptyRequestResult()
        }
    }

    /**
     * Registers handlers based on annotations
     */
    private suspend fun registerAnnotatedHandlers() {
        McpAnnotationProcessor.registerAnnotatedHandlers(project, server)
    }

    override fun dispose() {
        if (::server.isInitialized) {
            coroutineScope.launch {
                try {
                    server.close()
                    logger.info("MCP MessageBus server closed")
                } catch (e: Exception) {
                    logger.warn("Error closing MCP MessageBus server", e)
                }
            }
        }
    }

    companion object {
        /**
         * Gets the McpMessageBusServerService instance for the given project
         */
        fun getInstance(project: Project): McpMessageBusServerService {
            return project.service()
        }
    }
}
