package ai.zencoder.plugin.migration.patches

import ai.zencoder.generated.client.all.models.ZencoderWorkflowToolkitType
import ai.zencoder.plugin.agents.CustomAgentsManager
import ai.zencoder.plugin.agents.LegacyCustomAgentsStorage
import ai.zencoder.plugin.mcp.MCPLibraryManager
import ai.zencoder.plugin.mcp.MCPServersStateManager
import ai.zencoder.plugin.mcp.service.McpMessageBusServerService
import ai.zencoder.plugin.migration.ZencoderAppPatch
import ai.zencoder.plugin.migration.ZencoderAppPatch.AppContext
import ai.zencoder.plugin.utils.rethrowIfNotConflict
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.getOpenedProjects
import com.intellij.util.application
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlin.time.Duration.Companion.seconds

private val MCP_TIMEOUT = 10.seconds

object MigrateCustomAgentsToBackend : ZencoderAppPatch {
    private val legacyCustomAgents get() = service<LegacyCustomAgentsStorage>().state.agents

    override val idSuffix = "migrate-custom-agents-to-backend"

    override fun isNeeded(ctx: AppContext): Boolean {
        return legacyCustomAgents.isNotEmpty()
    }

    override fun doPatch(ctx: AppContext) {
        application.executeOnPooledThread {
            val alreadyMigratedCommandNames = runBlocking {
                service<CustomAgentsManager>()
                    .getAll()
                    .mapTo(mutableSetOf()) { it.commandName }
            }

            val tools = collectTools()
            legacyCustomAgents.values
                .filter { it.commandName !in alreadyMigratedCommandNames }
                .forEach { agent ->
                    service<CustomAgentsManager>()
                        .save(agent.copy(id = "", tools = tools))
                        .rethrowIfNotConflict()
                }

            legacyCustomAgents.clear()
            thisLogger().info("Custom agents migration completed successfully")
        }
    }

    private fun collectTools() = getOpenedProjects().flatMap { project ->
        runBlocking {
            try {
                withContext(Dispatchers.IO) {
                    withTimeout(MCP_TIMEOUT) {
                        // Start MCP Message Bus server
                        val mcpMessageBusServerService = project.service<McpMessageBusServerService>()
                        mcpMessageBusServerService.startServer()
                        // Get agent tools from MCPLibraryManager
                        val mcpLibraryManager = project.service<MCPLibraryManager>()
                        val agentTools = mcpLibraryManager.getAgentToolsState()

                        // Get installed server configs
                        val mcpServersStateManager = project.service<MCPServersStateManager>()
                        val mcpConfig = mcpServersStateManager.getAllServerConfigs()

                        agentTools.values.filter { serverInfo ->
                            // Include tools that are from Zencoder (have an author that is a ZencoderWorkflowToolkitType)
                            (
                                serverInfo.author != null &&
                                    ZencoderWorkflowToolkitType.entries
                                        .filter { it != ZencoderWorkflowToolkitType.EXTERNAL_MCP }
                                        .map { it.value }
                                        .contains(serverInfo.author)
                                ) ||
                                // For backward compatibility add also all external MCP servers installed by users
                                mcpConfig.keys.contains(serverInfo.id)
                        }.toList()
                    }
                }
            } catch (e: Exception) {
                thisLogger().warn("Failed to get default MCP servers for migration from project ${project.name}: ${e.message}", e)
                emptyList()
            }
        }
    }.toList()
}
