package ai.zencoder.plugin.context

import ai.zencoder.plugin.context.resolve.PythonNodeResolver
import com.intellij.lang.Language
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.PsiElement
import com.intellij.psi.PsiFile
import com.intellij.psi.util.PsiTreeUtil
import com.jetbrains.python.PythonLanguage
import com.jetbrains.python.psi.PyClass
import com.jetbrains.python.psi.PyFunction
import com.jetbrains.python.psi.PyImportStatementBase
import com.jetbrains.python.psi.PyReferenceExpression

@Suppress("DuplicatedCode")
class PythonContextGather : ContextGather {

    private val resolver = PythonNodeResolver()
    override fun isSupportedLanguage(language: Language): Boolean {
        return language.isKindOf(PythonLanguage.INSTANCE)
    }

    override fun readContextSymbolHeader(psiElement: PsiElement, extractDocComment: Boolean): ContextSymbolHeader? {
        when (psiElement) {
            is PyFunction -> {
                val name = psiElement.name ?: return null
                val docString = if (extractDocComment) {
                    psiElement.docStringValue
                } else {
                    null
                }
                return ContextSymbolHeader(name, NodeType.Method, docString)
            }

            is PyClass -> {
                val name = psiElement.name ?: return null
                val docString = if (extractDocComment) {
                    psiElement.docStringValue
                } else {
                    null
                }
                return ContextSymbolHeader(name, NodeType.Class, docString)
            }

            else -> {
                return null
            }
        }
    }

    override fun readImportsSection(psiFile: PsiFile): String? =
        PsiTreeUtil.findChildOfType(psiFile, PyImportStatementBase::class.java)?.text

    override fun getImportSignatures(element: PsiElement): List<ImportSignature> {
        val importSignatures = mutableSetOf<ImportSignature>()
        val psiFile = runReadAction { element.containingFile }
            ?: return emptyList()
        for (importStmt in runReadAction { PsiTreeUtil.findChildrenOfType(psiFile, PyImportStatementBase::class.java) }) {
            for (importElement in runReadAction { importStmt.importElements }) {
                for (resolvedElement in runReadAction { importElement.multiResolve() }) {
                    runReadAction {
                        resolvedElement.element?.let { resolver.toResolvedASTNode(it) }?.let {
                            importSignatures.add(
                                ImportSignature(importStmt.text, it)
                            )
                        }
                    }
                }
            }
        }
        return importSignatures.toList()
    }

    override fun getExternalSignatures(element: PsiElement): List<ResolvedASTNode> {
        val methodElement = element as? PyFunction ?: runReadAction { PsiTreeUtil.getParentOfType(element, PyFunction::class.java) }
        val classElement = element as? PyClass ?: runReadAction { PsiTreeUtil.getParentOfType(element, PyClass::class.java) }
        val methodTypes =
            runReadAction {
                PsiTreeUtil.collectElementsOfType(methodElement, PyReferenceExpression::class.java).map { it.reference.resolve() }
            }
        val classTypes = if (classElement != null) {
            runReadAction { (classElement.instanceAttributes + classElement.classAttributes).map { it.reference.resolve() } }
        } else {
            emptyList()
        }
        val resolved = (methodTypes + classTypes).asSequence()
            .filterNotNull()
            .filter { runReadAction { it.containingFile != element.containingFile } }
            .distinctBy { runReadAction { Pair(it.containingFile, it.node?.startOffset) } }
            .mapNotNull { runReadAction { resolver.toResolvedASTNode(it, true) } }.toList()
        return resolved
    }

    override fun getFileSignatures(psiFile: PsiFile): List<ResolvedASTNode> = resolver.getFileSignatures(psiFile)
}
