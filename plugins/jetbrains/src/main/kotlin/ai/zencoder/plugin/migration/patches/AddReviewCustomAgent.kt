package ai.zencoder.plugin.migration.patches

import ai.zencoder.plugin.agents.Agent
import ai.zencoder.plugin.agents.CustomAgentsManager
import ai.zencoder.plugin.agents.DEFAULT_REVIEW_AGENT_PROMPT
import ai.zencoder.plugin.mcp.MCPLibraryManager
import ai.zencoder.plugin.migration.ZencoderAppPatch
import ai.zencoder.plugin.migration.ZencoderAppPatch.AppContext
import ai.zencoder.plugin.utils.rethrowIfNotConflict
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.getOpenedProjects
import kotlinx.coroutines.runBlocking
import java.time.Instant

private const val REVIEW_AGENT_NAME = "Review"
private const val REVIEW_AGENT_COMMAND_NAME = "review"
private val ALLOWED_TOOLS = setOf(
    "web_search",
    "fetch_webpage",
    "RequirementsTool",
    "str_replace_editor",
    "ExecuteShellCommand"
)

object AddReviewCustomAgent : ZencoderAppPatch {
    private val customAgentsManager get() = service<CustomAgentsManager>()

    override val idSuffix = "add-review-custom-agent"

    override fun isNeeded(ctx: AppContext): Boolean {
        return customAgentsManager.getAll().none { it.commandName == REVIEW_AGENT_COMMAND_NAME }
    }

    override fun doPatch(ctx: AppContext) {
        val now = Instant.now().toString()

        val tools = getOpenedProjects().map { project ->
            runBlocking {
                try {
                    val mcpLibraryManager = project.service<MCPLibraryManager>()
                    val agentTools = mcpLibraryManager.getAgentToolsState()

                    agentTools.filterKeys { it in ALLOWED_TOOLS }.values.toList()
                } catch (e: Exception) {
                    thisLogger().warn("Failed to get default MCP servers for migration: ${e.message}", e)
                    emptyList()
                }
            }
        }.flatten().toList()

        customAgentsManager.save(
            Agent(
                id = "",
                name = REVIEW_AGENT_NAME,
                commandName = REVIEW_AGENT_COMMAND_NAME,
                command = DEFAULT_REVIEW_AGENT_PROMPT,
                codeLens = true,
                rag = true,
                creationDate = now,
                lastModifiedDate = now,
                tools = tools
            )
        ).rethrowIfNotConflict()
    }
}
