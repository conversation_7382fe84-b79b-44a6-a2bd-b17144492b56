package ai.zencoder.plugin.settings

import ai.zencoder.plugin.migration.APPLICATION_PATCHES_IDS
import ai.zencoder.plugin.migration.PROJECT_PATCHES_IDS
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.rag.RepoRagUploader
import ai.zencoder.plugin.socket.ably.AblyConnectionManager
import ai.zencoder.plugin.utils.OTHER_RUN_ONCE_ACTIVITIES_IDS
import ai.zencoder.plugin.utils.unmarkAsRunOnce
import ai.zencoder.plugin.webview.chat.ChatWebviewManager
import ai.zencoder.plugin.webview.model.PostedMessage.SetSettings
import ai.zencoder.plugin.webview.model.ShellToolConfig
import ai.zencoder.plugin.webview.service.AppLevelSettingsManager
import com.intellij.openapi.components.service
import com.intellij.openapi.options.Configurable
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.KeyWithDefaultValue
import javax.swing.JComponent
import kotlin.reflect.KProperty

val REPO_INDEXING = KeyWithDefaultValue.create("zencoder.repoIndexing", true)

class ZencoderSettingsConfigurable(private val project: Project) : Configurable {

    private var zencoderSettingsComponent: ZencoderSettingsComponent? = null
    private val ragUploader get() = project.service<RepoRagUploader>()

    override fun getDisplayName(): String = "Zencoder"

    override fun getPreferredFocusedComponent(): JComponent {
        val initializedComponent = zencoderSettingsComponent as ZencoderSettingsComponent
        return initializedComponent.getPreferredFocusedComponent()
    }

    override fun createComponent(): JComponent {
        zencoderSettingsComponent = ZencoderSettingsComponent(project)
        return (zencoderSettingsComponent as ZencoderSettingsComponent).getMainPanel()
    }

    override fun isModified(): Boolean {
        val settings: ZencoderSettings = service<ZencoderSettings>()
        val initializedComponent = zencoderSettingsComponent as ZencoderSettingsComponent
        val modified =
            initializedComponent.getAuthBaseUrl() != settings.authBaseUrl ||
                initializedComponent.getApiBaseUrl() != settings.apiBaseUrl ||
                initializedComponent.getSignInUrl() != settings.signInUrl ||
                initializedComponent.getSignUpUrl() != settings.signUpUrl ||
                initializedComponent.getAgentsUrl() != settings.agentsServiceUrl ||
                initializedComponent.getCodeGenUrl() != settings.codeGenServiceUrl ||
                initializedComponent.getCodeRepairUrl() != settings.codeRepairServiceUrl ||
                initializedComponent.getUnitTestUrl() != settings.unitTestsServiceUrl ||
                initializedComponent.getFeedbackUrl() != settings.feedbackServiceUrl ||
                initializedComponent.getChatUrl() != settings.chatServiceUrl ||
                initializedComponent.getRepoUploadUrl() != settings.repoUploadServiceUrl ||
                initializedComponent.getCodeCompletionUrl() != settings.codeCompletionServiceUrl ||
                initializedComponent.getCloudStorageUrl() != settings.cloudStorageUrl ||
                initializedComponent.enabledRepoIndexing() != settings.codebaseIndexing ||
                initializedComponent.getExcludeFilePatterns() != settings.excludeCustomFilesPatterns ||
                initializedComponent.enabledInlineCodeCompletion() != settings.enableInlineCodeCompletion ||
                initializedComponent.enabledMultiLineCompletion() != settings.enableMultiLineCompletion ||
                initializedComponent.enabledNextEditPrediction() != settings.enableNextEditPrediction ||
                initializedComponent.getCodeCompletionDebounceDelay() != settings.codeCompletionDebounceDelay ||
                initializedComponent.isInlineSuggestionsEnabled() != settings.isInlineSuggestionsEnabled ||
                initializedComponent.getAblyUrl() != settings.ablyAuthUrl ||
                initializedComponent.isSentryEnabled() != settings.isSentryEnabled ||
                initializedComponent.isAnalyticsEnabled() != settings.isAnalyticsEnabled ||
                initializedComponent.getWorkflowServiceUrl() != settings.workflowServiceUrl ||
                initializedComponent.getDefaultServiceUrl() != settings.defaultServiceUrl ||
                initializedComponent.getWorkflowServiceUrl() != settings.workflowServiceUrl ||
                initializedComponent.isOwnApiKeyEnabled() != settings.isOwnApiKeyEnabled ||
                initializedComponent.getChatLLMChoice() != settings.chatLlmChoice ||
                initializedComponent.wasOpenApiKeyModified ||
                initializedComponent.wasAnthropicApiKeyModified ||
                initializedComponent.getShellToolAllowedCommands() != settings.shellToolAllowedCommands ||
                initializedComponent.getShellToolConfirmationPolicy() != settings.shellToolConfirmationPolicy ||
                initializedComponent.getDisplayDebugInfo() != settings.displayDebugInfo ||
                initializedComponent.enabledShellTool() != settings.enableShellTool ||
                initializedComponent.isRequirementsToolEnabled() != settings.isRequirementsToolEnabled ||
                (APPLICATION_PATCHES_IDS - initializedComponent.getApplicationPatches()).isNotEmpty() ||
                (PROJECT_PATCHES_IDS - initializedComponent.getProjectPatches()).isNotEmpty() ||
                (OTHER_RUN_ONCE_ACTIVITIES_IDS - initializedComponent.getOtherRunOnceActivities()).isNotEmpty()

        return modified
    }

    override fun apply() {
        val settings: ZencoderSettings = service<ZencoderSettings>()
        val initializedComponent = zencoderSettingsComponent as ZencoderSettingsComponent
        settings.authBaseUrl = initializedComponent.getAuthBaseUrl()
        settings.apiBaseUrl = initializedComponent.getApiBaseUrl()
        settings.signInUrl = initializedComponent.getSignInUrl()
        settings.signUpUrl = initializedComponent.getSignUpUrl()
        settings.agentsServiceUrl = initializedComponent.getAgentsUrl()
        settings.codeGenServiceUrl = initializedComponent.getCodeGenUrl()
        settings.codeRepairServiceUrl = initializedComponent.getCodeRepairUrl()
        settings.unitTestsServiceUrl = initializedComponent.getUnitTestUrl()
        settings.feedbackServiceUrl = initializedComponent.getFeedbackUrl()
        settings.chatServiceUrl = initializedComponent.getChatUrl()
        settings.repoUploadServiceUrl = initializedComponent.getRepoUploadUrl()
        settings.codeCompletionServiceUrl = initializedComponent.getCodeCompletionUrl()
        settings.workflowServiceUrl = initializedComponent.getWorkflowServiceUrl()
        settings.defaultServiceUrl = initializedComponent.getDefaultServiceUrl()
        settings.cloudStorageUrl = initializedComponent.getCloudStorageUrl()
        if (settings.displayDebugInfo != initializedComponent.getDisplayDebugInfo()) {
            settings.displayDebugInfo = initializedComponent.getDisplayDebugInfo()
            project.service<ChatWebviewManager>().send(
                SetSettings(settings = service<AppLevelSettingsManager>().collectSettings(isChatView = true))
            )
        }

        trackSettingsChange(
            property = ZencoderSettings::enableInlineCodeCompletion,
            oldValue = settings.enableInlineCodeCompletion,
            newValue = initializedComponent.enabledInlineCodeCompletion()
        )
        settings.enableInlineCodeCompletion = initializedComponent.enabledInlineCodeCompletion()
        trackSettingsChange(
            property = ZencoderSettings::enableMultiLineCompletion,
            oldValue = settings.enableMultiLineCompletion,
            newValue = initializedComponent.enabledMultiLineCompletion()
        )
        settings.enableMultiLineCompletion = initializedComponent.enabledMultiLineCompletion()
        settings.codeCompletionDebounceDelay = initializedComponent.getCodeCompletionDebounceDelay()
        trackSettingsChange(
            property = ZencoderSettings::codeCompletionDebounceDelay,
            oldValue = settings.codeCompletionDebounceDelay,
            newValue = initializedComponent.getCodeCompletionDebounceDelay()
        )
        trackSettingsChange(
            property = ZencoderSettings::enableNextEditPrediction,
            oldValue = settings.enableNextEditPrediction,
            newValue = initializedComponent.enabledNextEditPrediction()
        )
        settings.enableNextEditPrediction = initializedComponent.enabledNextEditPrediction()
        settings.isInlineSuggestionsEnabled = initializedComponent.isInlineSuggestionsEnabled()
        settings.isSentryEnabled = initializedComponent.isSentryEnabled()
        settings.isAnalyticsEnabled = initializedComponent.isAnalyticsEnabled()

        settings.isOwnApiKeyEnabled = initializedComponent.isOwnApiKeyEnabled()
        settings.chatLlmChoice = initializedComponent.getChatLLMChoice()
        if (initializedComponent.wasOpenApiKeyModified) {
            CredentialStore.store(CredentialKey.OPENAI_API_KEY, initializedComponent.getOpenApiKey())
            initializedComponent.wasOpenApiKeyModified = false
        }
        if (initializedComponent.wasAnthropicApiKeyModified) {
            CredentialStore.store(CredentialKey.ANTHROPIC_API_KEY, initializedComponent.getAnthropicApiKey())
            initializedComponent.wasAnthropicApiKeyModified = false
        }

        // repo grokking
        val indexingBefore = settings.codebaseIndexing
        trackSettingsChange(
            property = ZencoderSettings::codebaseIndexing,
            oldValue = settings.codebaseIndexing,
            newValue = initializedComponent.enabledRepoIndexing()
        )
        settings.codebaseIndexing = initializedComponent.enabledRepoIndexing()

        val excludeByGitignoreBefore = settings.excludeByGitignore
        trackSettingsChange(
            property = ZencoderSettings::excludeByGitignore,
            oldValue = settings.excludeByGitignore,
            newValue = initializedComponent.isExcludedByGitIgnore()
        )
        settings.excludeByGitignore = initializedComponent.isExcludedByGitIgnore()
        val excludeByGitignoreChanged = excludeByGitignoreBefore != settings.excludeByGitignore

        val previousExcludeFilePatterns = settings.excludeCustomFilesPatterns
        settings.excludeCustomFilesPatterns = initializedComponent.getExcludeFilePatterns()
        val excludeFilePatternsChanged = previousExcludeFilePatterns != settings.excludeCustomFilesPatterns

        val indexingAfter = initializedComponent.enabledRepoIndexing()
        when {
            (excludeByGitignoreChanged || excludeFilePatternsChanged) && indexingAfter -> ragUploader.upload(forceReindex = true)
            !indexingBefore && indexingAfter -> ragUploader.upload()
            indexingBefore && !indexingAfter -> ragUploader.clearRagProgress()
        }
        project.service<ChatWebviewManager>().updateRepoGrokkingProgress()
        project.putUserData(REPO_INDEXING, indexingAfter)

        if (settings.ablyAuthUrl != initializedComponent.getAblyUrl()) {
            settings.ablyAuthUrl = initializedComponent.getAblyUrl()
            service<AblyConnectionManager>().refresh()
        }
        settings.shellToolAllowedCommands = initializedComponent.getShellToolAllowedCommands()
        settings.shellToolConfirmationPolicy = initializedComponent.getShellToolConfirmationPolicy()
        project.service<ChatWebviewManager>()
            .sendShellToolConfig(
                config = ShellToolConfig(commandConfirmationPolicy = settings.shellToolConfirmationPolicy.toZencoderPolicy().value)
            )
        // tools options
        settings.enableShellTool = initializedComponent.enabledShellTool()
        settings.isRequirementsToolEnabled = initializedComponent.isRequirementsToolEnabled()

        // run once activities
        (APPLICATION_PATCHES_IDS - initializedComponent.getApplicationPatches()).forEach { unmarkAsRunOnce(it) }
        (PROJECT_PATCHES_IDS - initializedComponent.getProjectPatches()).forEach { project.unmarkAsRunOnce(it) }
        (OTHER_RUN_ONCE_ACTIVITIES_IDS - initializedComponent.getOtherRunOnceActivities()).forEach { unmarkAsRunOnce(it) }
    }

    private fun trackSettingsChange(
        property: KProperty<Any>,
        oldValue: Any,
        newValue: Any
    ) {
        if (oldValue != newValue) {
            track(
                "Plugin setting changed",
                "setting_name" to property.name,
                "value" to newValue
            )
        }
    }

    override fun reset() {
        val settings: ZencoderSettings = service<ZencoderSettings>()
        val initializedComponent = zencoderSettingsComponent as ZencoderSettingsComponent
        initializedComponent.setAuthBaseUrl(settings.authBaseUrl)
        initializedComponent.setApiBaseUrl(settings.apiBaseUrl)
        initializedComponent.setSignInUrl(settings.signInUrl)
        initializedComponent.setSignUpUrl(settings.signUpUrl)
        initializedComponent.setAgentsUrl(settings.agentsServiceUrl)
        initializedComponent.setCodeGenUrl(settings.codeGenServiceUrl)
        initializedComponent.setCodeRepairUrl(settings.codeRepairServiceUrl)
        initializedComponent.setUnitTestUrl(settings.unitTestsServiceUrl)
        initializedComponent.setFeedbackUrl(settings.feedbackServiceUrl)
        initializedComponent.setChatUrl(settings.chatServiceUrl)
        initializedComponent.setRepoUploadUrl(settings.repoUploadServiceUrl)
        initializedComponent.setCodeCompletionUrl(settings.codeCompletionServiceUrl)
        initializedComponent.setCloudStorageUrl(settings.cloudStorageUrl)

        initializedComponent.setOwnApiKeyEnabled(settings.isOwnApiKeyEnabled)
        initializedComponent.setChatLLMChoice(settings.chatLlmChoice)

        CredentialStore.retrieveAsync(CredentialKey.OPENAI_API_KEY).then {
            initializedComponent.setOpenApiKey(it ?: "")
        }
        initializedComponent.wasOpenApiKeyModified = false

        CredentialStore.retrieveAsync(CredentialKey.ANTHROPIC_API_KEY).then {
            initializedComponent.setAnthropicApiKey(it ?: "")
        }
        initializedComponent.wasAnthropicApiKeyModified = false

        // repo grokking
        initializedComponent.setEnabledRepoIndexing(settings.codebaseIndexing)
        initializedComponent.setExcludeByGitignore(settings.excludeByGitignore)
        initializedComponent.setExcludeFilePatterns(settings.excludeCustomFilesPatterns)

        initializedComponent.setEnabledInlineCodeCompletion(settings.enableInlineCodeCompletion)
        initializedComponent.setEnabledMultilineCompletion(settings.enableMultiLineCompletion)
        initializedComponent.setCodeCompletionDebounceDelay(settings.codeCompletionDebounceDelay)
        initializedComponent.setEnabledNextEditPrediction(settings.enableNextEditPrediction)
        initializedComponent.setInlineSuggestionsEnabled(settings.isInlineSuggestionsEnabled)
        initializedComponent.setAblyUrl(settings.ablyAuthUrl)
        initializedComponent.setWorkflowServiceUrl(settings.workflowServiceUrl)
        initializedComponent.setDefaultServiceUrl(settings.defaultServiceUrl)

        initializedComponent.setSentryEnabled(settings.isSentryEnabled)
        initializedComponent.setAnalyticsEnabled(settings.isAnalyticsEnabled)

        // shell tool
        initializedComponent.setAllowedShellToolCommands(settings.shellToolAllowedCommands)
        initializedComponent.setShellToolConfirmationPolicy(settings.shellToolConfirmationPolicy)

        initializedComponent.setDisplayDebugInfo(settings.displayDebugInfo)

        // tools options
        initializedComponent.setEnabledShellTool(settings.enableShellTool)
        initializedComponent.setRequirementsToolEnabled(settings.isRequirementsToolEnabled)

        // run once activities
        initializedComponent.setApplicationPatches(APPLICATION_PATCHES_IDS)
        initializedComponent.setProjectPatches(PROJECT_PATCHES_IDS)
        initializedComponent.setOtherRunOnceActivities(OTHER_RUN_ONCE_ACTIVITIES_IDS)
    }
}
