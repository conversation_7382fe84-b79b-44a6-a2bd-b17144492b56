package ai.zencoder.plugin.providers.completion

import ai.zencoder.generated.client.feedback.models.UserFeedback
import ai.zencoder.generated.client.feedback.models.UserFeedbackRequest
import ai.zencoder.plugin.api.UserFeedbackService
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.providers.completion.nextchange.NextEditPredictionHandler
import com.intellij.codeInsight.inline.completion.InlineCompletionEventAdapter
import com.intellij.codeInsight.inline.completion.InlineCompletionEventType
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.Editor
import com.intellij.util.application
import com.jetbrains.rd.util.AtomicReference
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

data class CompletionText(
    val requestId: String,
    val text: String,
    val fileType: String,
    val createdAt: Long
)

class ZenInlineCompletionEventListener(private val editor: Editor) : InlineCompletionEventAdapter {
    private val userFeedbackService get() = service<UserFeedbackService>()
    private val codeSuggestionService get() = service<CodeSuggestionService>()
    private val nextEditPredictionHandler get() = service<NextEditPredictionHandler>()

    private var currentAcceptedLength: AtomicInteger = AtomicInteger(0)
    private var lastRequestId = AtomicReference("")
    private var wasInserted: AtomicBoolean = AtomicBoolean(false)

    private fun submitUserFeedback(
        requestId: String,
        text: String,
        fileType: String,
        acceptedLength: Int,
        visibleTime: Long
    ) {
        if (text.isEmpty()) {
            return
        }
        val acceptedText = text.substring(0, acceptedLength)
        val isAccepted = acceptedLength > 0

        val eventSuffix = if (isAccepted) "accepted" else "rejected"
        val analyticsProps = mutableMapOf<String, Any?>(
            "message_len" to text.length,
            "file_type" to fileType,
            "is_multiLine" to text.contains('\n'),
            "visible_time_ms" to visibleTime,
            "request_id" to requestId,
            "screen" to "editor"
        )
        if (isAccepted) {
            val acceptType = if (acceptedLength < text.length) "partially" else "full"
            analyticsProps += "accept_type" to acceptType
        }
        track("Code completion $eventSuffix", analyticsProps)

        application.executeOnPooledThread {
            userFeedbackService.submitUserFeedback(
                UserFeedbackRequest(
                    requestId = requestId,
                    ideName = UserFeedbackRequest.IdeName.jetbrains,
                    feedback =
                    mapOf(
                        "0" to
                            UserFeedback(
                                text = if (isAccepted) acceptedText else text,
                                accepted = isAccepted,
                                modification = ""
                            )
                    )
                )
            )
        }
    }

    override fun onShow(event: InlineCompletionEventType.Show) {
        val completionText = editor.getUserData(COMPLETION_TEXT_KEY) ?: return
        val oldRequestId = lastRequestId.getAndSet(completionText.requestId)
        if (oldRequestId != completionText.requestId) {
            track(
                "Code completion shown",
                "message_len" to completionText.text.length,
                "file_type" to completionText.fileType,
                "is_multiLine" to completionText.text.contains('\n'),
                "request_id" to completionText.requestId,
                "screen" to "editor"
            )
        }
    }

    override fun onChange(event: InlineCompletionEventType.Change) {
        currentAcceptedLength.addAndGet(event.lengthChange)
    }

    override fun onHide(event: InlineCompletionEventType.Hide) {
        val acceptedLength = currentAcceptedLength.getAndSet(0)
        if (!event.isCurrentlyDisplaying || wasInserted.compareAndSet(true, false)) return
        val completionText = editor.getUserData(COMPLETION_TEXT_KEY) ?: return
        codeSuggestionService.reset()
        submitUserFeedback(
            requestId = completionText.requestId,
            text = completionText.text,
            fileType = completionText.fileType,
            acceptedLength = acceptedLength,
            visibleTime = System.currentTimeMillis() - completionText.createdAt
        )
        nextEditPredictionHandler.onCompletionRejected(editor)
    }

    override fun onInsert(event: InlineCompletionEventType.Insert) {
        codeSuggestionService.reset()
        currentAcceptedLength.set(0)
        wasInserted.set(true)
        val completionText = editor.getUserData(COMPLETION_TEXT_KEY) ?: return
        submitUserFeedback(
            requestId = completionText.requestId,
            text = completionText.text,
            fileType = completionText.fileType,
            acceptedLength = completionText.text.length,
            visibleTime = System.currentTimeMillis() - completionText.createdAt
        )
        nextEditPredictionHandler.onCompletionAccepted(editor)
    }

    override fun onCompletion(event: InlineCompletionEventType.Completion) {
        if (event.cause == null) {
            showTooltip(editor)
        }
    }
}
