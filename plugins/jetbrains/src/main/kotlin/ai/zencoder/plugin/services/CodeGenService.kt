package ai.zencoder.plugin.services

import ai.zencoder.generated.client.codegen.models.CodeEditRequest
import ai.zencoder.generated.client.codegen.models.TabItem
import ai.zencoder.generated.client.repair.models.DiagnosticsContext
import ai.zencoder.generated.client.repair.models.DiagnosticsContextFile
import ai.zencoder.generated.client.repair.models.DiagnosticsRequest
import ai.zencoder.generated.client.repair.models.DiagnosticsResponse
import ai.zencoder.plugin.api.CodeGenService
import ai.zencoder.plugin.api.CodeRepairService
import ai.zencoder.plugin.api.LintRepairStatus
import ai.zencoder.plugin.api.LintRequestDiagnostics
import ai.zencoder.plugin.api.exception.RateLimitException
import ai.zencoder.plugin.codegen.CodeGenInlayPanel
import ai.zencoder.plugin.codegen.DiffDialogListener
import ai.zencoder.plugin.context.CommandContext
import ai.zencoder.plugin.context.CommandContextConfig
import ai.zencoder.plugin.context.ContextGatherService
import ai.zencoder.plugin.context.OpenedTab
import ai.zencoder.plugin.diagnostics.DiagnosticsRunner
import ai.zencoder.plugin.diff.UnifiedDiffTool
import ai.zencoder.plugin.diff.applyPatchToContent
import ai.zencoder.plugin.editor.EditorComponentInlaysManager
import ai.zencoder.plugin.model.path.AbsolutePath
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.utils.*
import ai.zencoder.plugin.webview.chat.ChatDecorator
import ai.zencoder.plugin.webview.model.*
import ai.zencoder.plugin.webview.model.JsonContent.Type
import com.intellij.diff.comparison.ComparisonManager
import com.intellij.diff.comparison.ComparisonPolicy
import com.intellij.diff.fragments.LineFragment
import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.progress.DumbProgressIndicator
import com.intellij.openapi.progress.ProcessCanceledException
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Ref
import com.intellij.openapi.util.TextRange
import com.intellij.psi.PsiFile
import kotlinx.coroutines.*
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.bitbucket.cowwoc.diffmatchpatch.DiffMatchPatch
import java.util.*

@Service(Service.Level.PROJECT)
class CodeGenService(
    val project: Project,
    private val scope: CoroutineScope
) {

    private val contextGatherService get() = service<ContextGatherService>()
    private val diffMatchPatch = DiffMatchPatch()

    fun generateCodeWithChat(
        userQuery: String,
        editor: Editor,
        uiReceiver: UICodeGenReceiver? = null
    ) {
        track(
            "Codegen run",
            "prog_lang" to editor.language,
            "prompt_length" to userQuery.length
        )
        uiReceiver?.cancel()
        val commandMessage = prepareAgentMessage(editor, prompt = userQuery)
        project.service<ChatDecorator>().addToNewChatAndShow(commandMessage)
    }

    @Deprecated(
        message =
        "This method should not be used for now. We temporarily (or not temporarily) move inline code generation to the Agent chat." +
            "We keep it in the codebase because we might need it later. But let's see." +
            "TODO: remove this method after some time (today is May 28th, 2025).",
        replaceWith = ReplaceWith("generateCodeWithChat(userQuery, editor, uiReceiver)"),
        level = DeprecationLevel.ERROR
    )
    fun generateCode(
        editor: Editor,
        currentPsiFile: PsiFile,
        selectedCodeSpan: List<Int>,
        useRepair: Boolean?,
        calledFrom: String,
        userQuery: String,
        uiReceiver: UICodeGenReceiver? = null
    ) {
        val requestId = uuid()
        scope.launch {
            track(
                "Codegen initiated",
                "type" to "generate",
                "operation_id" to requestId,
                "prog_lang" to currentPsiFile.language.displayName,
                "called_from" to calledFrom
            )
            if (userQuery.isBlank()) {
                return@launch
            }
            object : Task.Backgroundable(project, "Gathering context", true) {

                override fun run(indicator: ProgressIndicator) = runBlocking {
                    track(
                        "Codegen run",
                        "type" to "generate",
                        "operation_id" to requestId,
                        "prog_lang" to currentPsiFile.language.displayName,
                        "prompt_length" to userQuery.length,
                        "is_agent_call" to (calledFrom == "custom_agent") // todo refactor
                    )

                    try {
                        val context = gatherContext(currentPsiFile, editor)
                        updateTitleOrCancel(indicator, uiReceiver, "Generating code")
                        var generatedCode = generateCodeAndApply(context, currentPsiFile, selectedCodeSpan, requestId, userQuery)
                        if (useRepair == true) {
                            updateTitleOrCancel(indicator, uiReceiver, "Validating code")
                            generatedCode = validateCodeAndApply(context, generatedCode, currentPsiFile, indicator, requestId)
                        }
                        updateTitleOrCancel(indicator, uiReceiver, "Waiting for confirmation")
                        val result = showConfirmation(project, editor, generatedCode)
                        thisLogger().debug("Code generation accepted: $result")
                        cleanup(currentPsiFile)
                    } catch (e: Exception) {
                        withContext(Dispatchers.EDT) {
                            handleError(e.message.orEmpty())
                            onCancel()
                        }
                        if (e is RateLimitException) {
                            invokeLater { showUpgradeWarning(e) }
                        } else if (e !is ProcessCanceledException) {
                            thisLogger().warn("Failed to generate code", e)
                            invokeLater { showWarning("Unable to generate code: ${e.message}") }
                        }
                    }
                }

                override fun onCancel() {
                    uiReceiver?.cancel()
                    val fileService = project.service<FileService>()
                    fileService.deleteTempCopyOfFileIfExists(currentPsiFile.name)
                }
            }.queue()
        }
    }

    fun showConfirmation(
        project: Project,
        editor: Editor,
        generatedCode: GeneratedCode
    ) {
        val relativeFilePath = RelativePathBuilder()
            .from(AbsolutePath(project.basePath as String))
            .to(AbsolutePath(editor.virtualFile.path))
        invokeLater {
            val diffDialog = UnifiedDiffTool(
                project = project,
                relativeFilePath,
                editor.virtualFile,
                null,
                generatedContentText = generatedCode.fullContentWithGenerated,
                afterApply = { project.messageBus.syncPublisher(DiffDialogListener.TOPIC).onApplyChanges(generatedCode) },
                afterCancel = { project.messageBus.syncPublisher(DiffDialogListener.TOPIC).onRejectChanges(generatedCode) },
                title = "Diff: ${editor.virtualFile.name}",
                scope = scope
            )
            diffDialog.showDiff()
        }
    }

    private fun handleError(message: String) {
        project.messageBus.syncPublisher(DiffDialogListener.TOPIC).onError(message)
    }

    private suspend inline fun updateTitleOrCancel(
        indicator: ProgressIndicator,
        receiver: UICodeGenReceiver?,
        title: String
    ) {
        indicator.checkCanceled()
        receiver?.updateStatus(title)
        indicator.text = title
    }

    private fun gatherContext(currentPsiFile: PsiFile, editor: Editor): FullContextForCodeGen {
        val commandContext =
            ApplicationManager.getApplication().runReadAction<CommandContext> {
                contextGatherService.gatherContext(
                    project = project,
                    editor = editor,
                    language = currentPsiFile.language,
                    CommandContextConfig(
                        openedTabs = true,
                        cursorLineNum = true,
                        activeFilePath = true,
                        fileLanguage = true
                    )
                )
            }
        val openedTabs = (commandContext.openedTabs as List<OpenedTab>).map { it.toTabItem() }
        val cursorLineNumber = commandContext.cursorLineNum as Int
        thisLogger().debug("cursorStartLineNumber = $cursorLineNumber")
        val diagnosticsRunner = DiagnosticsRunner.of(
            project,
            currentPsiFile
        )
        val ideDiagnostics = diagnosticsRunner.runBasicDiagnostics()
            .filter { it.positionForRepairCalculationAvailable() }
            .map {
                val errorMessage = ApplicationManager.getApplication().runReadAction<String> {
                    it.toString()
                }
                LintRequestDiagnostics(
                    message = errorMessage,
                    severity = "Warning",
                    source = currentPsiFile.language.displayName,
                    code = "",
                    range = it.calculatePositionForRepairRequest(currentPsiFile.virtualFile.getContent())
                )
            }
            .toList()
        return FullContextForCodeGen(
            commandContext,
            openedTabs,
            ideDiagnostics
        )
    }

    private fun generateCodeAndApply(
        context: FullContextForCodeGen,
        currentPsiFile: PsiFile,
        selectedCodeSpan: List<Int>,
        requestId: String,
        userQuery: String
    ): GeneratedCode {
        val cursorLineNumber = context.commandContext.cursorLineNum as Int
        val activeFilePath = project.calculateRelativePath(currentPsiFile.virtualFile.path)
        val codeGenRequest = CodeEditRequest(
            requestId = requestId,
            userQuery = userQuery,
            openedTabs = context.openTabs,
            activeFilePath = activeFilePath.actualPath,
            selectedCodeSpan = selectedCodeSpan,
            multiShot = emptyList()
        )
        thisLogger().debug("cursorLineNumber = $cursorLineNumber , selectedCodeSpan = ${selectedCodeSpan.joinToString(",")}")
        val fileContentForCodeGen = FileContentForCodeGen(
            context.commandContext.openedTabs!!.firstOrNull { it.path == activeFilePath.actualPath }
                ?.content
                ?: error("Could not find tab with active file content"),
            cursorLineNumber
        )

        val codeGenResponse = service<CodeGenService>().requestCodeGeneration(codeGenRequest)
        thisLogger().debug("code gen response: $codeGenResponse")
        // for some reason patch does not apply to an empty file
        val mockContent = "//mock content " + UUID.randomUUID()
        val fileContent = if (fileContentForCodeGen.content.trim().isEmpty()) {
            mockContent
        } else {
            fileContentForCodeGen.content
        }
        val generatedContentWithMockContent = applyPatchToContent(fileContent, codeGenResponse.patch)
        val generatedContent = generatedContentWithMockContent.replace(mockContent, "")
        val extractedSpan = generatedContent.extractSpan(
            codeGenResponse.spanOfInterest[0],
            codeGenResponse.spanOfInterest[1]
        )
        val fragments = ComparisonManager.getInstance().compareLines(
            fileContentForCodeGen.content,
            generatedContent,
            ComparisonPolicy.DEFAULT,
            DumbProgressIndicator.INSTANCE
        )
        return GeneratedCode(
            fullContentWithNoGeneratedCode = fileContentForCodeGen,
            fullContentWithGenerated = generatedContent,
            generatedCode = extractedSpan,
            startLine = codeGenResponse.spanOfInterest[0],
            endLine = codeGenResponse.spanOfInterest[1],
            fragments = fragments
        )
    }

    private fun validateCodeAndApply(
        context: FullContextForCodeGen,
        initialGeneratedCode: GeneratedCode,
        currentPsiFile: PsiFile,
        indicator: ProgressIndicator,
        requestId: String
    ): GeneratedCode {
        var lintRepairResponse: DiagnosticsResponse? = null
        var attemptCount = 0
        var someInternalIssueHappened = false

        var generatedCode: GeneratedCode = initialGeneratedCode

        while ((lintRepairResponse == null || lintRepairResponse.repairStatus == LintRepairStatus.CONTINUE.code) &&
            !someInternalIssueHappened &&
            !indicator.isCanceled
        ) {
            val fileService = project.service<FileService>()
            val fileCopy = fileService.makeTempFileWithSpecifiedContent(
                currentPsiFile.name,
                generatedCode.fullContentWithGenerated
            )
            val diagnosticsRunner = DiagnosticsRunner.of(project, fileCopy)
            val ideDiagnostics = diagnosticsRunner.runBasicDiagnostics()
                .filter { it.positionForRepairCalculationAvailable() }
                .map {
                    val errorMessage = ApplicationManager.getApplication().runReadAction<String> {
                        it.toString()
                    }
                    LintRequestDiagnostics(
                        message = errorMessage,
                        severity = "Warning",
                        source = context.commandContext.fileLanguage ?: "NULL",
                        code = "",
                        range = it.calculatePositionForRepairRequest(generatedCode.fullContentWithGenerated)
                    )
                }
                .filter { it.range[0].line >= generatedCode.startLine }
                .filter { it.range[1].line <= generatedCode.endLine }
                .toList()
            val filePath = ApplicationManager.getApplication().runReadAction<String> {
                currentPsiFile.virtualFile.path
            }
            val spanOfInterest = listOf(generatedCode.startLine, generatedCode.endLine)
            val file = DiagnosticsContextFile(
                project = "",
                path = filePath,
                origContent = generatedCode.fullContentWithNoGeneratedCode.content,
                patchText = diffMatchPatch.patchToText(
                    diffMatchPatch.patchMake(
                        generatedCode.fullContentWithNoGeneratedCode.content,
                        generatedCode.fullContentWithGenerated
                    )
                ),
                language = context.commandContext.fileLanguage ?: "NULL",
                spanOfInterest = spanOfInterest,
                additionalContext = DiagnosticsContext(
                    origDiagnostics = context.initialDiagnostics,
                    currDiagnostics = ideDiagnostics
                )
            )
            val lintRepairRequest = DiagnosticsRequest(
                requestId = requestId,
                attemptCount = attemptCount,
                file = file
            )
            try {
                lintRepairResponse = service<CodeRepairService>().anyLint(lintRepairRequest)
                val originalContent = generatedCode.fullContentWithNoGeneratedCode.content
                val repairedContent = applyPatchToContent(originalContent, lintRepairResponse.patch.patchText)
                val fragments = ComparisonManager.getInstance().compareLines(
                    originalContent,
                    repairedContent,
                    ComparisonPolicy.DEFAULT,
                    DumbProgressIndicator.INSTANCE
                )
                generatedCode = GeneratedCode(
                    fullContentWithNoGeneratedCode = generatedCode.fullContentWithNoGeneratedCode,
                    fullContentWithGenerated = repairedContent,
                    generatedCode = diff(originalContent, repairedContent),
                    startLine = lintRepairResponse.patch.spanOfInterest[0],
                    endLine = lintRepairResponse.patch.spanOfInterest[1],
                    fragments
                )
            } catch (e: Exception) {
                if (e is ProcessCanceledException) throw e
                someInternalIssueHappened = true
                thisLogger().warn(e)
            }
            attemptCount++
        }
        return generatedCode
    }

    private fun cleanup(currentPsiFile: PsiFile) {
        val fileService = project.service<FileService>()
        fileService.deleteTempCopyOfFileIfExists(currentPsiFile.name)
    }

    data class FullContextForCodeGen(
        val commandContext: CommandContext,
        val openTabs: List<TabItem>,
        val initialDiagnostics: List<LintRequestDiagnostics>
    )
}

interface UICodeGenReceiver {
    fun cancel()
    fun updateStatus(status: String)
}

class InlayCodeGenUIReceiver(
    private val project: Project,
    private val editor: Editor,
    private val currentPsiFile: PsiFile,
    private val cancelCallback: () -> Unit
) : UICodeGenReceiver {

    private val panelReference = Ref<CodeGenInlayPanel>()
    private val inlayReference = Ref<Disposable>()

    private val manager = EditorComponentInlaysManager.from(editor)
    private val currentLineNumber = editor.document.getLineNumber(editor.caretModel.offset)

    private val onEnter: (String) -> Unit = { prompt ->
        project.service<ai.zencoder.plugin.services.CodeGenService>().generateCodeWithChat(
            userQuery = prompt,
            editor,
            uiReceiver = this
        )
    }

    private val onCancel = {
        cancelCallback()
        inlayReference.get().dispose()
    }

    fun show() {
        val codeGenPanel = CodeGenInlayPanel(
            project,
            editor,
            onCloseCallback = onCancel,
            onEnterCallback = onEnter
        )
        panelReference.set(codeGenPanel)
        val insertionLineIndex = if (currentLineNumber > 0) {
            currentLineNumber - 1
        } else {
            0
        }
        invokeLater {
            val actualInlay = manager.insertAfter(insertionLineIndex, codeGenPanel)
            inlayReference.set(actualInlay)
            codeGenPanel.revalidate()
            codeGenPanel.getFocus()
        }
    }

    override fun cancel() {
        inlayReference.get().dispose()
    }

    override fun updateStatus(status: String) {
        panelReference.get().updateProgressBarTitle(status)
    }
}

data class GeneratedCode(
    val fullContentWithNoGeneratedCode: FileContentForCodeGen,
    val fullContentWithGenerated: String,
    val generatedCode: String,
    val startLine: Int,
    val endLine: Int,
    val fragments: List<LineFragment> = emptyList()
)

data class FileContentForCodeGen(
    val content: String,
    val lineToPlaceCode: Int
)

data class LineNumberAndCharacterOffset(
    val lineNumber: Int,
    val character: Int
)

fun diff(original: String, patched: String): String {
    val diffLines = StringBuilder()
    val originalLines = original.lines()
    val patchedLines = patched.lines()
    var originalIndex = 0
    for (line in patchedLines) {
        if (originalIndex >= originalLines.size || line != originalLines[originalIndex]) {
            diffLines.append("$line\n")
        } else {
            originalIndex++
        }
    }
    return diffLines.toString().trimEnd('\n') + "\n"
}

fun String.toSingleLineComment(prefix: String): String { // hack due to issues on backend
    return "$prefix${this.replace("\n", " ")}"
}

fun OpenedTab.toTabItem(): TabItem {
    return TabItem(
        path = this.path,
        content = this.content
    )
}

fun Int.calculatePositionIn(text: String): LineNumberAndCharacterOffset {
    val lines = text.lines()

    var currOffset = 0
    for ((index, line) in lines.withIndex()) {
        if (currOffset + line.length >= this) {
            return LineNumberAndCharacterOffset(
                lineNumber = index,
                character = this - currOffset
            )
        }
        currOffset += line.length + "\n".length
    }
    // return the last possible value if offset is out of bounds
    return LineNumberAndCharacterOffset(lines.lastIndex, lines.last().length)
}

fun String.extractSpan(leftBorder: Int, rightBorder: Int): String {
    return this.lines()
        .slice(leftBorder until rightBorder)
        .joinToString("\n")
}

fun Editor.getSelectedCodeSpan(): List<Int> {
    val selectionModel = this.selectionModel
    val document = this.document
    val startLineNumber = document.getLineNumber(selectionModel.selectionStart)
    val endLineNumber = document.getLineNumber(selectionModel.selectionEnd)
    thisLogger().debug("Codegen: selectionStart=$startLineNumber selectionEnd=$endLineNumber")

    //        todo python limitation on the protocol level TET-549
    val selectedCodeSpan = listOf(startLineNumber, endLineNumber + 1)
    return selectedCodeSpan
}

fun prepareAgentMessage(editor: Editor, prompt: String): UserMessage {
    val project = editor.project ?: error("Missing project")
    val language = editor.language
    val codeSnippet = editor.document.getText(TextRange(editor.selectionModel.selectionStart, editor.selectionModel.selectionEnd)).trim()

    val rawContent = JsonContent(
        type = Type.DOC,
        content = mutableListOf(
            JsonContent(
                type = Type.PARAGRAPH,
                content = mutableListOf(
                    JsonContent(type = Type.TEXT, text = prompt)
                )
            )
        )
    )

    if (codeSnippet.isNotEmpty()) {
        rawContent.content.add(
            JsonContent(
                type = Type.CODE_BLOCK,
                attrs = JsonObject(mapOf("language" to JsonPrimitive(language))),
                content = mutableListOf(JsonContent(type = Type.TEXT, text = codeSnippet))
            )
        )
    } else {
        val activeLineNumber = editor.document.getLineNumber(editor.selectionModel.selectionStart)

        if (activeLineNumber > 0) {
            rawContent.content.add(
                JsonContent(
                    type = Type.PARAGRAPH,
                    content = mutableListOf(JsonContent(type = Type.TEXT, text = "Currently selected file, line number: $activeLineNumber"))
                )
            )
        }
    }

    return UserMessage(
        rawContent = rawContent,
        content = convertToMessageContent(rawContent),
        context = ChatMessageContext(
            codebaseEnabled = true,
            currentFile = ChatAttachedFile(
                fsPath = editor.virtualFile.path,
                path = project.resolve(editor.virtualFile.path).toString(),
                content = editor.document.text
            )
        ),
        chatSettings = ChatSettings(
            isAgent = true
        )
    )
}
