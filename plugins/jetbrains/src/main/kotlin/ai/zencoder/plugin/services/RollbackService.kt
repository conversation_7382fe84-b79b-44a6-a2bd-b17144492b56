@file:Suppress("UnstableApiUsage")

package ai.zencoder.plugin.services

import ai.zencoder.plugin.webview.model.RollbackInfo
import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import com.intellij.history.LocalHistory
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.*
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import io.sentry.Sentry
import java.nio.file.Path
import java.util.concurrent.TimeUnit

@Service(Level.PROJECT)
class RollbackService(private val project: Project) {

    private val fileService get() = project.service<FileService>()

    fun getStoredContentBeforeTimestamp(virtualFile: VirtualFile, timestamp: Long): ByteArray? {
        return LocalHistory.getInstance().getByteContent(virtualFile) { date -> date <= timestamp }
    }

    private fun restoreFilesContentBeforeTimestamp(timestamp: Long, virtualFiles: List<VirtualFile>) {
        val fileByteContentsAndOriginalFiles = virtualFiles.map {
            Pair(it, getStoredContentBeforeTimestamp(it, timestamp))
        }
        invokeLater {
            saveAllDocuments()
            for (fileContentAndFile in fileByteContentsAndOriginalFiles) {
                if (fileContentAndFile.second != null) {
                    fileService.refreshFileContent(fileContentAndFile.first, fileContentAndFile.second!!)
                }
            }
        }
    }

    private fun removeFiles(virtualFiles: List<VirtualFile>) {
        invokeLater {
            saveAllDocuments()
            for (virtualFile in virtualFiles) {
                WriteCommandAction.runWriteCommandAction(project) {
                    try {
                        virtualFile.delete(this)
                    } catch (e: Exception) {
                        thisLogger().error("Error while deleting file $virtualFile", e)
                        Sentry.captureException(e)
                    }
                }
            }
        }
    }

    fun performRollback(rollbackInfo: RollbackInfo) {
        // Separate files to restore and files to remove based on isToRemove flag
        val filesToRestore = mutableListOf<VirtualFile>()
        val filesToRemove = mutableListOf<VirtualFile>()

        rollbackInfo.files.forEach { fileData ->
            val virtualFile = VirtualFileManager.getInstance().findFileByNioPath(Path.of(project.basePath + "/" + fileData.path))
            if (virtualFile != null) {
                if (fileData.isToRemove == true) {
                    filesToRemove.add(virtualFile)
                } else {
                    filesToRestore.add(virtualFile)
                }
            }
        }

        // Restore content for files that need to be restored
        if (filesToRestore.isNotEmpty()) {
            restoreFilesContentBeforeTimestamp(rollbackInfo.timestamp, filesToRestore)
        }

        // Remove files that need to be removed
        if (filesToRemove.isNotEmpty()) {
            removeFiles(filesToRemove)
        }
    }

    fun wasFileCreatedSinceTimestamp(timestamp: Long, virtualFile: VirtualFile): Boolean {
        val fileByteContent = LocalHistory.getInstance().getByteContent(virtualFile) { date -> date <= timestamp }
        return fileByteContent?.isEmpty() ?: true
    }

    private fun saveAllDocuments() = ApplicationManager.getApplication().runWriteAction {
        FileDocumentManager.getInstance().saveAllDocuments()
    }
}

@Service(Level.PROJECT)
class RollbackCachedService(val project: Project) {

    private val rollbackService = project.service<RollbackService>()

    // Required to prevent too many file index search and read operations in general for each "tick" in chat during agent session
    private val pathToIsNewStateCache: Cache<PathAndTimestamp, Boolean> = CacheBuilder.newBuilder()
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build()

    fun wasFileCreatedSinceTimestamp(timestamp: Long, relativePathToFile: String): Boolean =
        pathToIsNewStateCache.get(PathAndTimestamp(relativePathToFile, timestamp)) {
            val virtualFile = runReadAction {
                LocalFileSystem.getInstance().findFileByPath(project.basePath + "/" + relativePathToFile)
            }
            virtualFile?.let {
                rollbackService.wasFileCreatedSinceTimestamp(timestamp, it)
            } ?: false
        }
}

data class PathAndTimestamp(
    val path: String,
    val timestamp: Long
)
