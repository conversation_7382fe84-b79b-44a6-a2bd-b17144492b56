package ai.zencoder.plugin.context.resolve

import ai.zencoder.plugin.context.SimpleASTNode
import com.intellij.openapi.application.runReadAction
import com.intellij.psi.*
import com.intellij.psi.util.PsiFormatUtil
import com.intellij.psi.util.PsiFormatUtilBase
import kotlin.reflect.KClass

@Suppress("UnstableApiUsage")
class JavaNodeResolver : ASTNodeResolver<PsiClass, PsiMethod, PsiField, PsiFile> {
    override fun classElement(): KClass<PsiClass> = PsiClass::class

    override fun functionElement(): KClass<PsiMethod> = PsiMethod::class
    override fun fieldElement(): KClass<PsiField> = PsiField::class
    override fun fileElement(): KClass<PsiFile> = PsiFile::class

    override fun importsElement(): KClass<out PsiElement> = PsiImportList::class
    override fun extractClassSignature(element: PsiClass): SimpleASTNode? {
        if (element.name.isNullOrEmpty()) {
            return null
        }
        val options = PsiFormatUtilBase.SHOW_NAME or PsiFormatUtilBase.SHOW_EXTENDS_IMPLEMENTS
        val classLine = runReadAction { "${element.classKind.name.lowercase()} ${PsiFormatUtil.formatClass(element, options)}" }

        return SimpleASTNode.forClassHeader(
            psiElement = element,
            className = element.name,
            signature = classLine
        )
    }
    override fun extractMethodSignature(element: PsiMethod): SimpleASTNode? {
        if (element.name.isEmpty()) {
            return null
        }
        val options = PsiFormatUtilBase.SHOW_NAME or PsiFormatUtilBase.SHOW_PARAMETERS or PsiFormatUtilBase.SHOW_TYPE
        val formattedMethod = PsiFormatUtil.formatMethod(element, PsiSubstitutor.EMPTY, options, options)
        return SimpleASTNode.forMethod(
            psiElement = element,
            methodName = element.name,
            signature = formattedMethod
        )
    }

    override fun extractFieldSignature(element: PsiField): SimpleASTNode? {
        if (element.name.isEmpty()) {
            return null
        }
        return SimpleASTNode.forProperty(psiElement = element, propertyName = element.name, signature = element.text)
    }
}
