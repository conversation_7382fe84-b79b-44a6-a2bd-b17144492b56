@file:Suppress("unused", "UnusedVariable")
@file:OptIn(ExperimentalSerializationApi::class)

package ai.zencoder.plugin.mcp.service

import ai.zencoder.generated.client.all.models.ZencoderWorkflowToolkitType
import ai.zencoder.plugin.flags.zencoderFeatureFlags
import ai.zencoder.plugin.mcp.InternalToolsRegistry
import ai.zencoder.plugin.mcp.MCPServersStateManager
import ai.zencoder.plugin.mcp.McpServerInfo
import ai.zencoder.plugin.mcp.McpServerStatus
import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.mcp.sdk.CallToolResult
import ai.zencoder.plugin.mcp.sdk.McpError
import ai.zencoder.plugin.mcp.sdk.TextContent
import ai.zencoder.plugin.mcp.sdk.Tool
import ai.zencoder.plugin.mcp.sdk.server.Server
import ai.zencoder.plugin.utils.Os
import ai.zencoder.plugin.utils.getOs
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import io.github.classgraph.ClassGraph
import io.github.classgraph.ScanResult
import kotlinx.serialization.*
import kotlinx.serialization.json.*
import kotlin.reflect.*
import kotlin.reflect.full.*
import kotlin.reflect.jvm.jvmErasure

/**
 * Processor for MCP annotations.
 * This class scans for annotated classes and methods and registers them with the MCP server.
 * It also discovers and registers internal tools based on annotations.
 *
 * The main responsibilities of this class are:
 * 1. Scanning for classes and methods annotated with MCP annotations
 * 2. Registering tools, resources, prompts, and roots with the MCP server
 * 3. Discovering and registering internal tools with the InternalToolsRegistry
 */
object McpAnnotationProcessor {
    private val logger = thisLogger()

    /**
     * Registers all annotated handlers with the MCP server
     */
    fun registerAnnotatedHandlers(project: Project, server: Server) {
        logger.info("Registering annotated MCP handlers...")

        // Get all service classes in the project
        val serviceClasses = findServiceClasses(project)

        // Register tools
        registerTools(serviceClasses, server, project)

        // Register resources
        registerResources(serviceClasses, server, project)

        // Register prompts
        registerPrompts(serviceClasses, server, project)

        // Register roots
        registerRoots(serviceClasses, server, project)

        logger.info("Annotated MCP handlers registered successfully")
    }

    /**
     * Finds all service classes in the project
     * This method scans for classes annotated with @Service and also includes
     * explicitly registered service classes.
     *
     * It includes special handling for Windows environments where classpath scanning
     * might encounter issues with non-standard file systems.
     */
    private fun findServiceClasses(project: Project): List<KClass<*>> {
        val serviceClasses = mutableListOf<KClass<*>>()

        val isWindows = getOs() == Os.WINDOWS
        if (isWindows) {
            logger.info("Running on Windows - using enhanced classpath scanning configuration")
        }

        try {
            // 1. Find classes annotated with @Service using classpath scanning
            logger.info("Scanning for classes annotated with @Service")
            val serviceAnnotatedClasses = findClassesWithAnnotation(Service::class)
            serviceClasses.addAll(serviceAnnotatedClasses)
            logger.info("Found ${serviceAnnotatedClasses.size} classes annotated with @Service")

            // 2. Find classes annotated with @McpTool at the class level
            logger.info("Scanning for classes annotated with @McpTool")
            val mcpToolAnnotatedClasses = findClassesWithAnnotation(McpTool::class)
            serviceClasses.addAll(mcpToolAnnotatedClasses)
            logger.info("Found ${mcpToolAnnotatedClasses.size} classes annotated with @McpTool")

            // 3. Add explicitly registered service classes
            logger.info("Adding explicitly registered service classes")
            val explicitServices = getExplicitlyRegisteredServices()
            serviceClasses.addAll(explicitServices)
            logger.info("Added ${explicitServices.size} explicitly registered service classes")

            // Log the discovered service classes
            if (serviceClasses.isNotEmpty()) {
                logger.info("Found ${serviceClasses.size} total service classes:")
                serviceClasses.forEach { logger.debug("  - ${it.qualifiedName}") }
            } else {
                logger.warn("No service classes found - this may indicate a scanning issue")

                // If we're on Windows and no classes were found, this is likely due to the scanning issue
                if (isWindows) {
                    logger.warn("Windows environment detected with no classes found - this is likely due to a classpath scanning issue")
                    logger.info("Consider adding critical services to getExplicitlyRegisteredServices() method")
                }
            }
        } catch (e: Exception) {
            logger.warn("Error finding service classes", e)

            // Add explicitly registered services even if scanning fails
            logger.info("Adding explicitly registered services as fallback after scanning failure")
            serviceClasses.addAll(getExplicitlyRegisteredServices())
        }

        return serviceClasses
    }

    /**
     * Finds classes with a specific annotation using classpath scanning with ClassGraph
     */
    private fun findClassesWithAnnotation(annotationClass: KClass<out Annotation>): List<KClass<*>> {
        val result = mutableListOf<KClass<*>>()
        var scanResult: ScanResult? = null

        try {
            // Get all classes in the ai.zencoder.plugin package and subpackages
            scanResult = ClassGraph()
                .ignoreParentClassLoaders()
                .overrideClassLoaders(this.javaClass.classLoader)
                .enableAnnotationInfo() // Enable scanning for annotations
                .enableClassInfo() // Enable scanning for classes
                .acceptPackages("ai.zencoder.plugin") // Limit scan to our package
                .enableMemoryMapping()
                .enableRemoteJarScanning()
                .enableURLScheme("jimfs")
                .scan() // Perform the scan

            // Find classes with the specified annotation
            val annotatedClasses = scanResult
                .getClassesWithAnnotation(annotationClass.java.name)
                .loadClasses()

            // Convert to Kotlin classes and add to result
            for (clazz in annotatedClasses) {
                try {
                    result.add(clazz.kotlin)
                } catch (e: Exception) {
                    logger.warn("Error converting Java class to Kotlin class: ${clazz.name}", e)
                }
            }
        } catch (e: Exception) {
            logger.warn("Error scanning for classes with annotation: ${annotationClass.qualifiedName}", e)

            // Fallback to explicitly registered services if scanning fails
            logger.info("Using fallback mechanism for service discovery due to scanning failure")
        } finally {
            // Make sure to close the scan result to free resources
            scanResult?.close()
        }

        return result
    }

    /**
     * Returns explicitly registered service classes
     * This can be used to register services that might not be discovered through classpath scanning
     * or as a fallback when scanning fails (especially on Windows)
     */
    private fun getExplicitlyRegisteredServices(): List<KClass<*>> {
        // Add your critical service classes here that must be registered
        // even if classpath scanning fails

        // This is especially important for Windows environments where
        // classpath scanning might encounter issues with non-standard file systems

        // Example: If you know certain classes should always be registered,
        // uncomment and add them to this list

        return listOf(
            // Add critical services that should always be registered
            // MyService::class,
            // AnotherService::class
        )
    }

    /**
     * Registers tools from annotated classes and methods
     */
    private fun registerTools(
        classes: List<KClass<*>>,
        server: Server,
        project: Project
    ) {
        for (clazz in classes) {
            // Check if the class is annotated with @McpTool
            clazz.findAnnotation<McpTool>()?.let { annotation ->
                registerClassAsTool(clazz, annotation, server, project)
            }

            // Check methods for @McpTool annotation
            for (method in clazz.functions) {
                method.findAnnotation<McpTool>()?.let { annotation ->
                    registerMethodAsTool(clazz, method, annotation, server, project)
                }
            }
        }
    }

    /**
     * If a class is annotated with @McpTool, we treat that class as a single tool with multiple sub-commands.
     * Each public method becomes a separate command inside the main class tool.
     * This method registers the tool with both the MCP server and the InternalToolsRegistry.
     */
    private fun registerClassAsTool(
        clazz: KClass<*>,
        annotation: McpTool,
        server: Server,
        project: Project
    ) {
        val toolName = annotation.name
        val toolDescription = annotation.description

        // Register with InternalToolsRegistry
        val toolInfo = McpServerInfo(
            name = annotation.displayName.ifEmpty { annotation.name.replace("_", " ") },
            description = annotation.shortDescription.ifEmpty { annotation.description },
            author = ZencoderWorkflowToolkitType.ZENCODER_MCP_CLIENT.value,
            status = McpServerStatus(status = ToolStatus.LIBRARY),
            tools = listOf(annotation.name)
        )

        project.service<InternalToolsRegistry>().registerTool(toolName, toolInfo)

        val (methodMap, input) = createParametersForClass(clazz)

        // Register the single "class tool" with the server.
        // The closure will handle dispatch to the correct method.
        /*server.addTool(toolName, toolDescription, input) { params ->
            // 1) Extract the chosen command (method name)
            val commandName = params["command"]?.jsonPrimitive?.contentOrNull
            if (commandName == null) {
                return@addTool JsonObject(mapOf("error" to JsonPrimitive("Missing 'command'")))
            }
            val targetMethod = methodMap[commandName]
            if (targetMethod == null) {
                return@addTool JsonObject(mapOf("error" to JsonPrimitive("Unknown command '$commandName'")))
            }

            // 2) Retrieve the relevant parameters from "arguments"
            val rawArgs = params["arguments"]?.jsonObject ?: buildJsonObject {}

            // 3) Convert that JSON to the method's parameter types.
            // For now, we do a placeholder. In a real implementation, you'd parse rawArgs
            // against the schema for that method, call the method via reflection, etc.
            // Something like:
            // val resultValue = invokeMethodWithParams(clazz, targetMethod, rawArgs)

            // We'll do a placeholder:
            return@addTool JsonObject(mapOf("result" to JsonPrimitive("Executed command '$commandName' on class tool '$toolName'")))
        }*/

        logger.info("Registered class as tool with sub-commands: $toolName")
    }

    /**
     * Registers a method as a tool
     * This method registers the tool with both the MCP server and the InternalToolsRegistry.
     */
    private fun registerMethodAsTool(
        clazz: KClass<*>,
        method: KFunction<*>,
        annotation: McpTool,
        server: Server,
        project: Project
    ) {
        val toolName = annotation.name
        val toolDescription = annotation.description

        if (!zencoderFeatureFlags.enableCodeReferenceTool && (toolName == "find_usages" || toolName == "find_declarations")) {
            return
        }

        val toolInfo = McpServerInfo(
            id = toolName,
            name = annotation.displayName.ifEmpty { annotation.name.replace("_", " ") },
            description = annotation.shortDescription.ifEmpty { annotation.description },
            author = ZencoderWorkflowToolkitType.ZENCODER_MCP_CLIENT.value,
            status = McpServerStatus(status = ToolStatus.INSTALLED),
            tools = listOf(annotation.name)
        )

        project.service<InternalToolsRegistry>().registerTool(toolName, toolInfo)
        project.service<MCPServersStateManager>().setInternalToolState(toolName, true, project.name)

        // Register the tool with the MCP server
        server.addTool(toolName, toolDescription, createParametersForMethod(method)) { params ->
            try {
                // Parse and invoke the method, then produce the result JSON
                val resultJson = callMethodUsingKotlinx(method, clazz, params.arguments, project)

                // Return the result directly without wrapping it in a "result" field
                CallToolResult(content = listOf(TextContent(Json.encodeToString(resultJson))))
            } catch (ex: McpError) {
                logger.warn("McpError received while invoking tool method: $toolName", ex)

                throw ex
            } catch (ex: Throwable) {
                val cause = ex.cause
                when (cause) {
                    is McpError -> {
                        logger.warn("McpError cause received while invoking tool method: $toolName", ex)
                        throw cause
                    }

                    else -> {
                        logger.warn("Error invoking tool method: $toolName", ex)
                        throw ex
                    }
                }
            }
        }

        logger.info("Registered method as tool: $toolName")
    }

    /**
     * Registers resources from annotated classes and methods
     */
    private fun registerResources(
        classes: List<KClass<*>>,
        server: Server,
        project: Project
    ) {
        for (clazz in classes) {
            // Check if the class is annotated with @McpResource
            clazz.findAnnotation<McpResource>()?.let { annotation ->
                registerClassAsResource(clazz, annotation, server, project)
            }

            // Check methods for @McpResource annotation
            for (method in clazz.functions) {
                method.findAnnotation<McpResource>()?.let { annotation ->
                    registerMethodAsResource(clazz, method, annotation, server, project)
                }
            }
        }
    }

    /**
     * Registers a class as a resource
     */
    private fun registerClassAsResource(
        clazz: KClass<*>,
        annotation: McpResource,
        server: Server,
        project: Project
    ) {
        val resourceName = annotation.name
        val resourceDescription = annotation.description

        logger.info("Registered class as resource: $resourceName")

        // In a real implementation, you would register the resource with the server
        // For now, we'll just log it
    }

    /**
     * Registers a method as a resource
     */
    private fun registerMethodAsResource(
        clazz: KClass<*>,
        method: KFunction<*>,
        annotation: McpResource,
        server: Server,
        project: Project
    ) {
        val resourceName = annotation.name
        val resourceDescription = annotation.description

        logger.info("Registered method as resource: $resourceName")

        // In a real implementation, you would register the resource with the server
        // For now, we'll just log it
    }

    /**
     * Registers prompts from annotated classes and methods
     */
    private fun registerPrompts(
        classes: List<KClass<*>>,
        server: Server,
        project: Project
    ) {
        for (clazz in classes) {
            // Check if the class is annotated with @McpPrompt
            clazz.findAnnotation<McpPrompt>()?.let { annotation ->
                registerClassAsPrompt(clazz, annotation, server, project)
            }

            // Check methods for @McpPrompt annotation
            for (method in clazz.functions) {
                method.findAnnotation<McpPrompt>()?.let { annotation ->
                    registerMethodAsPrompt(clazz, method, annotation, server, project)
                }
            }
        }
    }

    /**
     * Registers a class as a prompt
     */
    private fun registerClassAsPrompt(
        clazz: KClass<*>,
        annotation: McpPrompt,
        server: Server,
        project: Project
    ) {
        val promptName = annotation.name
        val promptDescription = annotation.description

        logger.info("Registered class as prompt: $promptName")

        // TODO<@andrei-laiff>: Implement registration of prompts
    }

    /**
     * Registers a method as a prompt
     */
    private fun registerMethodAsPrompt(
        clazz: KClass<*>,
        method: KFunction<*>,
        annotation: McpPrompt,
        server: Server,
        project: Project
    ) {
        val promptName = annotation.name
        val promptDescription = annotation.description

        logger.info("Registered method as prompt: $promptName")

        // TODO<@andrei-laiff>: Implement registration of prompts
    }

    /**
     * Registers roots from annotated classes
     */
    private fun registerRoots(
        classes: List<KClass<*>>,
        server: Server,
        project: Project
    ) {
        for (clazz in classes) {
            // Check if the class is annotated with @McpRoot
            clazz.findAnnotation<McpRoot>()?.let { annotation ->
                registerClassAsRoot(clazz, annotation, server, project)
            }
        }
    }

    /**
     * Registers a class as a root
     */
    private fun registerClassAsRoot(
        clazz: KClass<*>,
        annotation: McpRoot,
        server: Server,
        project: Project
    ) {
        val rootName = annotation.name
        val rootDescription = annotation.description

        logger.info("Registered class as root: $rootName")

        // TODO<@andrei-laiff>: Implement registration of roots
    }

    private fun createParametersForClass(clazz: KClass<*>): Pair<Map<String, KFunction<*>>, Tool.Input> {
        // Gather all public, non-abstract methods
        val methods = clazz.declaredMemberFunctions.filter {
            it.visibility == KVisibility.PUBLIC && !it.isAbstract
        }

        // Build a map of method name -> the method reflection
        val methodMap = methods.associateBy { it.name }

        val methodNames = methods.map { it.name }

        val properties = buildJsonObject {
            // "command" is a string that enumerates possible method names
            put(
                "command",
                buildJsonObject {
                    put("type", JsonPrimitive("string"))
                    put("enum", JsonArray(methodNames.map { JsonPrimitive(it) }))
                }
            )
            put(
                "arguments",
                buildJsonObject {
                    put("type", JsonPrimitive("object"))
                }
            )
        }

        return methodMap to Tool.Input(
            properties = properties,
            required = listOf("command", "arguments")
        )
    }

    /**
     * Creates parameter definitions for a method
     */
    private fun createParametersForMethod(method: KFunction<*>): Tool.Input {
        return generateJsonSchemaForFunction(method)
    }

    @OptIn(ExperimentalStdlibApi::class)
    private suspend fun callMethodUsingKotlinx(
        method: KFunction<*>,
        clazz: KClass<*>,
        params: JsonObject,
        project: Project
    ): JsonElement {
        // 1) Build (or get) the instance if it’s not a static function
        val instanceParam = method.instanceParameter
        val extensionParam = method.extensionReceiverParameter

        val argumentMap = mutableMapOf<KParameter, Any?>()

        if (instanceParam != null) {
            argumentMap[instanceParam] = createOrGetInstance(clazz, project)
        }
        if (extensionParam != null) {
            TODO()
            // handle extension receiver if your method is an extension function
            // argumentMap[extensionParam] = ...
        }

        // 2) Iterate over the method's parameters
        method.valueParameters.forEach { param ->
            // 3) Obtain a KSerializer for that parameter type
            val serializer = serializerOrNull(param.type)
                ?: throw IllegalStateException("No serializer found for parameter type: ${param.type}")
            // 4) Look up the corresponding JSON element in the request params
            val paramName = param.name
                ?: throw IllegalStateException("Cannot determine parameter name: ${param.type}")
            // 5) Ensure the JSON element exists in the request params
            val paramElement = if (method.valueParameters.size == 1) {
                params
            } else {
                params[paramName]
                    ?: throw IllegalStateException("Required parameter '${param.name}' missing from request params")
            }
            // 6) Decode the JSON into the appropriate Kotlin type
            val decodedParamValue = Json.decodeFromJsonElement(serializer, paramElement)

            argumentMap[param] = decodedParamValue
        }

        // 7) Invoke the method (suspend or not)
        val callResult = if (method.isSuspend) {
            method.callSuspendBy(argumentMap)
        } else {
            method.callBy(argumentMap)
        } ?: throw IllegalStateException("Result is null after calling method: ${method.name}")

        // 8) Obtain a KSerializer for the return type
        val serializer = serializerOrNull(method.returnType)
            ?: throw IllegalStateException("No serializer found for parameter type: ${method.returnType}")

        // 9) Convert the result to JSON
        return Json.encodeToJsonElement(serializer, callResult)
    }

    /**
     * Creates or gets an instance of the given class.
     *
     * If the class is an object singleton, returns the singleton instance.
     * If the class is a service, gets the service instance from the project.
     * Otherwise, attempts to create a new instance.
     */
    private fun createOrGetInstance(kClass: KClass<*>, project: Project): Any {
        // If it's an object singleton, just use objectInstance
        kClass.objectInstance?.let { return it }

        // If it's a service class, get it from the project
        if (kClass.hasAnnotation<Service>()) {
            // Get the service level from the annotation
            val serviceAnnotation = kClass.findAnnotation<Service>()
            val level = serviceAnnotation?.value[0]?.ordinal ?: Service.Level.PROJECT.ordinal

            return when (level) {
                Service.Level.PROJECT.ordinal -> {
                    // For project-level services, use project.getService()
                    logger.info("Getting project-level service: ${kClass.qualifiedName}")
                    try {
                        // Use reflection to call the generic getService method
                        val getServiceMethod = Project::class.java.getMethod("getService", Class::class.java)
                        getServiceMethod.invoke(project, kClass.java)
                    } catch (e: Exception) {
                        logger.warn("Error getting project-level service: ${kClass.qualifiedName}", e)
                        // Fallback to creating a new instance
                        kClass.createInstance()
                    }
                }

                Service.Level.APP.ordinal -> {
                    // For application-level services, use ApplicationManager.getApplication().getService()
                    logger.info("Getting application-level service: ${kClass.qualifiedName}")
                    try {
                        // Get the Application instance and call getService
                        val application = ApplicationManager.getApplication()
                        val getServiceMethod = application.javaClass.getMethod("getService", Class::class.java)
                        getServiceMethod.invoke(application, kClass.java)
                    } catch (e: Exception) {
                        logger.warn("Error getting application-level service: ${kClass.qualifiedName}", e)
                        // Fallback to creating a new instance
                        kClass.createInstance()
                    }
                }

                else -> {
                    // For other levels, try to create a new instance
                    logger.info("Creating new instance for service with unknown level: ${kClass.qualifiedName}")
                    kClass.createInstance()
                }
            }
        }

        // Otherwise, attempt to create a new instance
        logger.info("Creating new instance: ${kClass.qualifiedName}")
        return kClass.createInstance()
    }
}

private fun buildJsonFromMcpError(e: McpError): JsonObject {
    val error = buildJsonObject {
        putJsonObject("error") {
            put("code", JsonPrimitive(e.code))
            put("message", JsonPrimitive(e.message))
            putJsonObject("data") {
                put("cause", JsonPrimitive(e.cause?.message ?: ""))
            }
        }
    }
    return error
}

/**
 * Primary entry point:
 * Build a JSON Schema describing the Kotlin function's parameters.
 *
 * - Each parameter becomes a property in the top-level "properties".
 * - The entire function's signature is described as an object with "type":"object".
 */
fun generateJsonSchemaForFunction(kFunction: KFunction<*>): Tool.Input {
    // Keep track of required parameter names
    val requiredProps = mutableListOf<String>()
    val properties = buildJsonObject {
        when (kFunction.valueParameters.size == 1) {
            true -> {
                val parameter = kFunction.valueParameters.single()
                val paramName = parameter.name ?: return@buildJsonObject
                // Recursively build a schema snippet for this parameter
                val paramSchema = generateJsonSchemaForType(parameter.type)

                when (paramSchema is JsonObject) {
                    // If the parameter itself is a complex object, include its properties
                    paramSchema.jsonObject["properties"]?.jsonObject?.isNotEmpty() -> {
                        paramSchema.jsonObject["properties"]?.jsonObject?.forEach { (paramName, paramSchema) ->
                            put(paramName, paramSchema)
                        }
                    }

                    else -> {}
                }

                val paramRequired = when (paramSchema is JsonObject) {
                    paramSchema.jsonObject["required"]?.jsonArray?.isNotEmpty() ->
                        paramSchema.jsonObject["required"]?.jsonArray?.mapNotNull {
                            when (it is JsonPrimitive) {
                                true -> it.content
                                else -> null
                            }
                        }?.toList() ?: listOf()

                    else -> listOf()
                }

                // Typically, function parameters are considered required
                requiredProps.addAll(paramRequired)
            }

            false -> {
                for (parameter in kFunction.valueParameters) {
                    val paramName = parameter.name ?: continue
                    // Recursively build a schema snippet for this parameter
                    val paramSchema = generateJsonSchemaForType(parameter.type)

                    // Add it to "properties"
                    put(paramName, paramSchema)

                    // Typically, function parameters are considered required
                    requiredProps.add(paramName)
                }
            }
        }
    }

    return Tool.Input(properties = properties, required = requiredProps)
}

/**
 * Recursively build a JSON schema "snippet" for a given Kotlin type.
 * - Primitives: string, boolean, integer, number
 * - Collections: array (with "items" describing element type)
 * - Objects: object (with "properties" describing each property), if data class or user-defined
 */
fun generateJsonSchemaForType(kType: KType): JsonElement {
    val kClass = kType.jvmErasure

    // 1) Handle simple primitives
    return when {
        kClass == String::class -> buildTypeNode("string")
        kClass == Boolean::class -> buildTypeNode("boolean")
        kClass == Int::class ||
            kClass == Long::class ||
            kClass == Short::class -> buildTypeNode("integer")

        kClass == Float::class ||
            kClass == Double::class -> buildTypeNode("number")

        // 2) Handle Collection / Array
        isCollectionType(kClass) -> {
            val itemType = kType.arguments.firstOrNull()?.type
            buildJsonObject {
                put("type", JsonPrimitive("array"))
                if (itemType != null) {
                    put("items", generateJsonSchemaForType(itemType))
                } else {
                    // Fallback if generic type is unknown
                    put("items", buildJsonObject {})
                }
            }
        }

        // 3) Otherwise, treat it as an object. If it's a data class or user-defined type,
        //    we recursively reflect on its properties.
        else -> {
            buildJsonObject {
                put("type", JsonPrimitive("object"))

                // If it's a user data class or similarly “plain” class, reflect its properties
                if (kClass.isData || isUserClass(kClass)) {
                    val requiredProps = mutableListOf<String>()
                    val propsObj = buildJsonObject {
                        // Use reflection on declaredMemberProperties
                        for (prop in kClass.declaredMemberProperties) {
                            val propSchema = generateJsonSchemaForType(prop.returnType)
                            put(prop.name, propSchema)
                            requiredProps.add(prop.name)
                        }
                    }
                    put("properties", propsObj)
                    put("required", buildJsonArray { addAll(requiredProps) })
                }
            }
        }
    }
}

/**
 * Build a JsonObject that has "type": <something>
 */
fun buildTypeNode(typeStr: String): JsonObject = buildJsonObject {
    put("type", JsonPrimitive(typeStr))
}

/**
 * A small helper to decide if a class is "Collection-like" (List, Set, Array).
 */
fun isCollectionType(klass: KClass<*>): Boolean = (
    List::class.starProjectedType.isSupertypeOf(klass.starProjectedType)
        || Set::class.starProjectedType.isSupertypeOf(klass.starProjectedType)
        || klass.java.isArray
    )

/**
 * A small helper to figure out if a class might be your “user-defined” or “plain” object.
 * Adjust as you see fit (you might skip if it's in java.* or kotlin.* packages, etc.)
 */
fun isUserClass(klass: KClass<*>): Boolean {
    // For the sake of example, let's just skip standard library classes
    val pkgName = klass.qualifiedName ?: return false
    return !pkgName.startsWith("kotlin.") && !pkgName.startsWith("java.")
}

/**
 * Try to obtain a KSerializer for the given KType.
 *
 * Kotlinx has a built-in API to get a serializer from a KType
 * => Need "kotlinx.serialization:typeOf()" + "serializer(kType)" or "serializerOrNull"
 * but that’s an experimental API.
 * => Instead, e.g. 'Json.serializersModule.serializer(kType)' in many setups
 */
fun serializerOrNull(kType: KType): KSerializer<Any>? {
    // Creates or retrieves a KSerializer for the given KType, if possible
    return try {
        val serializer = Json.serializersModule.serializer(kType)
        @Suppress("UNCHECKED_CAST")
        serializer as KSerializer<Any>
    } catch (_: SerializationException) {
        null
    }
}
