package ai.zencoder.plugin.agents

import ai.zencoder.plugin.mcp.McpServerInfo
import com.intellij.util.xmlb.Converter
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

typealias AgentId = String

@Serializable
data class Agent(
    val id: AgentId,
    val name: String,
    val commandName: String? = null,
    val command: String,
    val codeLens: Boolean? = null,
    val repair: Boolean? = null,
    val rag: Boolean? = null,
    val creationDate: String? = null,
    val lastModifiedDate: String? = null,
    val tools: List<McpServerInfo>? = null,
    val author: String? = null,
    val isSharedWithOrganization: Boolean? = null
)

class AgentsMapConverter : Converter<MutableMap<AgentId, Agent>>() {
    private val json = Json {
        ignoreUnknownKeys = true
        explicitNulls = false
    }

    override fun toString(value: MutableMap<AgentId, Agent>): String {
        return json.encodeToString(value)
    }

    override fun fromString(value: String): MutableMap<AgentId, Agent> {
        return json.decodeFromString<MutableMap<AgentId, Agent>>(value)
    }
}
