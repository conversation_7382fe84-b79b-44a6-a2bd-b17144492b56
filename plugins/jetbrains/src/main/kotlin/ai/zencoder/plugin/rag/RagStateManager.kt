package ai.zencoder.plugin.rag

import com.intellij.openapi.components.*
import com.intellij.openapi.components.Service.Level

@Service(Level.PROJECT)
@State(
    name = "ai.zencoder.plugin.rag.state",
    storages = [Storage(StoragePathMacros.WORKSPACE_FILE)]
)
class RagStateManager : SimplePersistentStateComponent<ProjectRagState>(ProjectRagState()) {
    val noUploads
        get() = state.repositoryId == null

    val hasUploads
        get() = !noUploads

    val repositoryId
        get() = state.repositoryId

    val isInProgress
        get() = state.state == WebviewIndexingStatus.UPDATING_INDEX || state.state == WebviewIndexingStatus.SENDING_FILES ||
            state.state == WebviewIndexingStatus.ANALYZING_REPO

    val isError
        get() = state.state == WebviewIndexingStatus.INDEXING_FAILED

    override fun loadState(state: ProjectRagState) {
        super.loadState(state)

        state.indexedFilesMap = state.indexedFilesMap.filterValues { it != Long.MAX_VALUE }
    }
}

class ProjectRagState : BaseState() {
    var indexedFilesMap = mapOf<String, Long>()
    var lastCommitHash by string()
    var repositoryId by string()
    var lastIndexTimestamp by property(0L)
    var lastError by string()
    var repoTotalFiles by property(1000)
    var repoAccumulatedChanges by property(0)
    var state by enum(WebviewIndexingStatus.NOT_INDEXED_YET)
}
