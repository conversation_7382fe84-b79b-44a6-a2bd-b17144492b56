package ai.zencoder.plugin.diff

import ai.zencoder.plugin.model.path.RelativePathFromProjectRoot
import ai.zencoder.plugin.services.FileService
import ai.zencoder.plugin.services.RollbackService
import ai.zencoder.plugin.utils.showError
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.model.ChatId
import com.intellij.diff.editor.ChainDiffVirtualFile
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFileManager
import io.sentry.Sentry
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.nio.file.Path
import java.util.*

@Service(PROJECT)
class DiffOpener(
    val project: Project,
    val scope: CoroutineScope
) {

    private val fileService get() = project.service<FileService>()
    private val openDiffIntentAssociatedWithUniqueFileNames = mutableMapOf<OpenDiffFromChatIntent, String>()
    private val chatManager get() = project.service<ChatManager>()
    private val fileEditorManager get() = project.service<FileEditorManager>()
    private val rollbackService get() = project.service<RollbackService>()

    fun openFromChat(intent: OpenDiffFromChatIntent) {
        val diffPanelNameAssociatedToThisIntent = getNewPanelNameOrOpenExisting(intent)
        if (diffPanelNameAssociatedToThisIntent == null) {
            return
        }

        val messageId = intent.messageId
        val relativeFilePath = RelativePathFromProjectRoot(intent.filePath)
        val fileName = intent.filePath.substringAfterLast("/")
        val initialFileContent = fileService.getFileContentAsString(relativeFilePath)
        val updatedFileContent = applyPatchToContent(initialFileContent, intent.patch)

        val file = runReadAction {
            LocalFileSystem.getInstance().findFileByPath("${project.basePath}/$relativeFilePath")
        }

        invokeLater {
            UnifiedDiffTool(
                project,
                relativeFilePath,
                file,
                null,
                updatedFileContent,
                afterApply = { result ->
                    scope.launch {
                        result.virtualFile?.let { invokeLater { fileEditorManager.openFile(it, true) } }
                        if (messageId != null) {
                            chatManager.updateDiffAppliedStatus(
                                chatId = intent.chatId,
                                messageId = messageId,
                                diffFilePath = intent.filePath,
                                isApplied = true
                            )
                        }
                    }
                },
                afterCancel = {},
                title = "Diff: $fileName",
                uniqueDiffFileName = diffPanelNameAssociatedToThisIntent,
                scope = scope
            ).showDiff()
        }
    }

    fun openAppliedDiffFromChat(intent: OpenDiffFromChatIntent) {
        val diffPanelNameAssociatedToThisIntent = getNewPanelNameOrOpenExisting(intent)
        if (diffPanelNameAssociatedToThisIntent == null) {
            return
        }

        val messageId = intent.messageId
        if (messageId == null) {
            thisLogger().error("messageId not found")
            showError("Could not find message associated with this diff")
            return
        }
        val chat = chatManager.getChatById(intent.chatId)
        val message = chat.messages.firstOrNull { it.id == messageId } ?: return
        val isRevertible = chat.findRevertibleAssistantMessage()?.id == messageId
        val mode = if (isRevertible) {
            if (intent.isNew) DiffViewerMode.REMOVE_FILE else DiffViewerMode.REVERT_DIFF
        } else {
            DiffViewerMode.VIEW_ONLY
        }

        val relativeFilePath = RelativePathFromProjectRoot(intent.filePath)
        val fileName = intent.filePath.substringAfterLast("/")
        val absoluteFilePath = "${project.basePath}/$relativeFilePath"
        val virtualFile = VirtualFileManager.getInstance().findFileByNioPath(Path.of(absoluteFilePath))
        val initialFileContentRaw = virtualFile?.let { rollbackService.getStoredContentBeforeTimestamp(it, message.createdAt) }
        if (initialFileContentRaw == null && mode == DiffViewerMode.REVERT_DIFF) {
            if (virtualFile == null) {
                thisLogger().warn("Could not find file at $absoluteFilePath")
                showError("Could not find file at $absoluteFilePath")
            } else {
                thisLogger().warn(
                    "No initial content found for file ${relativeFilePath.actualPath}. Opening actual file instead of diff viewer"
                )
                invokeLater {
                    fileEditorManager.openFile(virtualFile, true)
                }
            }
            return
        }

        val initialFileContent = if (initialFileContentRaw != null) String(initialFileContentRaw, virtualFile.charset) else ""

        val updatedFileContent = applyPatchToContent(initialFileContent, intent.patch)

        val file = runReadAction {
            LocalFileSystem.getInstance().findFileByPath(absoluteFilePath)
        }

        invokeLater {
            UnifiedDiffTool(
                project,
                relativeFilePath,
                file,
                initialFileContent,
                updatedFileContent,
                mode,
                afterApply = { result ->
                    scope.launch {
                        result.virtualFile?.let { invokeLater { fileEditorManager.openFile(it, true) } }
                        chatManager.updateDiffAppliedStatus(
                            chatId = intent.chatId,
                            messageId = messageId,
                            diffFilePath = intent.filePath,
                            isApplied = false
                        )
                    }
                },
                afterCancel = {},
                title = "Diff: $fileName",
                uniqueDiffFileName = diffPanelNameAssociatedToThisIntent,
                scope = scope
            ).showDiff()
        }
    }

    private fun getNewPanelNameOrOpenExisting(intent: OpenDiffFromChatIntent): String? {
        val diffPanelNameAssociatedToThisIntent = openDiffIntentAssociatedWithUniqueFileNames.getOrPut(intent) {
            UUID.randomUUID().toString()
        }
        val alreadyOpenedTabForThisIntent = fileEditorManager.allEditors.firstOrNull {
            it.file is ChainDiffVirtualFile && it.file.name == diffPanelNameAssociatedToThisIntent
        }
        if (alreadyOpenedTabForThisIntent != null) {
            invokeLater {
                fileEditorManager.openFile(alreadyOpenedTabForThisIntent.file)
            }
            return null
        }
        return diffPanelNameAssociatedToThisIntent
    }

    fun closeIfOpened(
        chatId: ChatId,
        messageId: String?,
        filePath: String,
        patch: String,
        isNew: Boolean
    ) {
        try {
            val alreadyOpenedTabUniqueFileName =
                openDiffIntentAssociatedWithUniqueFileNames[OpenDiffFromChatIntent(chatId, messageId, filePath, patch, isNew)]
            val alreadyOpenedTab = fileEditorManager.allEditors.firstOrNull {
                it.file is ChainDiffVirtualFile && it.file.name == alreadyOpenedTabUniqueFileName
            }
            if (alreadyOpenedTab != null) {
                invokeLater {
                    fileEditorManager.closeFile(alreadyOpenedTab.file)
                }
            }
        } catch (e: Exception) {
            thisLogger().warn("Could not close the tab: ", e)
            Sentry.captureException(e)
        }
    }
}

data class OpenDiffFromChatIntent(
    val chatId: ChatId,
    val messageId: String?,
    val filePath: String,
    val patch: String,
    val isNew: Boolean
)
