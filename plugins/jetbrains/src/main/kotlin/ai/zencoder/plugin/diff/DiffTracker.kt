@file:Suppress("SpellCheckingInspection")

package ai.zencoder.plugin.diff

import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.webview.model.ChatId
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

@Service(Level.APP)
class DiffTracker(val scope: CoroutineScope) {

    fun trackDiffOnShow(
        originalContent: String,
        generatedContent: String,
        fileType: String
    ) {
        scope.launch {
            val diffCounts = computeDiffCounts(originalContent, generatedContent)
            track(
                "Diff opened",
                "file_type" to fileType,
                "codeblock_lines_removed" to diffCounts.deletions,
                "codeblock_lines_added" to diffCounts.additions
            )
        }
    }

    fun trackDiffAfterApplyingChanges(
        originalContent: String,
        generatedContent: String,
        appliedContent: String,
        fileType: String,
        mode: DiffViewerMode = DiffViewerMode.APPLY_DIFF
    ) {
        scope.launch {
            val appliedDiffCounts = computeDiffCounts(originalContent, appliedContent)
            val redactedDiffCounts = computeDiffCounts(generatedContent, appliedContent)

            track(
                "Diff accepted",
                "file_type" to fileType,
                "codeblock_lines_removed" to appliedDiffCounts.deletions,
                "codeblock_lines_added" to appliedDiffCounts.additions,
                "mode" to mode.toAnalyticsString()
            )

            if (redactedDiffCounts != DiffCounts(0, 0)) {
                track(
                    "Diff codeblock reverted",
                    "file_type" to fileType,
                    "codeblock_lines_removed" to redactedDiffCounts.deletions,
                    "codeblock_lines_added" to redactedDiffCounts.additions
                )
            }
        }
    }

    fun trackDiffAfterRevertingChanges(
        originalContent: String,
        generatedContent: String,
        fileType: String,
        mode: DiffViewerMode
    ) {
        scope.launch {
            val appliedDiffCounts = computeDiffCounts(originalContent, generatedContent)

            track(
                "Diff reverted",
                "file_type" to fileType,
                "codeblock_lines_removed" to appliedDiffCounts.deletions,
                "codeblock_lines_added" to appliedDiffCounts.additions,
                "mode" to mode.toAnalyticsString()
            )
        }
    }

    fun trackDiffRejected(
        originalContent: String,
        generatedContent: String,
        fileType: String,
        mode: DiffViewerMode
    ) {
        scope.launch {
            val appliedDiffCounts = computeDiffCounts(originalContent, generatedContent)

            track(
                "Diff rejected",
                "file_type" to fileType,
                "codeblock_lines_removed" to appliedDiffCounts.deletions,
                "codeblock_lines_added" to appliedDiffCounts.additions,
                "mode" to mode.toAnalyticsString()
            )
        }
    }

    fun trackDiffOnGenerated(
        originalContent: String,
        generatedContent: String,
        fileType: String
    ) {
        scope.launch {
            val diffCounts = computeDiffCounts(originalContent, generatedContent)
            track(
                "Diff generated",
                "file_type" to fileType,
                "codeblock_lines_removed" to diffCounts.deletions,
                "codeblock_lines_added" to diffCounts.additions
            )
        }
    }

    fun trackApplyAll(numberOfFiles: Int) {
        track(
            "Diff Apply All clicked",
            "num_files" to numberOfFiles
        )
    }

    fun trackRevertAll(
        chatId: ChatId,
        messageId: String,
        numberOfFiles: Int
    ) {
        track(
            "Diff Revert all clicked",
            "chat_id" to chatId,
            "message_id" to messageId,
            "num_files" to numberOfFiles
        )
    }

    fun trackRevertToCheckpoint(chatId: ChatId, messageId: String? = null) {
        track(
            "Diff Revert to Checkpoint clicked",
            "chat_id" to chatId,
            "message_id" to messageId
        )
    }

    private fun DiffViewerMode.toAnalyticsString(): String = when (this) {
        DiffViewerMode.APPLY_DIFF -> "applyDiff"
        DiffViewerMode.REVERT_DIFF -> "revertDiff"
        DiffViewerMode.REMOVE_FILE -> "removeFile"
        DiffViewerMode.VIEW_ONLY -> "viewOnly"
    }
}
