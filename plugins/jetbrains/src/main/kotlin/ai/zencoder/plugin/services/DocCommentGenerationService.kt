package ai.zencoder.plugin.services

import ai.zencoder.generated.client.all.models.CodeeditCodeEditResponse
import ai.zencoder.generated.client.all.models.CodeeditGenerateDescriptionRequest
import ai.zencoder.generated.client.all.models.CodeeditTabItem
import ai.zencoder.plugin.api.CodeEditService
import ai.zencoder.plugin.api.exception.RateLimitException
import ai.zencoder.plugin.context.CommandContext
import ai.zencoder.plugin.context.CommandContextConfig
import ai.zencoder.plugin.context.ContextGatherService
import ai.zencoder.plugin.context.SymbolConfig
import ai.zencoder.plugin.diff.UnifiedDiffTool
import ai.zencoder.plugin.diff.applyPatchToContent
import ai.zencoder.plugin.log.errorAndThrow
import ai.zencoder.plugin.model.path.AbsolutePath
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.utils.RelativePathBuilder
import ai.zencoder.plugin.utils.showUpgradeWarning
import ai.zencoder.plugin.utils.showWarning
import ai.zencoder.plugin.utils.uuid
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiElement
import com.intellij.util.application
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.concurrent.locks.ReentrantLock

@Service(Level.PROJECT)
class DocCommentGenerationService(
    val project: Project,
    val scope: CoroutineScope
) {
    private val codeEditService get() = service<CodeEditService>()
    private val contextGatherService get() = service<ContextGatherService>()
    private val config = CommandContextConfig(
        targetSymbol = SymbolConfig(docstring = true),
        fileLanguage = true
    )
    private val lock = ReentrantLock()
    private val fileService get() = project.service<FileService>()

    fun generateDocString(
        editor: Editor,
        element: PsiElement,
        calledFrom: String
    ) {
        var actionCanceled = false

        object : Task.Backgroundable(project, "Generating doc comment", true) {
            private var job: Job? = null

            override fun run(indicator: ProgressIndicator) {
                launchIfNotBusy {
                    if (actionCanceled) {
                        return@launchIfNotBusy
                    }
                    job = scope.launch {
                        try {
                            val requestId = uuid()
                            val commandContext = gatherCommandContext(project, editor, element, config)
                            val analyticsProps = mapOf(
                                "operation_id" to requestId,
                                "called_from" to calledFrom,
                                "prog_lang" to commandContext?.fileLanguage
                            )

                            track("Code description requested", analyticsProps)

                            if (commandContext?.targetSymbol == null || commandContext.fileLanguage == null) {
                                thisLogger().errorAndThrow("Context is not properly gathered")
                            }

                            val activePath = commandContext.targetSymbol.path
                            val codeEditRequest = CodeeditGenerateDescriptionRequest(
                                requestId = requestId,
                                activeFilePath = activePath,
                                activeFileTab = CodeeditTabItem(
                                    path = activePath,
                                    content = editor.document.text,
                                    language = commandContext.fileLanguage
                                ),
                                selectedCodeSpan = listOf(
                                    editor.document.getLineNumber(element.textRange.startOffset),
                                    editor.document.getLineNumber(element.textRange.endOffset)
                                )
                            )
                            if (actionCanceled) {
                                return@launch
                            }
                            val codeEditResponse = codeEditService.requestCodeeditGenerateDescription(codeEditRequest)
                            if (actionCanceled) {
                                return@launch
                            }
                            track("Code description generated", analyticsProps)

                            updateUI(editor, codeEditResponse)
                        } catch (e: Exception) {
                            thisLogger().warn(e.message ?: e.toString())
                            if (e is RateLimitException) {
                                invokeLater { showUpgradeWarning(e) }
                            } else {
                                invokeLater { showWarning("Unable to generate doccomment: ${e.message}") }
                            }
                            onCancel()
                        }
                    }
                    if (!actionCanceled) {
                        runBlocking { job?.join() }
                    }
                }
            }

            override fun onCancel() {
                actionCanceled = true
            }
        }.queue()
    }

    private fun gatherCommandContext(
        project: Project,
        editor: Editor,
        element: PsiElement,
        config: CommandContextConfig
    ): CommandContext? {
        return application.runReadAction<CommandContext> {
            contextGatherService.gatherContext(
                project,
                editor,
                element,
                config
            )
        }
    }

    private fun updateUI(editor: Editor, codeEditResponse: CodeeditCodeEditResponse) {
        val relativeFilePath = RelativePathBuilder()
            .from(AbsolutePath(project.basePath as String))
            .to(AbsolutePath(editor.virtualFile.path))

        val initialFileContent = fileService.getFileContentAsString(relativeFilePath) ?: ""
        val updatedFileContent = applyPatchToContent(initialFileContent, codeEditResponse.patch)

        invokeLater {
            val diffDialog = UnifiedDiffTool(
                project = project,
                relativeFilePath,
                editor.virtualFile,
                null,
                generatedContentText = updatedFileContent,
                afterApply = { application.invokeLater { editor.selectionModel.removeSelection() } },
                afterCancel = { },
                title = "Diff: ${editor.virtualFile.name}",
                scope = scope
            )
            diffDialog.showDiff()
        }
    }

    private fun launchIfNotBusy(action: () -> Unit) {
        if (lock.tryLock()) {
            try {
                action.invoke()
            } finally {
                lock.unlock()
            }
        } else {
            thisLogger().warn("Attempt to launch unit test generation: already running")
        }
    }
}
