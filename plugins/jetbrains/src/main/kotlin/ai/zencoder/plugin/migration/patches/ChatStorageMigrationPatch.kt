package ai.zencoder.plugin.migration.patches

import ai.zencoder.plugin.migration.ZencoderProjectPatch
import ai.zencoder.plugin.migration.ZencoderProjectPatch.ProjectContext
import ai.zencoder.plugin.migration.chatManager
import ai.zencoder.plugin.migration.chatSessions
import ai.zencoder.plugin.webview.chat.ChatDedicatedFileStateManager
import ai.zencoder.plugin.webview.chat.ChatIndex
import ai.zencoder.plugin.webview.chat.ChatSessionStorage
import ai.zencoder.plugin.webview.chat.sendToWebview
import ai.zencoder.plugin.webview.model.PostedMessage
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.Logger
import kotlinx.coroutines.runBlocking
import java.io.File

private val logger = Logger.getInstance(ChatStorageMigrationPatch::class.java)

object ChatStorageMigrationPatch : ZencoderProjectPatch {
    /**
     * Unique identifier suffix for the migration patch.
     * This must never be changed once deployed.
     */
    override val idSuffix: String = "chat-storage-migration"

    /**
     * Determines if migration is needed by checking if there are any chats in the old format
     * that haven't been migrated yet.
     */
    override fun isNeeded(ctx: ProjectContext): Boolean {
        return ctx.chatSessions.isNotEmpty()
    }

    /**
     * Performs the actual migration from old to new format.
     * Uses ProgressManager to show progress to the user.
     */
    override fun doPatch(ctx: ProjectContext) {
        val project = ctx.project
        val oldChats = ctx.chatSessions
        val chatSessionStorage = project.service<ChatSessionStorage>()
        val chatIndex = project.service<ChatIndex>()
        val chatSingleFile = project.service<ChatDedicatedFileStateManager>()

        ctx.chatManager.state.also {
            chatSingleFile.state.activeChatId = it.activeChatId
            chatSingleFile.state.chatSessions.putAll(it.chatSessions)
        }

        // Ensure chat directory exists
        val chatDir = File(project.basePath, ".idea/zencoder/chats")
        if (!chatDir.exists()) {
            chatDir.mkdirs()
        }
        // Set active chat ID in new index
        chatIndex.setActiveChatId(ctx.chatManager.state.activeChatId)
        // Migrate each chat
        oldChats.entries.forEachIndexed { index, (chatId, chatModel) ->
            try {
                // Save chat to file
                runBlocking {
                    chatSessionStorage.saveChat(chatModel)
                }

                // Add metadata to index
                chatIndex.updateMetadataFromChatModel(chatModel)

                logger.info("Migrated chat $chatId: ${chatModel.title}")
            } catch (e: Exception) {
                logger.error("Failed to migrate chat $chatId", e)

                throw e
            }
        }
        oldChats.clear()

        ctx.project.sendToWebview(
            PostedMessage.SetChatSessions(
                chatSessions = ctx.chatManager.listSessions()
            )
        )
    }
}
