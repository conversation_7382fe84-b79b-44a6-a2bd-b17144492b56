@file:Suppress("SpellCheckingInspection", "DuplicatedCode")

package ai.zencoder.plugin.auth

import ai.zencoder.plugin.config.FileTokenStorage
import ai.zencoder.plugin.config.TokenStorage
import ai.zencoder.plugin.obfuscation.DoNotObfuscate
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.observers.auth.AuthObserver
import ai.zencoder.plugin.settings.ZencoderSettings
import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility
import com.fasterxml.jackson.annotation.JsonProperty
import com.intellij.ide.impl.ProjectUtil
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.wm.WindowManager
import com.intellij.util.application
import kotlinx.serialization.Serializable
import okhttp3.Request
import java.awt.Window

@Service(Service.Level.APP)
class AuthCodeFlowService : AbstractOAuthService() {

    override val providerTitle: String
        get() = "JBPlugin"
    override val authorizationUrl: String get() = service<ZencoderSettings>().signInUrl
    override val signUpUrl: String get() = service<ZencoderSettings>().signUpUrl
    override val redirectUrlPath: String
        get() = "/jbp-callback"
    override val tokenExchangeUrl: String
        get() = "$authorizationUrl/api/oauth/token"
    override val tokenRefreshUrl: String
        get() = "$authorizationUrl/refresh_token"
    override val clientRedirectUrl: String
        get() = "$authorizationUrl/extension/auth-success"

    override fun getTokenStorage(): TokenStorage = FileTokenStorage.getInstance()

    override fun generateLoginUrl(redirectUri: String, codeChallenge: String?): String =
        "$authorizationUrl/extension/signin?code_challenge=$codeChallenge&redirect_uri=$redirectUri"

    override fun generateSignUpUrl(redirectUri: String, codeChallenge: String?): String =
        "$authorizationUrl/extension/signup?code_challenge=$codeChallenge&redirect_uri=$redirectUri"

    override fun createRefreshTokenRequest(
        refreshToken: String,
        grantType: String?,
        providerType: String
    ): OAuthRefreshTokenRequest = AuthCodeFlowRefreshTokenRequest(
        refreshToken = refreshToken,
        grantType = grantType ?: "refresh_token"
    )

    override fun parseRefreshTokenResponse(responseBody: String): OAuthRefreshTokenResponse =
        json.decodeFromString<AuthCodeFlowRefreshTokenResponse>(responseBody)

    override fun serializeRefreshTokenRequest(request: OAuthRefreshTokenRequest): String =
        json.encodeToString(AuthCodeFlowRefreshTokenRequest.serializer(), request as AuthCodeFlowRefreshTokenRequest)

    override fun parseTokenExchangeResponse(responseBody: String): OAuthTokenExchangeResponse =
        objectMapper.readValue(responseBody, AuthCodeFlowTokenExchangeResponse::class.java)

    override fun postLoginActions(result: AuthInfo) {
        service<AuthObserver>().signIn(result)
        application.invokeLater {
            val recentWindow: Window? = WindowManager.getInstance().mostRecentFocusedWindow
            val project = ProjectUtil.getRootFrameForWindow(recentWindow)?.project
            ProjectUtil.focusProjectWindow(project, true)
        }
        track("User signed in")
    }

    override fun postLogoutActions() {
        service<AuthObserver>().signOut()
        track("User signed out")
    }

    override fun refreshRequestBuilderModifier(builder: Request.Builder) {}
}

@DoNotObfuscate
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@Serializable
data class AuthCodeFlowRefreshTokenRequest(
    override val refreshToken: String,
    override val grantType: String
) : OAuthRefreshTokenRequest

@DoNotObfuscate
@JsonAutoDetect(fieldVisibility = Visibility.ANY)
@Serializable
data class AuthCodeFlowRefreshTokenResponse(
    override val accessToken: String,
    override val refreshToken: String,
    val mfaRequired: Boolean? = null,
    val userId: String? = null,
    val expiresInSeconds: Int? = null,
    val expires: String? = null
) : OAuthRefreshTokenResponse

@JsonAutoDetect(fieldVisibility = Visibility.ANY)
private data class AuthCodeFlowTokenExchangeResponse(
    @JsonProperty("access_token")
    val accessToken: String,
    @JsonProperty("refresh_token")
    val refreshToken: String,
    @JsonProperty("expires_in")
    val expiresIn: Int? = null,
    @JsonProperty("token_type")
    val tokenType: String? = null
) : OAuthTokenExchangeResponse {
    override fun accessToken(): String = accessToken

    override fun refreshToken(): String = refreshToken
}
