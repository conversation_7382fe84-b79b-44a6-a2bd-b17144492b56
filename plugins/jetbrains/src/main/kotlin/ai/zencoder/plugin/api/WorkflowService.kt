package ai.zencoder.plugin.api

import ai.zencoder.agent.operations.AgentOperationEvent
import ai.zencoder.generated.client.all.apis.ZencoderWorkflowApi
import ai.zencoder.generated.client.all.infrastructure.ClientError
import ai.zencoder.generated.client.all.infrastructure.ClientException
import ai.zencoder.generated.client.all.models.*
import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.api.exception.ErrorMessage
import ai.zencoder.plugin.api.exception.RateLimitException
import ai.zencoder.plugin.api.exception.RequestDelayedException
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.auth.isFreePlanUser
import ai.zencoder.plugin.context.AgentChatContextService
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.toZencoderPolicy
import ai.zencoder.plugin.socket.ably.AblyConnectionManager
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.chat.model.CustomInstructionStateManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import io.sentry.Sentry
import kotlinx.serialization.json.Json

sealed class AgenticMessageResult {
    object Processed : AgenticMessageResult()
    class Delayed(val seconds: Long) : AgenticMessageResult()
}

@Service(Service.Level.PROJECT)
class WorkflowService(val project: Project) {

    private val authService get() = service<AuthService>()
    private val ablyConnectionManager get() = service<AblyConnectionManager>()
    private val chatManager get() = project.service<ChatManager>()
    private val jsonParser = Json {
        ignoreUnknownKeys = true
    }

    fun agenticMessage(operationId: String, autoApply: Boolean): AgenticMessageResult {
        try {
            val supportedEventTypes = getSupportedEventsTypes()
            val customInstructions = service<CustomInstructionStateManager>().customInstruction?.takeIf { it.isNotBlank() }
            val settings = service<ZencoderSettings>()
            val agentChatContextService = project.service<AgentChatContextService>()
            // Get the current chat and its custom agent (if any)
            val chat = chatManager.findChatIdForOperationId(operationId)?.let { chatManager.getChatById(it) }
            val allowedTools = mapAgentToolsToBackendFormat(chat?.customAgent)
            val llmSettings = agentChatContextService.collectLLMSettings(settings)

            val requestBody = ZencoderWorkflowAgentContextRequest(
                ideId = ablyConnectionManager.getOperationSessionChannelName(project),
                operationId = operationId,
                eventStreamingConfig = ZencoderWorkflowEventStreamingConfig(
                    supportedEventTypes = supportedEventTypes,
                    compressionEnabled = false,
                    chunkingEnabled = true
                ),
                agentContext = agentChatContextService.collectAgentContext(operationId),
                customConfiguration = ZencoderWorkflowCustomConfiguration(
                    customInstructions = customInstructions,
                    autoApply = autoApply
                ),
                ideContext = agentChatContextService.collectIdeContext(operationId),
                llmSettings = llmSettings,
                mcpConfig = ZencoderWorkflowMcpConfig(useAblyTransport = false),
                toolsAllowList = allowedTools,
                userConfiguration = ZencoderWorkflowUserConfiguration(
                    commandConfirmationPolicy = settings.shellToolConfirmationPolicy.toZencoderPolicy(),
                    allowedCommands = settings.shellToolAllowedCommands.toList()
                ),
                createOrUpdateFilesMutationVersion = ZencoderWorkflowCreateOrUpdateFilesMutationVersion.V2
            )

            val api = ZencoderWorkflowApi(settings.workflowServiceUrl)
                .withAuth(authService.accessToken)
            if (isAnthropicKeyProvided(llmSettings)) {
                api.zencoderWorkflowAgenticMessage3ByokV3AgenticMessageByokPost(requestBody)
            } else {
                api.zencoderWorkflowAgenticMessage3V3AgenticMessagePost(requestBody)
            }
        } catch (e: RequestDelayedException) {
            return AgenticMessageResult.Delayed(e.delayedForSeconds)
        } catch (e: Exception) {
            val chat = chatManager.findChatIdForOperationId(operationId)?.let { chatManager.getChatById(it) }
            if (chat == null) {
                Sentry.captureException(Exception("Chat is null for operation id $operationId", e))
                thisLogger().debug("Chat is null for operation id $operationId", e)
                return AgenticMessageResult.Processed
            }
            when (e) {
                is ClientException -> {
                    when (e.statusCode) {
                        426 -> {
                            chatManager.sendUpdateRequiredToChat(
                                e.errorMessage(ZencoderBundle["error.chat.update.required"]),
                                chat,
                                operationId
                            )
                        }

                        else -> {
                            Sentry.captureException(e)
                            thisLogger().debug("Could not initiale agentic workflow", e)
                            chatManager.sendErrorMessageToChat(e.errorMessage(), chat, operationId)
                        }
                    }
                }

                is RateLimitException -> {
                    if (authService.isFreePlanUser) {
                        chatManager.sendUpgradePlanToChat(e, chat, operationId)
                    } else {
                        chatManager.sendErrorMessageToChat(e.genericAgenticWorkflowErrorMessage(), chat, operationId)
                    }
                }

                else -> {
                    Sentry.captureException(e)
                    thisLogger().debug("Could not initiale agentic workflow", e)
                    chatManager.sendErrorMessageToChat(e.genericAgenticWorkflowErrorMessage(), chat, operationId)
                }
            }
        }
        return AgenticMessageResult.Processed
    }

    /**
     * Fetches available toolkits from the Zencoder backend.
     * This method is used to integrate Zencoder backend tools into the tools registry.
     *
     * @return ZencoderWorkflowAvailableToolkitsResponse containing available toolkits
     */
    fun getAvailableToolkits(): ZencoderWorkflowAvailableToolkitsResponse {
        try {
            return ZencoderWorkflowApi(service<ZencoderSettings>().workflowServiceUrl)
                .withAuth(authService.accessToken)
                .zencoderWorkflowAvailableToolkitsV1AvailableToolkitsGet()
        } catch (e: Exception) {
            thisLogger().warn("Failed to fetch available toolkits", e)
            Sentry.captureException(e)
            // Return empty response in case of error
            return ZencoderWorkflowAvailableToolkitsResponse(emptyMap())
        }
    }

    private fun getSupportedEventsTypes(): List<ZencoderWorkflowOperationEventTypeEnum> {
        val supportedEventTypes = mutableSetOf(
            AgentOperationEvent.EVENT_TEXT_PART,
            AgentOperationEvent.EVENT_LOADING_MARKER_PART,
            AgentOperationEvent.EVENT_CODE_DIFFS_PART,
            AgentOperationEvent.EVENT_UPDATE_CONTEXT_PART,
            AgentOperationEvent.EVENT_LOADING_PART,
            AgentOperationEvent.EVENT_TOOL_CALL_PART,
            AgentOperationEvent.EVENT_END_OPERATION,
            AgentOperationEvent.EVENT_GROUPED_CHECKLIST_PART,
            AgentOperationEvent.EVENT_CODE_HUNK_PART,
            AgentOperationEvent.EVENT_SHELL_COMMAND_PART,
            AgentOperationEvent.EVENT_WARNING_ALERT
        )
        return supportedEventTypes
            .mapNotNull { ZencoderWorkflowOperationEventTypeEnum.decode(it.adapter.type?.simpleName) }
    }

    private fun ClientException.errorMessage(fallbackMessage: String? = null) = try {
        this.errorMessageFromBackend()
    } catch (fallbackException: Exception) {
        fallbackMessage ?: this.genericAgenticWorkflowErrorMessage()
    }

    private fun ClientException.errorMessageFromBackend() = ((this.response as ClientError<*>).body as String).let {
        jsonParser.decodeFromString<ErrorMessage>(it)
    }.message

    private fun Exception.genericAgenticWorkflowErrorMessage() = this.message ?: "Unable to initiate agentic workflow"

    private fun mapAgentToolsToBackendFormat(agent: ai.zencoder.plugin.agents.Agent?): List<ZencoderWorkflowAvailableToolModel>? {
        // Return null to enable all tools for non-custom agents
        if (agent?.tools == null) return null

        return agent.tools.map { tool ->
            // Determine the type based on the author field
            val type = when (tool.author) {
                ZencoderWorkflowToolkitType.ZENCODER_BACKEND.value -> ZencoderWorkflowToolkitType.ZENCODER_BACKEND
                ZencoderWorkflowToolkitType.ZENCODER_MCP_CLIENT.value -> ZencoderWorkflowToolkitType.ZENCODER_MCP_CLIENT
                ZencoderWorkflowToolkitType.ZENCODER_MCP_BACKEND.value -> ZencoderWorkflowToolkitType.ZENCODER_MCP_BACKEND
                ZencoderWorkflowToolkitType.ZENCODER_CLIENT.value -> ZencoderWorkflowToolkitType.ZENCODER_CLIENT
                else -> ZencoderWorkflowToolkitType.EXTERNAL_MCP
            }

            ZencoderWorkflowAvailableToolModel(
                id = tool.id,
                name = tool.name ?: tool.id,
                description = tool.description ?: "",
                type = type,
                tools = tool.tools ?: emptyList()
            )
        }
    }
}

private fun isAnthropicKeyProvided(llmSettings: ZencoderWorkflowLLMSettings?): Boolean = if (llmSettings != null) {
    llmSettings.modelsApiKey.let {
        it != null && it.any { key ->
            key.apiKey.isNotEmpty() && key.model == ZencoderWorkflowModelType.ANTHROPIC
        }
    }
} else {
    false
}
