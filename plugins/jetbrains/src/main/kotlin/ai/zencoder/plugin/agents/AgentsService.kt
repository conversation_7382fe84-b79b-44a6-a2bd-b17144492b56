package ai.zencoder.plugin.agents

import ai.zencoder.generated.client.all.apis.AgentControllerApi
import ai.zencoder.generated.client.all.models.AgentRequest
import ai.zencoder.generated.client.all.models.PageAgent
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.settings.ZencoderAgentsUrlChangeListener
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.settings.ZencoderSettingsEvents
import ai.zencoder.plugin.utils.JSON
import ai.zencoder.plugin.utils.uuidOf
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.util.application
import com.intellij.util.io.await
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.encodeToString
import kotlin.jvm.Throws
import kotlin.time.Duration.Companion.seconds
import ai.zencoder.generated.client.all.models.Agent as ApiAgent

private val DEFAULT_NETWORK_TIMEOUT = 5.seconds

@Service(Level.APP)
class AgentsService {
    private var client = createClient()

    init {
        application.messageBus
            .connect()
            .subscribe(
                ZencoderSettingsEvents.AGENTS_URL_SETTINGS_CHANGED,
                object : ZencoderAgentsUrlChangeListener {
                    override fun agentsServiceUrlChanged() {
                        client = createClient()
                    }
                }
            )
    }

    private fun createClient(): AgentControllerApi {
        return AgentControllerApi(basePath = service<ZencoderSettings>().agentsServiceUrl)
    }

    @Throws(Exception::class)
    suspend fun getAll(): PageAgent {
        return withApiTimeout {
            client
                .withAuth(service<AuthService>().accessToken)
                .getMyAgentsAsync(page = 0, size = 100)
                .await()
        }
    }

    @Throws(Exception::class)
    suspend fun create(request: AgentRequest): ApiAgent {
        return withApiTimeout {
            client
                .withAuth(service<AuthService>().accessToken)
                .createAgentAsync(request)
                .await()
        }
    }

    @Throws(Exception::class)
    suspend fun update(id: AgentId, request: AgentRequest): ApiAgent {
        return withApiTimeout {
            client
                .withAuth(service<AuthService>().accessToken)
                .updateAgentAsync(uuidOf(id), request)
                .await()
        }
    }

    @Throws(Exception::class)
    suspend fun remove(id: AgentId) {
        return withApiTimeout {
            client
                .withAuth(service<AuthService>().accessToken)
                .deleteAgentAsync(uuidOf(id))
                .await()
        }
    }

    private suspend fun <T> withApiTimeout(block: suspend () -> T): T {
        try {
            return withTimeout(DEFAULT_NETWORK_TIMEOUT) { block() }
        } catch (e: TimeoutCancellationException) {
            thisLogger().warn("API call timed out after $DEFAULT_NETWORK_TIMEOUT")
            throw e
        } catch (e: Exception) {
            thisLogger().warn("API call failed: ${e.message}")
            throw e
        }
    }
}

fun requestOf(agent: Agent) = AgentRequest(
    name = agent.name,
    alias = agent.commandName ?: error("Missing command name"),
    instruction = agent.command,
    tools = agent.tools?.map { JSON.encodeToString(it) } ?: emptyList(),
    availableInIde = true,
    isSharedWithOrganization = agent.isSharedWithOrganization ?: false,
    description = null
)

fun ApiAgent.toDomain() = Agent(
    id = id.toString(),
    name = name,
    commandName = alias,
    command = instruction,
    codeLens = true,
    repair = true,
    rag = true,
    tools = tools.map { JSON.decodeFromString(it) },
    creationDate = createdAt.toInstant().toString(),
    lastModifiedDate = updatedAt.toInstant().toString(),
    author = author,
    isSharedWithOrganization = sharedWithOrganization != null
)
