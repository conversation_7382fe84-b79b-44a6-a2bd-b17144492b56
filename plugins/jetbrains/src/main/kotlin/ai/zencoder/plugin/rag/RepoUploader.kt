package ai.zencoder.plugin.rag

import ai.zencoder.generated.client.all.models.ControlPlaneQuotaStatus
import ai.zencoder.generated.client.repoUpload.infrastructure.ClientException
import ai.zencoder.generated.client.transport.models.TransportBusRepoIndexingEvent
import ai.zencoder.generated.client.transport.models.TransportBusRepoIndexingStatus
import ai.zencoder.plugin.ZencoderBundle
import ai.zencoder.plugin.api.DefaultApiService
import ai.zencoder.plugin.api.RagUploadApiService
import ai.zencoder.plugin.api.exception.RateLimitException
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.log.errorAndThrow
import ai.zencoder.plugin.observability.track
import ai.zencoder.plugin.observers.auth.AUTH_LISTENER
import ai.zencoder.plugin.observers.auth.AuthListener
import ai.zencoder.plugin.rag.WebviewIndexingStatus.FULLY_INDEXED
import ai.zencoder.plugin.rag.WebviewIndexingStatus.INDEXING_FAILED
import ai.zencoder.plugin.rag.vcs.DiffStatus
import ai.zencoder.plugin.rag.vcs.RepoDiff
import ai.zencoder.plugin.services.ZencoderPluginProjectDisposable
import ai.zencoder.plugin.settings.ZencoderSettings
import ai.zencoder.plugin.utils.calculateRelativePath
import ai.zencoder.plugin.utils.showInformation
import ai.zencoder.plugin.utils.showNotification
import ai.zencoder.plugin.utils.uuid
import ai.zencoder.plugin.webview.chat.ChatWebviewManager
import ai.zencoder.plugin.webview.model.PostedMessage.SetRepoIndexData
import ai.zencoder.plugin.webview.service.ProjectLevelSettingsManager
import com.intellij.notification.Notification
import com.intellij.notification.NotificationType
import com.intellij.notification.Notifications
import com.intellij.openapi.application.ModalityState
import com.intellij.openapi.application.invokeLater
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import com.intellij.openapi.vcs.FileStatus
import com.intellij.openapi.vcs.changes.Change
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.platform.ide.progress.withBackgroundProgress
import com.intellij.platform.util.progress.SequentialProgressReporter
import com.intellij.platform.util.progress.reportSequentialProgress
import com.intellij.ui.ColorUtil.toHtmlColor
import com.intellij.ui.JBColor
import com.intellij.util.application
import com.intellij.util.messages.Topic
import io.github.oshai.kotlinlogging.KotlinLogging
import io.sentry.Sentry
import io.sentry.SpanStatus
import io.sentry.TransactionOptions
import io.sentry.kotlin.SentryContext
import kotlinx.coroutines.*
import java.awt.Desktop
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import javax.swing.event.HyperlinkEvent.EventType
import kotlin.math.ln
import kotlin.math.pow

val REPO_UPLOAD_PROGRESS = Topic.create("zencoder.repoUploadProgress", RepoUploadListener::class.java)
const val MAX_FILE_PROGRESS_PERCENT = 0.81

val intermediateStatuses = setOf(
    WebviewIndexingStatus.NOT_INDEXED_YET,
    WebviewIndexingStatus.ANALYZING_REPO,
    WebviewIndexingStatus.SENDING_FILES
)
val devMode = System.getProperty("development.mode").toBoolean()

fun interface RepoUploadListener {
    fun update(percent: Double, errorMessage: String?)
}

@Service(Level.PROJECT)
class RepoRagUploader(
    private val project: Project,
    private val cs: CoroutineScope
) {
    private val logger = thisLogger()

    private val settings get() = service<ZencoderSettings>()
    private val ragStateManager get() = project.service<RagStateManager>()
    private val ragSettingsManager get() = project.service<RagSettingsManager>()
    private val chatWebview get() = project.service<ChatWebviewManager>()
    private val projectSettings get() = project.service<ProjectLevelSettingsManager>()
    private val repoUploadService get() = service<RagUploadApiService>()

    private val listener = project.messageBus.syncPublisher(REPO_UPLOAD_PROGRESS)
    private val vcsManager get() = project.service<VCSManager>()
    private var chunkedUploader: ChunkedRepoUploader? = null

    @Volatile
    private var isUploadingRunning = false

    init {
        logger.debug("Initializing RepoRagUploader for project: ${project.name}")
        application.messageBus.connect(project.service<ZencoderPluginProjectDisposable>())
            .subscribe(
                AUTH_LISTENER,
                AuthListener {
                    logger.debug("Auth event received, triggering repository upload")
                    upload()
                }
            )
    }

    fun enableRagAndUpload(forceReindex: Boolean = false) {
        logger.info("Enabling RAG and uploading repository, forceReindex=$forceReindex")
        if (!settings.codebaseIndexing) {
            logger.info("Codebase indexing was disabled, enabling it now")
            settings.codebaseIndexing = true
            showInformation(ZencoderBundle["notification.repo.indexing.enabled"])
        }
        upload(forceReindex)
    }

    fun upload(forceReindex: Boolean = false) {
        logger.info("Repository upload requested, forceReindex=$forceReindex")

        // Check preconditions
        if (!settings.codebaseIndexing) {
            logger.debug("Codebase indexing is disabled, skipping upload")
            return
        }

        if (vcsManager.vcsService.getRootPath() == null) {
            logger.debug("No VCS root path found, skipping upload")
            return
        }

        if (!service<AuthService>().isAuthenticated()) {
            logger.debug("User is not authenticated, skipping upload")
            return
        }

        if (isUploadingRunning) {
            logger.debug("Upload is already running, skipping duplicate request")
            return
        }

        logger.info("Starting repository upload process for project: ${project.name}")
        val span = Sentry.startTransaction("jetbrains.repo-upload.v1", "repo-upload", TransactionOptions().apply { isBindToScope = true })
        logger.debug("Created Sentry transaction: ${span.name}")
        isUploadingRunning = true

        val handler = CoroutineExceptionHandler { _, exception ->
            logger.error("Exception during repository upload", exception)
            span.throwable = exception

            when (exception) {
                is RateLimitException -> {
                    logger.warn("Rate limit exceeded: ${exception.message}")
                    ragStateManager.state.apply {
                        state = FULLY_INDEXED
                    }
                    span.finish(SpanStatus.RESOURCE_EXHAUSTED)

                    clearRagProgress()
                    showNotification(exception.message, NotificationType.INFORMATION)
                }

                is ClientException -> {
                    val error = exception.message ?: "Unknown client error occurred."
                    val code = exception.statusCode
                    logger.warn("Client exception during repository upload: $error (code: $code)", exception)

                    ragStateManager.state.apply {
                        state = FULLY_INDEXED
                    }

                    span.finish(
                        when (code) {
                            401 -> SpanStatus.UNAUTHENTICATED
                            else -> SpanStatus.INTERNAL_ERROR
                        }
                    )

                    val failedMsg = ZencoderBundle["notification.repoGrokking.indexingFailed"].format(error)
                    logger.warn(failedMsg)
                    clearRagProgress()

                    if (devMode) {
                        showNotification(error, NotificationType.WARNING)
                    }
                }

                is Exception -> {
                    val error = exception.message ?: "Unknown server error occurred."
                    logger.warn("Unexpected error during repository upload: $error", exception)

                    ragStateManager.state.apply {
                        lastError = error
                        state = INDEXING_FAILED
                    }
                    span.finish(SpanStatus.INTERNAL_ERROR)

                    val failedMsg = ZencoderBundle["notification.repoGrokking.indexingFailed"].format(error)
                    listener.update(0.0, failedMsg)

                    if (devMode) {
                        showNotification(error, NotificationType.ERROR)
                    }
                }
            }
        }

        val job = cs.launch(Dispatchers.IO + SentryContext() + handler) {
            try {
                // initial default value, because get{} with also don't work in PyCharm
                val repositoryId = ragStateManager.state.repositoryId ?: uuid()
                logger.info("Processing repository with ID: $repositoryId")

                logger.debug("Fetching quotas and active indexing tasks")
                val quotasReq = this.async { service<DefaultApiService>().quotas() }
                val requestId = uuid()
                logger.debug("Generated request ID: $requestId")

                val repoIndexingStatusReq = this.async {
                    repoUploadService.activeIndexingTasks(
                        ActiveIndexingTasksRequest(
                            repositoryId = repositoryId,
                            requestId = requestId
                        )
                    )
                }

                val quotas = quotasReq.await()
                logger.debug("Received quotas: advanced=${quotas.advanced}")

                val repoIndexingStatus = repoIndexingStatusReq.await()
                logger.debug("Received indexing status: tasks=${repoIndexingStatus.tasks.size}, quota=${repoIndexingStatus.quota}")

                // user reached limit of rag quota
                // if user explicitly asks for reindex - let go further and show next indexing date from backend
                if (!forceReindex && (quotas.advanced == null || quotas.advanced != ControlPlaneQuotaStatus.OK)) {
                    logger.info("User reached limit of RAG quota, advanced status: ${quotas.advanced}")
                    span.finish(SpanStatus.RESOURCE_EXHAUSTED)
                    return@launch
                }

                if (repoIndexingStatus.tasks.size >= repoIndexingStatus.quota) {
                    logger.info("Concurrent indexing limit reached: ${repoIndexingStatus.tasks.size}/${repoIndexingStatus.quota}")
                    // if user explicitly asks for reindex - show warning
                    if (forceReindex) {
                        logger.debug("Force reindex requested, showing warning about concurrent limit")
                        showNotification(
                            message = ZencoderBundle["notification.repo.indexing.concurrentLimitReached"],
                            type = NotificationType.WARNING
                        )
                    }
                    span.finish(SpanStatus.ALREADY_EXISTS)
                    return@launch
                }

                val isRepoIndexing = repoIndexingStatus.tasks.any { it.repoId.toString() == repositoryId }
                logger.debug("Repository indexing status: isRepoIndexing=$isRepoIndexing, isInProgress=${ragStateManager.isInProgress}")

                if (!isRepoIndexing && ragStateManager.isInProgress) { // reset IDE status if it was closed during some indexing process
                    logger.info("Resetting indexing state that was in progress")
                    ragStateManager.state.apply {
                        state = WebviewIndexingStatus.NOT_INDEXED_YET
                    }
                }

                logger.debug("Fetching VCS changes")
                val changes = vcsManager.vcsService.getChanges()
                logger.debug("Found ${changes.size} changes in VCS")

                // user requires or gitignore changed or first time indexing -> rebuild
                val rebuildIndexRequired =
                    forceReindex || changes.isGitignoreChanged() || ragStateManager.state.lastCommitHash == null ||
                        intermediateStatuses.contains(ragStateManager.state.state)

                logger.info(
                    buildString {
                        append("Rebuild index required: ")
                        append(rebuildIndexRequired)
                        append(" (forceReindex=")
                        append(forceReindex)
                        append(", gitignoreChanged=")
                        append(changes.isGitignoreChanged())
                        append(", firstTimeIndexing=")
                        append(ragStateManager.state.lastCommitHash == null)
                        append(", intermediateStatus=")
                        append(
                            intermediateStatuses.contains(
                                ragStateManager.state.state
                            )
                        )
                        append(")")
                    }
                )

                // no changes since last indexing, and we don't want to track indexing status (open project during indexing phase)
                if (!rebuildIndexRequired && changes.isEmpty() && ragStateManager.state.state != WebviewIndexingStatus.UPDATING_INDEX) {
                    logger.info("No changes detected and not in updating state, skipping upload")
                    span.finish(SpanStatus.OK)
                    return@launch
                }

                // check if there's an ongoing indexing process
                if (!isRepoIndexing) {
                    logger.info("Starting new indexing process")
                    ragStateManager.state.apply {
                        this.state = WebviewIndexingStatus.ANALYZING_REPO
                        this.lastError = null
                        this.repositoryId = repositoryId
                    }

                    logger.debug("Notifying chat webview of state change")
                    notifyChatWebview()

                    logger.info("Starting background progress for repository upload")
                    withBackgroundProgress(project, ZencoderBundle["notification.repoIndexing"], true) {
                        reportSequentialProgress { indicator ->
                            logger.debug("Starting repository upload with progress indicator")
                            doRepoUpload(rebuildIndexRequired, changes, indicator, this)
                        }
                    }
                } else {
                    logger.info("Repository is already being indexed, skipping upload")
                }

                logger.info("Repository upload process completed successfully")
                span.finish(SpanStatus.OK)
            } catch (e: Exception) {
                logger.error("Unexpected error in upload coroutine", e)
                span.throwable = e
                span.finish(SpanStatus.INTERNAL_ERROR)
            }
        }

        job.invokeOnCompletion { throwable ->
            logger.info("Upload job completed, throwable=${throwable != null}")
            isUploadingRunning = false

            if (throwable != null && throwable !is CancellationException) {
                logger.warn("Upload job completed with error", throwable)
            }

            invokeLater(ModalityState.any()) {
                logger.debug("Notifying chat webview after job completion")
                notifyChatWebview()
            }
        }
    }

    fun clearRagProgress() {
        logger.debug("Clearing RAG progress")
        listener.update(0.0, null)
    }

    private suspend fun doRepoUpload(
        rebuildIndexRequired: Boolean = false,
        changesForIndexing: List<Change> = emptyList(),
        indicator: SequentialProgressReporter,
        scope: CoroutineScope
    ) {
        logger.info(
            "Starting repository upload operation: rebuildIndexRequired=$rebuildIndexRequired, changesCount=${changesForIndexing.size}"
        )

        val span = Sentry.getSpan()?.startChild("request.repo_upload")
        logger.debug("Created Sentry child span for repository upload")

        val rootPath = vcsManager.vcsService.getRootPath()
        if (rootPath == null) {
            logger.warn("No VCS root path found, aborting upload")
            span?.finish(SpanStatus.FAILED_PRECONDITION)
            return
        }
        logger.debug("Using VCS root path: $rootPath")

        val repositoryId = ragStateManager.state.repositoryId ?: uuid()
        logger.debug("Repository ID: $repositoryId")

        logger.debug("Getting filtered files for upload")
        val filesToZip = vcsManager.vcsService.getFilteredFiles(rootPath, rebuildIndexRequired).iterator()
        val hasFiles = filesToZip.hasNext()
        logger.debug("Files available for upload: $hasFiles")

        if (!rebuildIndexRequired && changesForIndexing.isEmpty() && !hasFiles) {
            logger.info("No files to upload and no changes detected, skipping upload")
            listener.update(1.0, null)
            span?.finish(SpanStatus.OK)
            return
        }

        val startUploadTime = System.currentTimeMillis()
        logger.debug("Upload start time: $startUploadTime")

        logger.info("Updating state to SENDING_FILES")
        ragStateManager.state.state = WebviewIndexingStatus.SENDING_FILES
        notifyChatWebview()

        // Initialize chunked uploader
        logger.debug("Initializing chunked uploader")
        val repoDiff = changesForIndexing.mapToRepoDiff()
        logger.debug("Created repo diff with ${repoDiff.size} entries")

        chunkedUploader = ChunkedRepoUploader(
            project = project,
            scope = scope,
            repoUploadService = repoUploadService,
            rootPath = rootPath,
            repositoryId = repositoryId,
            rebuildIndexRequired = rebuildIndexRequired,
            startUploadTime = startUploadTime,
            repoDiff = repoDiff,
            settings = ragSettingsManager.state,
            filesToZip = filesToZip
        )
        logger.debug("Chunked uploader initialized")

        logger.info("Tracking repository indexing start event")
        track(
            "Repo Indexing started",
            "repo_id" to repositoryId,
            "is_reindex" to rebuildIndexRequired
        )

        logger.info("Updating state to UPDATING_INDEX")
        ragStateManager.state.state = WebviewIndexingStatus.UPDATING_INDEX
        notifyChatWebview()

        // Upload chunks sequentially
        logger.info("Starting chunk upload process")
        chunkedUploader?.uploadChunks { progress, chunk, timestamp ->
            logger.debug("Upload progress: ${(progress * 100).toInt()}%, chunk: $chunk, timestamp: $timestamp")
            indicator.nextStep((progress * 100).toInt())
            listener.update(progress * MAX_FILE_PROGRESS_PERCENT, null)
        }
        logger.info("Chunk upload process completed")

        logger.debug("Updating VCS state to completed")
        vcsManager.vcsService.updateState(TransportBusRepoIndexingStatus.completed, startUploadTime)

        logger.debug("Updating repository state with file counts")
        ragStateManager.state.apply {
            when {
                rebuildIndexRequired -> {
                    val totalFiles = chunkedUploader?.totalFiles ?: 0
                    logger.debug("Rebuild required, setting total files to $totalFiles")
                    repoTotalFiles = totalFiles
                    repoAccumulatedChanges += totalFiles
                }
                else -> {
                    repoAccumulatedChanges += changesForIndexing.size
                }
            }
            logger.debug("Updated accumulated changes to $repoAccumulatedChanges")
        }

        // Update project info
        logger.info("Checking if project info update is needed")
        project.service<ProjectInfoService>().checkIfNeedToUpdateProjectInfo()

        logger.debug("Notifying chat webview of upload completion")
        notifyChatWebview()

        logger.info("Tracking repository indexing completion event")
        track(
            "Repo Indexing completed",
            "repo_id" to repositoryId,
            "total_files_num" to chunkedUploader?.totalFiles,
            "num_files_changed" to changesForIndexing.size,
            "is_reindex" to rebuildIndexRequired
        )

        logger.info("Repository upload completed successfully")
        span?.finish(SpanStatus.OK)

        logger.debug("Setting progress to 100%")
        listener.update(1.0, null)
    }

    private fun notifyChatWebview() {
        logger.debug("Notifying chat webview with updated repository index data")
        val repoIndexData = projectSettings.collectRepoIndexSettings()
        logger.trace("Repository index data: $repoIndexData")
        chatWebview.send(
            SetRepoIndexData(
                repoIndexData = repoIndexData
            )
        )
    }

    fun onRepoIndexingComplete(event: TransportBusRepoIndexingEvent, startIndexingTimestamp: Long) {
        logger.info("Repository indexing complete event received: ${event.status}, startTimestamp=$startIndexingTimestamp")
        chunkedUploader?.onRepoIndexingComplete(event, startIndexingTimestamp)
    }

    private fun List<Change>.mapToRepoDiff() = mapNotNull { change ->
        val status = when (change.fileStatus) {
            FileStatus.ADDED -> {
                DiffStatus.A
            }
            FileStatus.DELETED -> {
                DiffStatus.D
            }
            FileStatus.MODIFIED -> {
                DiffStatus.M
            }
            else -> {
                logger.trace("File status not relevant for diff: ${change.fileStatus}")
                return@mapNotNull null
            }
        }

        val newPath = change.afterRevision?.file?.path ?: change.beforeRevision?.file?.path
            ?: logger.errorAndThrow("afterRevision and beforeRevision is null for change: $change")
        val oldPath = change.beforeRevision?.file?.path ?: ""

        val relativePath = project.calculateRelativePath(newPath).toString()
        val relativeOldPath = project.calculateRelativePath(oldPath).toString()

        RepoDiff(
            name = relativePath,
            oldName = relativeOldPath,
            status = status
        )
    }

    private fun Collection<Change>.isGitignoreChanged(): Boolean {
        logger.debug("Checking if .gitignore file has changed")
        val result = this.any {
            val beforePath = it.beforeRevision?.file?.path ?: ""
            val afterPath = it.afterRevision?.file?.path ?: ""
            val isGitignore = beforePath.endsWith(".gitignore") || afterPath.endsWith(".gitignore")

            if (isGitignore) {
                logger.debug("Found .gitignore change: before=$beforePath, after=$afterPath")
            }

            isGitignore
        }
        logger.debug(".gitignore changed: $result")
        return result
    }
}

private fun colorForStatus(status: DiffStatus) = when (status) {
    DiffStatus.A -> FileStatus.ADDED.color
    DiffStatus.M -> FileStatus.MODIFIED.color
    DiffStatus.D -> FileStatus.DELETED.color
}

fun monospaced(value: String?): String {
    return "<span style='color:${toHtmlColor(JBColor.ORANGE)}font-family:monospace'>$value</span>"
}

fun storeZipLocally(
    request: RepoUploadRequest,
    zipFile: File,
    files: List<VirtualFile>
) {
    val logger = KotlinLogging.logger {}

    logger.info { "Storing ZIP file locally for repository: ${request.repositoryId}" }
    logger.debug { "ZIP file size: ${zipFile.length().toHumanReadableSize()}, files count: ${files.size}" }

    val ragZipsFolder = Paths.get(System.getProperty("user.home"), ".zencoder").resolve("rag-zips")
    logger.debug { "Using RAG zips folder: $ragZipsFolder" }

    val zipId = uuid()
    val zippedFile = ragZipsFolder.resolve("${request.repositoryId}_$zipId.zip")
    logger.debug { "Target ZIP file path: $zippedFile" }

    try {
        // Create parent directories if they don't exist
        zippedFile.parent.toFile().mkdirs()
        logger.debug { "Created parent directories" }

        // Copy the zipFile to the desired location
        Files.copy(zipFile.toPath(), zippedFile, StandardCopyOption.REPLACE_EXISTING)
        logger.info { "Successfully copied ZIP file to: $zippedFile" }
    } catch (e: Exception) {
        logger.error(e) { "Failed to copy ZIP file" }
        return
    }

    logger.debug { "Building diff HTML for notification" }
    // Build the diff lines with colors + monospaced file names
    val diffCount = request.diff.size
    logger.debug { "Diff count: $diffCount" }

    val diffToUse = if (diffCount > 50) {
        logger.debug { "Folding diff due to large size (> 50)" }
        foldDiff(request.diff)
    } else {
        request.diff
    }

    val diffHtml = diffToUse.joinToString(
        separator = "<br/>",
        prefix = "<br/>",
        postfix = "<br/>"
    ) {
        val color = colorForStatus(it.status)
        val displayPath = when (it.oldName) {
            it.name -> it.name
            null -> it.name
            "" -> it.name
            else -> "${it.oldName} -> ${it.name}"
        }

        """
        <span style="color:${toHtmlColor(color ?: JBColor.BLACK)};font-family:monospace">
          ${it.status}: $displayPath
        </span>
        """.trimIndent()
    }
    logger.trace { "Generated diff HTML" }

    // Get user ID for notification
    val userId = service<AuthService>().authInfoOrNull()?.userData?.id
    logger.debug { "User ID for notification: $userId" }

    // Create notification content
    val notificationContent = """
        userId: ${monospaced(userId)}<br/>
        repoId: ${monospaced(request.repositoryId)}<br/>
        requestId: ${monospaced(request.requestId)}<br/>
        diff: ${if (request.diff.isNotEmpty()) diffHtml else "<br/>"}
        rebuild: ${monospaced(request.rebuild.toString())}<br/>
        zip: <a href="${zippedFile.toUri().path}">Click</a> size: ${
        monospaced(
            zipFile.length().toHumanReadableSize()
        )
    } files: ${monospaced(files.size.toString())}
    """.trimIndent()

    logger.debug { "Creating notification with ZIP information" }
    val notification = Notification(
        "Zencoder Notification Group",
        notificationContent,
        NotificationType.INFORMATION
    )

    notification.setListener { _, hyperlinkEvent ->
        if (hyperlinkEvent.eventType == EventType.ACTIVATED) {
            logger.debug { "Hyperlink activated: ${hyperlinkEvent.description}" }
            val file = File(hyperlinkEvent.description)
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.BROWSE)) {
                logger.debug { "Opening file directory: ${file.absolutePath}" }
                Desktop.getDesktop().browseFileDirectory(file)
            } else {
                logger.warn("Desktop browsing not supported on this platform")
            }
        }
    }

    logger.info { "Showing ZIP storage notification" }
    Notifications.Bus.notify(notification)
}

private fun foldDiff(diff: List<RepoDiff>): List<RepoDiff> {
    val logger = KotlinLogging.logger {}
    logger.debug { "Folding diff with ${diff.size} entries" }

    val statusCounts = diff.fold(mutableMapOf<DiffStatus, Int>()) { acc, value ->
        acc.compute(value.status) { _, stored ->
            (stored ?: 0) + 1
        }
        return@fold acc
    }

    logger.debug { "Status counts: $statusCounts" }

    val foldedDiff = statusCounts.mapKeys { (status, count) ->
        logger.trace { "Creating folded diff entry: status=$status, count=$count" }
        RepoDiff(count.toString(), null, status)
    }.keys.toList()

    logger.debug { "Folded diff contains ${foldedDiff.size} entries" }
    return foldedDiff
}

fun Long.toHumanReadableSize(): String {
    val bytes = this
    if (bytes < 1024) return "$bytes B"

    val exp = (ln(bytes.toDouble()) / ln(1024.0)).toInt()
    val prefix = "KMGTPE"[exp - 1] + "i" // e.g. Ki, Mi, Gi, etc.
    val value = bytes / 1024.0.pow(exp.toDouble())

    return String.format("%.1f %sB", value, prefix)
}

data class RepoUploadRequest(
    val repositoryId: String,
    val requestId: String,
    val zipFile: File,
    val diff: List<RepoDiff>,
    val rebuild: Boolean
)

data class ActiveIndexingTasksRequest(
    val repositoryId: String,
    val requestId: String
)
