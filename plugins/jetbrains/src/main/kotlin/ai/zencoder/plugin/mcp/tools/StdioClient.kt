package ai.zencoder.plugin.mcp.tools

import ai.zencoder.plugin.utils.Os
import ai.zencoder.plugin.utils.getOs
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.openapi.diagnostic.thisLogger
import kotlinx.coroutines.*
import kotlinx.io.*
import java.io.File

/**
 * StdioClient implements the ToolClient interface to provide communication with external processes
 * via standard input/output streams. This class manages the lifecycle of an external process,
 * including creation, communication, and termination.
 *
 * The client creates a subprocess with the specified command and working directory, then provides
 * access to the process's input and output streams for bidirectional communication.
 *
 * @property workDirectory The directory in which the process will be executed
 * @property command A list of strings representing the command to execute (first element is the executable,
 *                  remaining elements are arguments)
 * @property environment Optional map of environment variables to set for the process
 */
class StdioClient(
    private val workDirectory: File,
    private val command: List<String>,
    private val environment: Map<String, String> = emptyMap()
) : ToolClient {
    // Process management fields
    /** The underlying process instance */
    private var process: Process? = null

    /** Input stream from the process (process stdout -> our input) */
    private var input: Source? = null

    /** Output stream to the process (our output -> process stdin) */
    private var output: Sink? = null

    /**
     * Establishes a connection to the external process and returns streams for communication.
     *
     * This method:
     * 1. Creates a new process using the configured command and working directory
     * 2. Sets up error handling for the coroutine context
     * 3. Configures platform-specific command execution (Windows vs. other platforms)
     * 4. Verifies the process started successfully
     * 5. Returns a pair of Source and Sink for bidirectional communication with the process
     *
     * @return A Pair containing:
     *         - Source: For reading data from the process (process stdout)
     *         - Sink: For writing data to the process (process stdin)
     * @throws TimeoutCancellationException if process creation exceeds the timeout (10 seconds)
     * @throws IllegalStateException if the process exits immediately after creation
     * @throws Exception for other process creation failures
     */
    override suspend fun connect(): Pair<Source, Sink> {
        // Set up exception handling for the coroutine
        val coroutineHandler = CoroutineExceptionHandler { _, exception ->
            thisLogger().warn("Error in StdioClient: ${exception.message}")
            thisLogger().debug(exception)
            disconnect()
        }
        // Create a named coroutine for better debugging and monitoring
        val coroutineName = CoroutineName("zencoder-stdio-client@${command.joinToString(separator = "_")}")

        try {
            // Create the process with a timeout to prevent hanging
            val localProcess = withContext(Dispatchers.IO + coroutineHandler + coroutineName) {
                // Handle platform-specific command execution
                when (getOs()) {
                    Os.WINDOWS -> {
                        // On Windows, use powershell.exe with appropriate flags
                        GeneralCommandLine("powershell.exe")
                            .withParameters("-NonInteractive", "-Command", command.joinToString(" "))
                    }

                    else -> {
                        // On Unix-like systems, execute the command directly
                        GeneralCommandLine(command[0])
                            .withParameters(command.drop(1))
                    }
                }
                    .withWorkDirectory(workDirectory)
                    .withEnvironment(environment)
                    .createProcess()
            }

            // Verify the process is still running after creation
            if (!localProcess.isAlive) {
                val exitCode = localProcess.exitValue()
                val errorMessage = "Process exited immediately with code $exitCode"
                thisLogger().warn(errorMessage)

                // Attempt to capture any error output for diagnostic purposes
                var errorOutput = ""
                try {
                    errorOutput = localProcess.inputStream.bufferedReader().readText()
                    if (errorOutput.isNotEmpty()) {
                        thisLogger().warn("Process error output: $errorOutput")
                    }
                } catch (e: Exception) {
                    thisLogger().warn("Could not read process error output: ${e.message}")
                }

                throw Error(errorMessage, Error(errorOutput))
            }

            // Store the process reference for later management
            process = localProcess

            // Return buffered streams for efficient I/O with the process
            // Note: Process.inputStream is the OUTPUT from the process (we read from it)
            //       Process.outputStream is the INPUT to the process (we write to it)
            return localProcess.inputStream.asSource().buffered() to localProcess.outputStream.asSink().buffered()
        } catch (e: Throwable) {
            thisLogger().warn("Failed to create process: ${e.message}")
            disconnect()
            throw e
        }
    }

    /**
     * Terminates the connection to the external process and cleans up resources.
     *
     * This method:
     * 1. Closes I/O streams to prevent resource leaks
     * 2. Attempts to gracefully terminate the process
     * 3. Forcibly terminates the process if it doesn't respond to the graceful termination
     * 4. Clears all references to the process and streams
     *
     * The method is designed to be safe to call multiple times and in any state,
     * including after failed connection attempts.
     */
    override fun disconnect() {
        try {
            // Close I/O streams first to signal EOF to the process
            output?.close()
            input?.close()

            // Terminate the process if it exists
            process?.let {
                // Try graceful termination first
                it.destroy()
                // If process is still running, force termination
                if (it.isAlive) {
                    it.destroyForcibly()
                }
            }
        } catch (e: Exception) {
            thisLogger().warn("Error during disconnect: ${e.message}")
        } finally {
            // Clear all references to allow garbage collection
            process = null
            input = null
            output = null
        }
    }
}
