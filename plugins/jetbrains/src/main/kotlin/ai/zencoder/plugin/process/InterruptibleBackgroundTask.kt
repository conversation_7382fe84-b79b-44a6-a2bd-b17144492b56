package ai.zencoder.plugin.process

import ai.zencoder.plugin.log.error
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.Task
import org.awaitility.Awaitility
import org.awaitility.core.ConditionTimeoutException
import java.time.Duration
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

@Deprecated("For remove")
abstract class InterruptibleBackgroundTask(
    taskName: String,
    private val context: ProcessContext? = null
) : Task.Backgroundable(null, taskName, true) {
    private val threadPoolExecutor = Executors.newSingleThreadExecutor()

    @Volatile
    private var internallyCancelled = false

    @Volatile
    private var allProcessesAreComplete = false

    override fun run(indicator: ProgressIndicator) {
        if (!interruptionCondition()) {
            prepare(indicator)
            threadPoolExecutor.submit {
                try {
                    process().run()
                } catch (t: Throwable) {
                    thisLogger().error(context, t)
                    internallyCancelled = true
                }
            }
        }
        try {
            Awaitility
                .await()
                .atMost(timeoutDuration())
                .pollInterval(2L, TimeUnit.SECONDS)
                .until { successCondition() || interruptionCondition() || internallyCancelled }
        } catch (timeoutException: ConditionTimeoutException) {
            thisLogger().warn(timeoutException)
            threadPoolExecutor.shutdownNow()
            onTimeout()
            return
        }
        try {
            if (successCondition()) {
                complete()
                indicator.stop()
            }
            if (interruptionCondition() || internallyCancelled) {
                onInterrupted()
                indicator.cancel()
            }
        } catch (e: Exception) {
            thisLogger().error(e)
        } finally {
            threadPoolExecutor.shutdownNow()
            if (indicator.isRunning) {
                indicator.stop()
            }
            finally()
            allProcessesAreComplete = true
        }
    }

    override fun onCancel() {
        internallyCancelled = true
    }

    abstract fun interruptionCondition(): Boolean

    abstract fun successCondition(): Boolean

    abstract fun timeoutDuration(): Duration

    abstract fun onTimeout()

    abstract fun onInterrupted()

    abstract fun process(): Runnable

    open fun complete() {}

    open fun prepare(indicator: ProgressIndicator) {}

    open fun finally() {}
}
