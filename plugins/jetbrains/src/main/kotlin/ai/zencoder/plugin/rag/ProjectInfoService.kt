package ai.zencoder.plugin.rag

import ai.zencoder.generated.client.all.models.ProjectInfoWorkflowServiceProjectInfoRequest
import ai.zencoder.plugin.api.ProjectInfoApiService
import ai.zencoder.plugin.auth.AuthService
import ai.zencoder.plugin.context.AgentChatContextService
import ai.zencoder.plugin.observers.graphql.AgentCommunicationsService
import ai.zencoder.plugin.socket.ably.AblyConnectionManager
import ai.zencoder.plugin.utils.showNotification
import ai.zencoder.plugin.utils.uuid
import ai.zencoder.plugin.webview.chat.ChatManager
import ai.zencoder.plugin.webview.chat.ChatWebviewManager
import ai.zencoder.plugin.webview.model.ChatSettings
import ai.zencoder.plugin.webview.model.JsonContent
import ai.zencoder.plugin.webview.model.JsonContent.Type
import ai.zencoder.plugin.webview.model.MessagePart
import ai.zencoder.plugin.webview.model.PostedMessage.SetActiveChat
import ai.zencoder.plugin.webview.model.UserMessage
import com.intellij.notification.NotificationType
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.project.Project
import io.sentry.Sentry
import io.sentry.SpanStatus
import io.sentry.TransactionOptions
import io.sentry.kotlin.SentryContext
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

const val AGENT_NAME = "Project Info Agent"

@Service(Level.PROJECT)
class ProjectInfoService(
    private val project: Project,
    private val cs: CoroutineScope
) {
    private val logger = thisLogger()

    private val ragStateManager get() = project.service<RagStateManager>()
    private val ragSettingsManager get() = project.service<RagSettingsManager>()
    private val ablyConnectionManager get() = service<AblyConnectionManager>()
    private val projectInfoApiService get() = service<ProjectInfoApiService>()
    private val chatManager get() = project.service<ChatManager>()
    private val chatWebview get() = project.service<ChatWebviewManager>()

    fun checkIfNeedToUpdateProjectInfo() {
        val totalFiles = ragStateManager.state.repoTotalFiles
        val accumulatedChanges = ragStateManager.state.repoAccumulatedChanges
        val threshold =
            (totalFiles.toDouble() / 100) * ragSettingsManager.state.projectInfoUpdateThresholdPercentage.toDouble()

        logger.info(
            "Checking if project info update is needed: totalFiles=$totalFiles, accumulatedChanges=$accumulatedChanges, threshold=${threshold.toInt()}"
        )

        if (accumulatedChanges >= threshold.toInt()) {
            logger.info("Threshold exceeded ($accumulatedChanges > ${threshold.toInt()}), triggering project info collection")
            ragStateManager.state.repoAccumulatedChanges = 0
            collectProjectInfo()
        } else {
            logger.debug("Threshold not exceeded ($accumulatedChanges <= ${threshold.toInt()}), skipping project info collection")
        }
    }

    fun collectProjectInfo() {
        logger.info("Starting project info collection for project: ${project.name}")

        val span = Sentry.startTransaction(
            "jetbrains.project-info.v1",
            "project-info",
            TransactionOptions().apply { isBindToScope = true }
        )
        logger.debug("Created Sentry transaction: ${span.name}")

        val job = cs.launch(
            Dispatchers.IO + SentryContext() + CoroutineExceptionHandler { _, exception ->
                logger.error("Error during project info collection", exception)
                span.throwable = exception
            }
        ) {
            try {
                val operationId = uuid()
                val requestId = uuid()
                logger.info("Generated IDs for operation: operationId=$operationId, requestId=$requestId")

                val prompt = "Build project info"
                val message = UserMessage(
                    rawContent = JsonContent(type = Type.TEXT, text = prompt),
                    content = listOf(MessagePart.Text(prompt)),
                    context = null,
                    chatSettings = ChatSettings(isCustomAgent = true, selfDestruct = true)
                )

                logger.debug("Starting new chat with prompt: '$prompt'")
                val chat = chatManager.startNewChat(message)
                logger.debug("Chat created with ID: ${chat.id}")

                chatManager.startOperation(operationId, chat.id)
                logger.debug("Operation started: operationId=$operationId, chatId=${chat.id}")

                chatManager.addMessageToChat(chat, chatManager.constructUserMessage(message, true), { curChat, isDisabled ->
                    logger.debug("Sending SetActiveChat: chatId=${curChat.id}, isStreaming=$isDisabled")
                    chatWebview.send(
                        SetActiveChat(
                            chat = curChat,
                            isStreaming = isDisabled,
                            navigateTo = null
                        )
                    )
                })

                logger.debug("Collecting context for operation: $operationId")
                val context = project.service<AgentChatContextService>().collectContextGraphQL(operationId)

                logger.debug("Registering operation with AgentCommunicationsService")
                project.service<AgentCommunicationsService>().registerOperation(operationId, AGENT_NAME, context)

                logger.info("Calling projectInfoApiService.collectProjectInfo")
                projectInfoApiService.collectProjectInfo(
                    ProjectInfoWorkflowServiceProjectInfoRequest(
                        ideId = ablyConnectionManager.getOperationSessionChannelName(project),
                        operationId = operationId,
                        requestId = requestId
                    )
                )
                logger.info("Project info collection request sent successfully")

                if (devMode) {
                    val userId = service<AuthService>().authInfoOrNull()?.userData?.id
                    logger.debug("Dev mode enabled, showing notification with userId=$userId")
                    showNotification(
                        """
                            userId: ${monospaced(userId)}<br/>
                            operationId: ${monospaced(operationId)}<br/>
                            requestId: ${monospaced(requestId)}<br/>

                            Project Info Agent: triggered successfully!<br/>
                        """.trimIndent(),
                        NotificationType.INFORMATION
                    )
                }
            } catch (e: Exception) {
                logger.error("Unexpected error during project info collection", e)
                span.throwable = e
            }
        }

        job.invokeOnCompletion { throwable ->
            val status = when (throwable) {
                is Throwable -> {
                    logger.warn("Project info collection completed with error", throwable)
                    SpanStatus.INTERNAL_ERROR
                }
                else -> {
                    logger.info("Project info collection completed successfully")
                    SpanStatus.OK
                }
            }
            span.finish(status)
        }
    }
}
