@file:OptIn(ExperimentalSerializationApi::class)

package ai.zencoder.plugin.mcp

import ai.zencoder.plugin.mcp.model.ToolStatus
import ai.zencoder.plugin.mcp.service.McpMessageBusServerService
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.Service.Level.PROJECT
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.serialization.ExperimentalSerializationApi
import java.util.concurrent.ConcurrentHashMap

private val logger = KotlinLogging.logger {}

/**
 * Manager for MCP tools library.
 * Handles fetching, installing, and uninstalling MCP tools.
 */
@Service(PROJECT)
class MCPLibraryManager(private val project: Project) {
    private val toolsState: ConcurrentHashMap<String, McpServerInfo> = ConcurrentHashMap()
    private val stateManager get() = project.service<MCPServersStateManager>()
    private val clientsStateManager get() = project.service<MCPClientsStateManager>()

    /**
     * Fetches the current state of agent tools, including library tools, installed tools, and Zencoder backend tools.
     */
    suspend fun getAgentToolsState(): Map<String, McpServerInfo> {
        val zencoderServers = getZencoderTools()

        // Get cached or fetch library tools
        val library = stateManager.getLibraryTools(project)

        // Get cached or fetch Zencoder backend tools
        val zencoderBackendTools = stateManager.getZencoderBackendTools(project)

        logger.info { "Getting agent tools state" }

        if (toolsState.isEmpty()) {
            // Library servers
            for ((serverId, serverInfo) in library) {
                toolsState[serverId] = serverInfo
            }

            // Installed servers
            val serverConfigs = stateManager.getAllServerConfigs()
            for (server in serverConfigs.keys) {
                // If the server is already in the state (e.g., from library), update its status
                if (toolsState.containsKey(server)) {
                    val existingInfo = toolsState[server]!!
                    toolsState[server] = existingInfo.copy(
                        status = McpServerStatus(
                            status = ToolStatus.INSTALLED,
                            config = serverConfigs[server]
                        )
                    )
                } else {
                    // Otherwise, create a new entry
                    toolsState[server] = McpServerInfo(
                        id = server, // Set ID field to match the key
                        name = server.replace("_", " "),
                        status = McpServerStatus(
                            status = ToolStatus.INSTALLED,
                            config = serverConfigs[server]
                        )
                    )
                }
            }

            // Internal servers
            for ((serverId, serverInfo) in zencoderServers) {
                // Don't overwrite installed servers
                if (!toolsState.containsKey(serverId) || toolsState[serverId]?.status?.status != ToolStatus.INSTALLED) {
                    toolsState[serverId] = serverInfo
                }
            }

            // Zencoder backend tools
            for ((serverId, serverInfo) in zencoderBackendTools) {
                // Don't overwrite installed servers
                if (!toolsState.containsKey(serverId) || toolsState[serverId]?.status?.status != ToolStatus.INSTALLED) {
                    toolsState[serverId] = serverInfo
                }
            }
        }

        return toolsState
    }

    /**
     * Installs an agent tool.
     *
     * This method handles the installation of both internal tools and custom tools.
     * For internal tools, it simply marks them as installed in the state.
     * For custom tools, it tests the tool by starting it, then updates the state.
     *
     * @param toolId The unique identifier of the tool to install
     * @param toolName The display name of the tool
     * @param config The configuration for the tool
     */
    suspend fun installAgentTool(
        toolId: String,
        toolName: String,
        config: ServerConfig
    ) {
        logger.info { "Installing agent tool $toolId" }

        val zencoderServers = getZencoderTools()

        // Check if it's a Zencoder backend tool using the cached data
        val isZencoderBackendTool = stateManager.isZencoderBackendTool(toolId)

        if (zencoderServers.containsKey(toolId) || isZencoderBackendTool) {
            // Handle internal or backend server installation
            stateManager.setInternalToolState(toolId, true, project.name)

            // Update tool state in the UI
            if (toolsState.containsKey(toolId)) {
                toolsState[toolId]?.status = McpServerStatus(
                    status = ToolStatus.INSTALLED
                )
            }
            return
        }

        if (!toolsState.containsKey(toolId)) {
            // New user server
            toolsState[toolId] = McpServerInfo(
                name = toolName,
                status = McpServerStatus(
                    status = ToolStatus.LIBRARY,
                    config = config
                )
            )
        }
        val client = when (config) {
            is StdioServerConfig -> clientsStateManager.createStdioClient(null, toolId, config)
            is SseServerConfig -> clientsStateManager.createSseClient(null, toolId, config)
            else -> throw IllegalArgumentException("Unsupported server type: ${config.javaClass.simpleName}")
        }
        client?.ping() ?: throw IllegalStateException("Unable to install agent tool $toolId")

        // Shutdown all running servers before updating configuration
        clientsStateManager.shutdownAllServers()

        // Update tool state
        toolsState[toolId]?.status = McpServerStatus(
            status = ToolStatus.INSTALLED,
            config = config
        )

        // Save the config to disk
        stateManager.setServerConfig(toolId, config, project.name)
    }

    /**
     * Uninstalls an agent tool.
     */
    suspend fun uninstallAgentTool(toolId: String) {
        logger.info { "Uninstalling agent tool $toolId" }

        val zencoderServers = getZencoderTools()

        // Check if it's a Zencoder backend tool using the cached data
        val isZencoderBackendTool = stateManager.isZencoderBackendTool(toolId)

        if (zencoderServers.containsKey(toolId) || isZencoderBackendTool) {
            // Handle internal or backend server uninstallation
            stateManager.setInternalToolState(toolId, false, project.name)

            // Update tool state in the UI
            if (toolsState.containsKey(toolId)) {
                toolsState[toolId]?.status = McpServerStatus(
                    status = ToolStatus.LIBRARY
                )
            }

            // Shutdown all running servers to reflect the configuration change
            clientsStateManager.shutdownAllServers()
            return
        }

        // Shutdown all running servers before updating configuration
        clientsStateManager.shutdownAllServers()

        // Remove from configuration
        stateManager.removeServerConfig(toolId)

        // Update tool state
        if (toolsState.containsKey(toolId)) {
            toolsState[toolId]?.status = McpServerStatus(
                status = ToolStatus.LIBRARY
            )
        }
    }

    fun isToolInstalled(toolId: String): Boolean {
        val tool = toolsState[toolId]
        return tool?.status?.status == ToolStatus.INSTALLED
    }

    /**
     * Gets the Zencoder internal tools from the InternalToolsRegistry.
     * This method retrieves all registered internal tools and maps them to their status.
     */
    private suspend fun getZencoderTools(): Map<String, McpServerInfo> {
        val mcpMessageBus = project.service<McpMessageBusServerService>()
        val internalToolsRegistry = project.service<InternalToolsRegistry>()
        val registeredTools = internalToolsRegistry.getRegisteredTools()

        mcpMessageBus.job.join()

        return registeredTools.mapValues { (toolId, serverInfo) ->
            // Check if the tool is enabled in the internal tools state
            val isEnabled = stateManager.getInternalToolState(toolId)
            // Create `McpServerInfo` object copy with correct status
            serverInfo.copy(
                status = serverInfo.status.copy(
                    status = if (isEnabled) ToolStatus.INSTALLED else ToolStatus.LIBRARY
                )
            )
        }
    }
}
