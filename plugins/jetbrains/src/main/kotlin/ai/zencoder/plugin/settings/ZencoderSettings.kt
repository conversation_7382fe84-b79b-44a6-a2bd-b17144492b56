package ai.zencoder.plugin.settings

import ai.zencoder.plugin.rag.RagFilesFilteringHelper
import ai.zencoder.plugin.utils.ZencoderApp
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.components.service
import com.intellij.util.application
import com.intellij.util.xmlb.XmlSerializerUtil
import com.intellij.util.xmlb.annotations.OptionTag
import com.intellij.util.xmlb.annotations.Transient

val zencoderSettings get() = service<ZencoderSettings>()

private const val AUTH_BASE_URL = "https://auth.zencoder.ai"
private const val API_BASE_URL = "https://api.zencoder.ai"
private const val SIGNIN_URL = "https://auth.zencoder.ai"
private const val SIGNUP_URL = "https://auth.zencoder.ai/signup"
private const val AGENTS_SERVICE_URL = "https://api.zencoder.ai"
private const val CODEGEN_SERVICE_URL = "https://api.zencoder.ai/codeedit"
private const val CODE_REPAIR_SERVICE_URL = "https://api.zencoder.ai/repair"
private const val UNIT_TESTS_SERVICE_URL = "https://api.zencoder.ai/unittests"
private const val FEEDBACK_SERVICE_URL = "https://api.zencoder.ai/feedback-service"
private const val CHAT_SERVICE_URL = "https://api.zencoder.ai/chat"
private const val CLOUD_STORAGE_URL = "https://storage.googleapis.com/zencoder-public"
private const val REPO_UPLOAD_SERVICE_URL = "https://api.zencoder.ai/repo-upload-service"
private const val CODE_COMPLETION_SERVICE_URL = "https://api.zencoder.ai/code-completion-service"
private const val ABLY_AUTH_URL = "https://api.zencoder.ai/transport-bus/auth_callback"
private const val WORKFLOW_SERVICE_URL = "https://api.zencoder.ai"
private const val DEFAULT_SERVICE_URL = "https://api.zencoder.ai"
private val DEFAULT_ALLOWED_COMMANDS = setOf(
    "whoami",
    "find",
    "sort",
    "cd",
    "echo",
    "ls",
    "pwd",
    "cat",
    "head",
    "tail",
    "uname",
    "id",
    "env",
    "printenv",
    "df",
    "free",
    "ps",
    "grep",
    "uniq",
    "wc",
    "diff",
    "dir",
    "tree",
    "chdir",
    "type",
    "help",
    "ver",
    "systeminfo",
    "ipconfig",
    "tasklist",
    "hostname",
    "netstat",
    "which",
    "awk",
    "git status",
    "git diff",
    "git log",
    "git branch"
)

@State(
    name = "org.intellij.sdk.settings.AppSettingsState",
    storages = [Storage("zencoder-settings.xml")],
    reloadable = true
)
class ZencoderSettings : PersistentStateComponent<ZencoderSettings> {
    var isOwnApiKeyEnabled = false
    var chatLlmChoice = ChatLLMChoice.default()

    var version = 0

    var authBaseUrl = AUTH_BASE_URL
        get() = chooseByEnv(field, AUTH_BASE_URL)

    var apiBaseUrl = API_BASE_URL
        get() = chooseByEnv(field, API_BASE_URL)

    var signInUrl = SIGNIN_URL
        get() = chooseByEnv(field, SIGNIN_URL)

    var signUpUrl = SIGNUP_URL
        get() = chooseByEnv(field, SIGNUP_URL)

    val managePlanUrl
        get() = "$authBaseUrl/portal/manage-subscription"

    var agentsServiceUrl = AGENTS_SERVICE_URL
        get() = chooseByEnv(field, AGENTS_SERVICE_URL)
        set(value) {
            if (field != value) {
                field = value
                application.messageBus
                    .syncPublisher(ZencoderSettingsEvents.AGENTS_URL_SETTINGS_CHANGED)
                    .agentsServiceUrlChanged()
            }
        }

    var codeGenServiceUrl = CODEGEN_SERVICE_URL
        get() = chooseByEnv(field, CODEGEN_SERVICE_URL)
        set(value) {
            if (field != value) {
                field = value
                application.messageBus
                    .syncPublisher(ZencoderSettingsEvents.CODE_GEN_SERVICE_URL_CHANGED)
                    .codeGenServiceUrlChanged()
            }
        }

    var codeRepairServiceUrl = CODE_REPAIR_SERVICE_URL
        get() = chooseByEnv(field, CODE_REPAIR_SERVICE_URL)

    var unitTestsServiceUrl = UNIT_TESTS_SERVICE_URL
        get() = chooseByEnv(field, UNIT_TESTS_SERVICE_URL)

    var feedbackServiceUrl = FEEDBACK_SERVICE_URL
        get() = chooseByEnv(field, FEEDBACK_SERVICE_URL)
        set(value) {
            if (field != value) {
                field = value
                application.messageBus
                    .syncPublisher(ZencoderSettingsEvents.FEEDBACK_SERVICE_URL_CHANGED)
                    .feedbackServiceUrlChanged()
            }
        }

    var chatServiceUrl = CHAT_SERVICE_URL
        get() = chooseByEnv(field, CHAT_SERVICE_URL)
        set(value) {
            if (field != value) {
                field = value
                application.messageBus
                    .syncPublisher(ZencoderSettingsEvents.CHAT_SERVICE_URL_CHANGED)
                    .chatServiceUrlChanged()
            }
        }

    var cloudStorageUrl = CLOUD_STORAGE_URL
        get() = chooseByEnv(field, CLOUD_STORAGE_URL)

    var repoUploadServiceUrl = REPO_UPLOAD_SERVICE_URL
        get() = chooseByEnv(field, REPO_UPLOAD_SERVICE_URL)

    var codeCompletionServiceUrl = CODE_COMPLETION_SERVICE_URL
        get() = chooseByEnv(field, CODE_COMPLETION_SERVICE_URL)
        set(value) {
            if (field != value) {
                field = value
                application.messageBus
                    .syncPublisher(ZencoderSettingsEvents.CODE_COMPLETION_SETTINGS_CHANGED)
                    .codeCompletionUrlChanged()
            }
        }

    var ablyAuthUrl = ABLY_AUTH_URL
        get() = chooseByEnv(field, ABLY_AUTH_URL)

    var workflowServiceUrl = WORKFLOW_SERVICE_URL
        get() = chooseByEnv(field, WORKFLOW_SERVICE_URL)

    var defaultServiceUrl = DEFAULT_SERVICE_URL
        get() = chooseByEnv(field, DEFAULT_SERVICE_URL)

    var codebaseIndexing: Boolean = true
    var excludeByGitignore: Boolean = true
    var excludeCustomFilesPatterns: Set<String> = RagFilesFilteringHelper.defaultExclusionPatterns

    @get:Transient
    val isRagDisabled get() = !codebaseIndexing

    var enableInlineCodeCompletion: Boolean = true
    var enableMultiLineCompletion: Boolean = true
    var enableNextEditPrediction: Boolean = true
    var isInlineSuggestionsEnabled = true
    var codeCompletionDebounceDelay: Int = 400

    var isSentryEnabled = false
    var isAnalyticsEnabled = false

    @OptionTag("coffeeModeConfirmationPolicy")
    var shellToolConfirmationPolicy = CommandConfirmationPolicy.AS_CONFIGURED

    @OptionTag("coffeeModeAllowedCommands")
    var shellToolAllowedCommands = DEFAULT_ALLOWED_COMMANDS

    var displayDebugInfo: Boolean = false

    var enableShellTool: Boolean = true
    var isRequirementsToolEnabled: Boolean = true

    fun addAllowedShellToolCommand(command: String) {
        val shellToolAllowedCommands = this.shellToolAllowedCommands.toMutableSet()
        shellToolAllowedCommands.add(command)
        this.shellToolAllowedCommands = shellToolAllowedCommands
    }

    override fun getState(): ZencoderSettings {
        return this
    }

    override fun loadState(state: ZencoderSettings) {
        XmlSerializerUtil.copyBean(state, this)

        migrateIfNeeded()
    }

    private fun chooseByEnv(savedToSettings: String, default: String) = if (ZencoderApp.isDevMode) savedToSettings else default

    /**
     * Checks and performs necessary migrations based on the current version of the settings.
     *
     * This method is invoked during the loading of the settings state to ensure that any
     * outdated configurations are updated to the latest format.
     *
     * The migration process involves:
     * - Evaluating the current version of the settings.
     * - If the version is 0, it triggers the `migrateFromVersion0` method to update the settings.
     * - Future versions can be handled by adding additional cases in the `when` statement.
     *
     * This mechanism ensures that the settings remain compatible with the latest application
     * requirements without manual intervention from the user.
     */
    private fun migrateIfNeeded() {
        when (this.version) {
            0 -> migrateFromVersion0()
            // Future migrations for other versions
        }
    }

    /**
     * Migrates configuration settings from version 0 to version 1.
     *
     * Updates the `defaultServiceUrl` based on the majority of service URLs being in production or development.
     * Sets the state version to 1 after migration.
     */
    private fun migrateFromVersion0() {
        val (prod, dev) = listOf(
            codeGenServiceUrl,
            codeRepairServiceUrl,
            unitTestsServiceUrl,
            feedbackServiceUrl,
            chatServiceUrl,
            repoUploadServiceUrl,
            codeCompletionServiceUrl,
            ablyAuthUrl,
            workflowServiceUrl
        ).map { it.startsWith("https://api") }
            .fold(0 to 0) { acc, b -> if (b) acc.first + 1 to acc.second else acc.first to acc.second + 1 }

        if (dev > prod) {
            defaultServiceUrl = defaultServiceUrl.replace("https://api", "https://dev.api")
        }
        if (dev < prod) {
            defaultServiceUrl = defaultServiceUrl.replace("https://dev.api", "https://api")
        }

        state.version = 1
    }
}
