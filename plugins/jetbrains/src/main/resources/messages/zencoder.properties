sentry.report.actionText=Report to Zencoder
sentry.report.privacyNoticeText=I agree to my hardware configuration, software configuration, product information, and the error details shown above \
  being used by Zencoder (For Good AI Inc.) to let Zencoder improve its product and to provide me with support service \
  in accordance with the <a href="https://zencoder.ai/terms-of-service">Terms of Service</a> and <a href="https://zencoder.ai/privacy-policy">Privacy Policy</a>.
project.label=Zencoder
notification.group.name=Zencoder notification group
dialog.deleteChatSession.title=Delete chat session
dialog.deleteChatSession.description=Are you sure you want to delete this chat session?
toolwindow.chat.settings.settings=Settings
toolwindow.chat.settings.shortcuts=Shortcuts
toolwindow.chat.settings.repoGrokking=Repo Grokking
toolwindow.chat.settings.modelsSettings=Models
toolwindow.chat.settings.instructions=Instructions for AI
toolwindow.chat.settings.memories=Memories
toolwindow.chat.settings.customAgents=Agents
toolwindow.chat.settings.toolsRegistry=Agent Tools
toolwindow.chat.settings.integrations=Integrations
toolwindow.tab.chat=Chat
toolwindow.chat.settings.button.startIndexing=Start indexing
toolwindow.chat.settings.button.reindex=Reindex
toolwindow.chat.editor.action.copy.title=Copy
toolwindow.chat.editor.action.copy.description=Copy generated code
toolwindow.chat.editor.action.copy.success=Code copied!
toolwindow.chat.editor.action.expand=Show More (+%s rows)
toolwindow.chat.editor.action.collapse=Show Less
toolwindow.chat.textArea.emptyText=New message
toolwindow.chat.editor.action.placeIntoEditor.title=Place into editor
toolwindow.chat.editor.action.placeIntoEditor.description=Place code into editor
toolwindow.chat.editor.action.warning.unableToLocate=Unable to locate a selected editor
toolwindow.chat.editor.action.warning.putCursor=Please put the cursor into editor
toolwindow.unit.tests.test.plan.text=Test Plan
toolwindow.unit.tests.test.plan.description=Show test plan
shared.escToCancel=Esc to cancel
notification.title=Zencoder
notification.repo.indexing.enabled=Repo indexing has been enabled
notification.repo.indexing.concurrentLimitReached=You've reached limit for simultaneous indexing Please wait until your other repositories finish indexing before indexing this one.
unit.tests.editor.action.accept=Accept
unit.tests.editor.action.decline=Decline
unit.tests.panel.name=Zencoder Tests
unit.tests.default.panel.header.label=Unit test generation
unit.tests.default.panel.description=<html>Zencoder takes from you routine in writing test<br>for functions and give more time for coding</html>
unit.tests.default.panel.explanation.label=How it works
unit.tests.default.panel.explanation.step.selection=<html>1. Select Zencoder Unit tests from right-click menu<br>   or using Code Lens</html>
unit.tests.default.panel.explanation.step.test.scenarios.review=2. Review test scenarios
unit.tests.default.panel.explanation.step.generate=3. Generate tests
notification.chat.context.attachedFileIsOutsideOfProject=Currently, only workspace files are supported
notification.chat.context.attachedFileIsBinary=Currently, binary files are not supported
settings.repoGrokking.title=Repo Grokking
settings.repoGrokking.codebaseIndexing=Codebase Indexing
settings.repoGrokking.excludeByGitignore=Exclude files listed in .gitignore
settings.repoGrokking.excludeCustomFiles=Exclude custom files from indexing by file patterns (ant file patterns, e.g. 'secret/**/*.txt' or '**/*.kt'):
settings.repoGrokking.excludeCustomFiles.onAddMessage="Add pattern:"
settings.editor.enableInlineSuggestions=Enable Inline Suggestions
notification.exceptions.rateLimitReached=Rate limit exceeded. You can try again on %s.
notification.repoUploading=Repo uploading
notification.repoIndexing=Repo indexing
notification.repoGrokking.indexingFailed=<html>Repository indexing failed: <br />%s</html>
notification.repoGrokking.openSettings=Open indexing settings
chat.loading.step.preparingContext=Retrieving relevant context
chat.loading.step.contextPrepared=Analyzing and refining the context
chat.loading.step.streaming=Preparing response
chat.loading.step.initialization=Initializing agent
chat.request.error.message=The chat request was not successful. Please try again later.
chat.request.delayed.message=Daily limits reached. Requests are slower now.
chat.request.delayed.upgradePlan=Upgrade
chat.request.error.invalidModelsSettings=Invalid Models Settings
chat.request.error.openModelsSettings=Open Models Settings
panel.jcef.bootRuntime.required=Zencoder requires JCEF to work
notification.jcef.bootRuntime.notBundledMsg=JCEF is not bundled in the current Boot Runtime
notification.jcef.bootRuntime.choose=Choose Boot Runtime with JCEF
unified.diff.tool.originalContentTitle=Current Version
unified.diff.tool.originalContentForRevertTitle=Original Version
unified.diff.tool.generatedContentTitle=Generated Version
unified.diff.tool.popupActions.revert=Revert
settings.chat.section.title=Chat Assistant
settings.chat.llmChoice.comment=Select which LLM Zencoder should use in Chat. By default, Zencoder uses combination of different LLMs for optimal performance and quality
settings.chat.llmChoice.ownApiKey=Use my own API key
settings.chat.llmChoice.label=Model to use in chat
settings.chat.llmChoice.openai.label=OpenAI
settings.chat.llmChoice.anthropic.label=Anthropic
settings.chat.llmChoice.apiKey.comment=Enable to always use your API key for the selected model (not applicable for coding agent and Zencoder custom model)
settings.advanced.title=Advanced Settings
settings.advanced.debug.info.title=Show debug info
settings.advanced.debug.info.description=Controls whether debug information is displayed in the interface.
settings.codeCompletion.debounce.label=Min code completion delay (ms)
settings.shellTool.title=Shell Tool
settings.shellTool.confirmationPolicy.label=Command confirmation policy
settings.shellTool.confirmationPolicy.comment=Whether agents should ask for user confirmation before executing commands
settings.shellTool.commandsList.comment=<html>List of commands that will be run without confirmation <br/>If <b>Command Confirmation Policy</b> is set to <b>Don't ask for commands below</b></html>
settings.shellTool.commandsList.addMessage=Add command
error.chat.update.required=You are using an outdated version of Zencoder. Please update to the latest version to continue working with Chat Assistant
error.chat.websocket.connection.failed=Could not establish websocket connection. Please try again later.
editor.inlay.tooltip=If you don't want to see such suggestions, disable them in the plugin settings
editor.diff.title=Zencoder Diff
editor.diff.local.content.title=Zencoder suggested code

# MCP Servers Configurable
mcp.servers.configurable.title=MCP Servers
mcp.servers.configurable.moved.title=MCP Servers configuration has been moved to the Zencoder View itself.
mcp.servers.configurable.moved.migration=All your existing configuration has been migrated to the new interface.
mcp.servers.configurable.moved.benefits=The new interface provides a more user-friendly way to manage your MCP servers with improved validation and a better user experience.
mcp.servers.configurable.button.open=Open Agent Tools

# MCP Tools Panel
mcp.tools.panel.action.add=Add Custom MCP
mcp.tools.panel.action.add.description=Add a new custom MCP server
mcp.tools.panel.action.refresh=Refresh
mcp.tools.panel.action.refresh.description=Refresh the tools list
mcp.tools.panel.tab.all=All
mcp.tools.panel.tab.zencoder=Zencoder
mcp.tools.panel.tab.custom=Custom
mcp.tools.panel.tab.library=MCP Library
mcp.tools.panel.header.title=Agent Tools
mcp.tools.panel.header.description=Seamlessly make official and community built MCP tools available to Zencoder agents.
mcp.tools.panel.empty.title=No available tools
mcp.tools.panel.empty.message=No active tools found
mcp.tools.panel.error.load=Failed to load tools: {0}
mcp.tools.panel.search.label=Search:
mcp.tools.panel.button.dismiss=Dismiss
mcp.tools.panel.status.installed=Installed
mcp.tools.panel.dialog.warning.community=This MCP server is community-built. Install and use responsibly.
mcp.tools.panel.dialog.config.title=MCP server config
mcp.tools.panel.dialog.config.description=Some properties in the config may need to be configured, depending on the MCP server.
mcp.tools.panel.dialog.install.method=Installation method
mcp.tools.panel.dialog.install.description=Ensure that the corresponding tool (like uv, npm, or docker) is installed for the selected method.
mcp.tools.panel.dialog.button.install=Install
mcp.tools.panel.dialog.button.uninstall=Uninstall
mcp.tools.panel.dialog.button.save=Save
mcp.tools.panel.dialog.button.installing=Installing...
mcp.tools.panel.dialog.error.json=Invalid JSON configuration: {0}
mcp.tools.panel.dialog.error.title=Configuration Error
mcp.tools.panel.dialog.error.operation=Error: {0}
mcp.tools.panel.dialog.error.operation.title=Operation Failed

update.dialog.message=Would you like to update Zencoder now?
update.dialog.title=Zencoder Update
update.dialog.update=Update
update.dialog.cancel=Cancel
